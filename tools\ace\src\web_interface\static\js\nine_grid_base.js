/**
 * Nine Grid 通用JavaScript函数库
 * 提供九宫格页面共用的基础功能
 */

// --- 通用提示框功能 ---

/**
 * 显示提示框
 * @param {HTMLElement} element - 触发元素
 * @param {string} text - 提示文本
 * @param {string} tooltipId - 提示框ID，默认为'node-tooltip'
 */
function showTooltip(element, text, tooltipId = 'node-tooltip') {
    const tooltip = document.getElementById(tooltipId);
    if (!tooltip) return;
    
    tooltip.textContent = text;
    tooltip.classList.add('show');
    
    const rect = element.getBoundingClientRect();
    const graphRect = element.parentElement.getBoundingClientRect();
    
    tooltip.style.left = (rect.left - graphRect.left + rect.width / 2) + 'px';
    tooltip.style.top = (rect.top - graphRect.top - 30) + 'px';
}

/**
 * 隐藏提示框
 * @param {string} tooltipId - 提示框ID，默认为'node-tooltip'
 */
function hideTooltip(tooltipId = 'node-tooltip') {
    const tooltip = document.getElementById(tooltipId);
    if (!tooltip) return;
    
    tooltip.classList.remove('show');
}

// --- 通用日志详情展开/收起功能 ---

/**
 * 切换日志详情展开/收起功能
 * @param {HTMLElement} arrowElement - 箭头元素
 * @param {string} detailType - 详情类型 ('ai-comm' 或 'py-ops')
 * @param {string} logId - 日志ID
 */
function toggleLogDetail(arrowElement, detailType, logId) {
    const logEntry = arrowElement.closest('.log-entry');
    const isExpanded = arrowElement.classList.contains('expanded');

    if (isExpanded) {
        // 收起详情
        arrowElement.classList.remove('expanded');
        const existingDetails = logEntry.querySelector(`.${detailType}-details`);
        if (existingDetails) {
            existingDetails.remove();
        }
    } else {
        // 展开详情
        arrowElement.classList.add('expanded');

        // 创建详情内容
        const detailsDiv = document.createElement('div');
        detailsDiv.className = `expanded-details ${detailType}-details`;

        const detailContent = getExpandedDetailContent(detailType, logId);
        detailsDiv.innerHTML = detailContent;

        // 插入到log-entry下方
        logEntry.appendChild(detailsDiv);
    }

    console.log(`切换${detailType}详情:`, logId, isExpanded ? '收起' : '展开');
}

/**
 * 获取展开的详情内容
 * @param {string} detailType - 详情类型
 * @param {string} logId - 日志ID
 * @returns {string} HTML内容
 */
function getExpandedDetailContent(detailType, logId) {
    const aiCommDetails = {
        'startup_check_ide': `
            <div class="detail-header">AI通讯详情</div>
            <div class="detail-item">请求: IDE AI连接状态检查</div>
            <div class="detail-item">响应时间: 23ms</div>
            <div class="detail-item">状态: 连接正常</div>
            <div class="detail-item">工具数量: 6个核心工具就绪</div>
        `,
        'startup_check_meeting': `
            <div class="detail-header">AI通讯详情</div>
            <div class="detail-item">请求: Meeting目录权限验证</div>
            <div class="detail-item">响应时间: 15ms</div>
            <div class="detail-item">状态: 权限正常</div>
            <div class="detail-item">目录: docs/features/.../meeting</div>
        `,
        'startup_check_algorithms': `
            <div class="detail-header">AI通讯详情</div>
            <div class="detail-item">请求: 算法库加载状态</div>
            <div class="detail-item">响应时间: 45ms</div>
            <div class="detail-item">状态: 加载完成</div>
            <div class="detail-item">算法数量: 12种逻辑分析方法</div>
        `
    };

    const pyOpsDetails = {
        'startup_check_ide': `
            <div class="detail-header">Python操作详情</div>
            <div class="detail-item">操作: 验证IDE连接</div>
            <div class="detail-item">执行时间: 12ms</div>
            <div class="detail-item">内存使用: 2.3MB</div>
            <div class="detail-item">CPU使用: 0.1%</div>
        `,
        'startup_check_meeting': `
            <div class="detail-header">Python操作详情</div>
            <div class="detail-item">操作: 目录权限检查</div>
            <div class="detail-item">执行时间: 8ms</div>
            <div class="detail-item">内存使用: 1.8MB</div>
            <div class="detail-item">CPU使用: 0.05%</div>
        `
    };

    if (detailType === 'ai-comm') {
        return aiCommDetails[logId] || '<div class="detail-item">暂无AI通讯详情</div>';
    } else if (detailType === 'py-ops') {
        return pyOpsDetails[logId] || '<div class="detail-item">暂无Python操作详情</div>';
    }

    return '<div class="detail-item">暂无详情信息</div>';
}

/**
 * 显示日志详细内容
 * @param {HTMLElement} logElement - 日志元素
 * @param {string} logId - 日志ID
 * @param {string} detailAreaId - 详细区域ID，默认为'detail-content'
 */
function showLogDetail(logElement, logId, detailAreaId = 'detail-content') {
    // 移除其他日志的选中状态
    document.querySelectorAll('.log-entry').forEach(entry => {
        entry.style.backgroundColor = 'transparent';
        entry.style.border = 'none';
    });

    // 高亮当前选中的日志
    logElement.style.backgroundColor = '#0078D4';
    logElement.style.border = '1px solid #0078D4';

    // 获取详细内容
    const detailContent = getLogDetailContent(logId);

    // 显示在详细区
    const detailArea = document.getElementById(detailAreaId);
    if (detailArea) {
        detailArea.innerHTML = `<div style="height: 100%; overflow-y: auto; padding: 0.3rem;">${detailContent}</div>`;
    }

    // 隐藏"详细"标识（因为现在有内容了）
    const detailTitle = document.getElementById('detail-title');
    if (detailTitle) {
        detailTitle.style.display = 'none';
    }

    // 滚动到顶部
    const detailAreaContainer = document.getElementById('detail-area');
    if (detailAreaContainer) {
        detailAreaContainer.scrollTop = 0;
    }

    console.log('显示日志详细:', logId);
}

/**
 * 获取日志详细内容
 * @param {string} logId - 日志ID
 * @returns {string} HTML内容
 */
function getLogDetailContent(logId) {
    const detailContents = {
        'startup_check_ide': `
            <div style="color: #4CAF50; font-weight: bold; margin-bottom: 0.5rem;">✅ IDE AI连接状态检查</div>
            <div style="margin-bottom: 0.3rem;"><strong>检查时间:</strong> 2025-01-31 14:17:30</div>
            <div style="margin-bottom: 0.3rem;"><strong>连接状态:</strong> 正常</div>
            <div style="margin-bottom: 0.3rem;"><strong>响应时间:</strong> 23ms</div>
            <div style="margin-bottom: 0.3rem;"><strong>可用工具:</strong> 6个核心工具就绪</div>
            <div style="margin-bottom: 0.3rem;"><strong>工具列表:</strong></div>
            <ul style="margin-left: 1rem; margin-bottom: 0.5rem;">
                <li>codebase-retrieval: 代码库检索</li>
                <li>str-replace-editor: 文件编辑</li>
                <li>view: 文件查看</li>
                <li>save-file: 文件保存</li>
                <li>launch-process: 进程启动</li>
                <li>web-search: 网络搜索</li>
            </ul>
        `,
        'startup_check_meeting': `
            <div style="color: #4CAF50; font-weight: bold; margin-bottom: 0.5rem;">✅ Meeting目录权限验证</div>
            <div style="margin-bottom: 0.3rem;"><strong>检查时间:</strong> 2025-01-31 14:17:31</div>
            <div style="margin-bottom: 0.3rem;"><strong>目录路径:</strong> docs/features/.../meeting</div>
            <div style="margin-bottom: 0.3rem;"><strong>权限状态:</strong> 读写权限正常</div>
            <div style="margin-bottom: 0.3rem;"><strong>响应时间:</strong> 15ms</div>
        `
    };

    return detailContents[logId] || `
        <div style="color: #888; text-align: center; margin-top: 2rem;">
            <div>日志ID: ${logId}</div>
            <div>暂无详细信息</div>
        </div>
    `;
}

// --- 通用会议控制函数 ---

/**
 * 开始会议
 */
function startMeeting() {
    console.log('开始会议');
    // 这里可以添加具体的会议开始逻辑
}

/**
 * 暂停会议
 */
function pauseMeeting() {
    console.log('暂停会议');
    // 这里可以添加具体的会议暂停逻辑
}

/**
 * 停止会议
 */
function stopMeeting() {
    console.log('停止会议');
    // 这里可以添加具体的会议停止逻辑
}

/**
 * 处理扫描按钮点击
 */
function handleScanningClick() {
    console.log('扫描按钮点击');
    // 这里可以添加具体的扫描逻辑
}

// --- 通用工具函数 ---

/**
 * 格式化时间戳
 * @param {Date} date - 日期对象
 * @returns {string} 格式化的时间字符串
 */
function formatTimestamp(date = new Date()) {
    return date.toLocaleTimeString('zh-CN', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit' 
    });
}

/**
 * 添加日志条目
 * @param {string} containerId - 日志容器ID
 * @param {string} message - 日志消息
 * @param {string} type - 日志类型 ('info', 'success', 'warning', 'error')
 */
function addLogEntry(containerId, message, type = 'info') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const timestamp = formatTimestamp();
    const colors = {
        info: '#2196F3',
        success: '#4CAF50',
        warning: '#FF9800',
        error: '#F44336'
    };
    
    const logEntry = document.createElement('div');
    logEntry.style.color = colors[type] || colors.info;
    logEntry.style.marginBottom = '0.2rem';
    logEntry.innerHTML = `[${timestamp}] ${message}`;
    
    container.appendChild(logEntry);
    container.scrollTop = container.scrollHeight;
}
