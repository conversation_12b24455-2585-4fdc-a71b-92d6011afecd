# 注意力命令说明

## 概述

本文档记录了XKongCloud项目中常用的注意力命令，这些命令是AI记忆系统的重要组成部分，用于引导AI关注特定的项目约束、模式和最佳实践。

注意力命令系统提供了：
- **自动激活机制**：基于关键词和上下文自动激活相关命令
- **约束验证功能**：确保代码和配置符合项目标准
- **最佳实践指导**：提供具体的改进建议和修正示例
- **RIPER-5集成**：与开发流程各阶段无缝集成

这些命令覆盖了数据库操作、配置验证、错误处理、架构演进、代码质量等关键领域，是确保项目质量和一致性的重要工具。

本文档记录了XKongCloud项目中常用的注意力命令，这些命令用于引导AI关注特定的项目约束、模式和最佳实践。

## 数据库操作相关命令

### 1. `@ENFORCE_SCHEMA_NAMING`

**说明**：强制执行Schema命名规范，确保所有Schema名称符合规定的格式。

**用法**：
```
@ENFORCE_SCHEMA_NAMING
// 后续代码或说明
```

**执行效果**：
- 检查业务Schema是否使用`<业务领域>_<可选子域>`格式
- 检查基础设施Schema是否使用`infra_<组件类型>`格式
- 检查通用功能Schema是否使用`common_<功能类型>`格式
- 如果发现不符合规范的Schema命名，将提出警告并建议修正

### 2. `@ENFORCE_ENTITY_SCHEMA`

**说明**：强制所有实体类明确指定Schema，不依赖默认Schema设置。

**用法**：
```
@ENFORCE_ENTITY_SCHEMA
// 实体类代码
```

**执行效果**：
- 检查实体类是否使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
- 如果发现没有指定Schema的实体类，将提出警告并给出修正示例
- 验证指定的Schema是否符合命名规范

### 3. `@DATABASE_CONFIG_CHECK`

**说明**：检查数据库配置是否符合项目约束，包括必需的KV参数、DDL策略等。

**用法**：
```
@DATABASE_CONFIG_CHECK
// 配置代码或说明
```

**执行效果**：
- 检查是否配置了必需的KV参数：`postgresql.url`、`postgresql.username`、`postgresql.password`、`postgresql.ddl-auto`
- 检查DDL策略是否根据环境正确设置
- 检查Schema验证逻辑是否正确实现

## 配置验证相关命令

### 4. `@UID_FACADE_ENFORCE`

**说明**：强制使用UidGeneratorFacade门面模式集成UID生成器，禁止直接使用内部组件。

**用法**：
```
@UID_FACADE_ENFORCE
// UID生成器配置代码
```

**执行效果**：
- 检查是否通过UidGeneratorFacade使用UID生成功能
- 检查是否使用了UidGeneratorFacadeBuilder构建门面实例
- 检查是否正确配置了门面所需的参数
- 如果发现直接使用内部组件的代码，将提出警告并给出修正示例

### 5. `@PARAM_VALIDATION_CHECK`

**说明**：检查参数验证逻辑是否完整，确保所有必需参数都有适当的验证。

**用法**：
```
@PARAM_VALIDATION_CHECK
// 参数验证代码
```

**执行效果**：
- 检查是否对所有必需参数进行了非空验证
- 检查是否对参数进行了格式和有效性验证
- 检查是否提供了清晰的错误信息
- 检查是否在验证失败时抛出适当的异常

## 错误处理相关命令

### 6. `@ERROR_HANDLING_CHECK`

**说明**：检查错误处理逻辑是否完整，确保所有可能的异常都得到适当处理。

**用法**：
```
@ERROR_HANDLING_CHECK
// 错误处理代码
```

**执行效果**：
- 检查是否捕获并处理了可能的异常
- 检查是否提供了有用的错误信息和日志记录
- 检查是否实现了恰当的恢复策略
- 检查是否避免了异常吞噬

### 7. `@RESOURCE_MANAGEMENT_CHECK`

**说明**：检查资源管理逻辑是否完整，确保所有资源都得到适当的释放。

**用法**：
```
@RESOURCE_MANAGEMENT_CHECK
// 资源管理代码
```

**执行效果**：
- 检查是否使用了try-with-resources或类似机制管理资源
- 检查是否实现了AutoCloseable接口（如适用）
- 检查是否在finally块中释放资源
- 检查是否处理了关闭资源时可能的异常

## 架构演进相关命令

### 8. `@SERVICE_INTERFACE_CHECK`

**说明**：检查服务接口定义是否符合演进架构要求，便于未来向微服务架构演进。

**用法**：
```
@SERVICE_INTERFACE_CHECK
// 服务接口代码
```

**执行效果**：
- 检查服务接口是否使用@ServiceInterface注解
- 检查服务接口是否定义了版本信息
- 检查服务接口是否支持远程调用（如适用）
- 检查服务接口是否遵循接口设计最佳实践

### 9. `@EVOLUTIONARY_ARCHITECTURE_CHECK`

**说明**：检查代码是否符合演进架构设计原则，便于系统逐步演进。

**用法**：
```
@EVOLUTIONARY_ARCHITECTURE_CHECK
// 架构代码或说明
```

**执行效果**：
- 检查是否使用服务接口抽象层
- 检查是否使用配置驱动架构
- 检查是否实现了适当的兼容性策略
- 检查是否支持增量式变化

## 代码质量相关命令

### 10. `@CODE_DUPLICATION_CHECK`

**说明**：检查代码是否存在重复，如有必要进行重构以提高可维护性。

**用法**：
```
@CODE_DUPLICATION_CHECK
// 代码段
```

**执行效果**：
- 检查代码是否与项目中现有代码重复
- 如发现重复，建议提取共享方法或类
- 提供重构建议，减少代码重复
- 确保重构后的代码保持功能一致性

### 11. `@BEST_PRACTICE_CHECK`

**说明**：检查代码是否遵循项目的最佳实践。

**用法**：
```
@BEST_PRACTICE_CHECK
// 代码段
```

**执行效果**：
- 检查是否遵循命名约定
- 检查是否使用推荐的设计模式
- 检查是否遵循代码风格指南
- 检查是否添加了适当的注释和文档

## 注意事项

### 命令使用原则
1. **自动激活优先**：大多数命令会根据关键词自动激活，无需手动指定
2. **组合使用**：多个相关命令可以组合使用，提供全面的验证
3. **上下文相关**：命令的执行效果会根据具体的代码上下文调整

### 命令执行优先级
1. **数据库操作命令**：最高优先级，确保数据一致性
2. **配置验证命令**：高优先级，确保系统配置正确
3. **错误处理命令**：中高优先级，确保系统稳定性
4. **架构演进命令**：中优先级，支持长期架构发展
5. **代码质量命令**：基础优先级，提升代码可维护性

### 自动激活规则
- **关键词检测**：系统会自动检测相关关键词并激活对应命令
- **RIPER-5集成**：不同开发阶段会自动激活相应的命令集
- **项目上下文**：根据当前项目类型和技术栈激活相关命令

### 命令扩展机制
- 新的约束和最佳实践可以通过添加新命令来支持
- 现有命令可以根据项目演进进行更新和优化
- 命令配置存储在JSON格式中，便于程序化处理和更新