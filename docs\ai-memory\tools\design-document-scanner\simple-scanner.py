#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计文档规范扫描程序
目标：检查设计文档是否符合AI生成80%成功率的要求
用法：python simple-scanner.py <文档路径或目录>
"""

import os
import re
import json
import sys
from pathlib import Path
from typing import Dict, List, Tuple

class DesignDocScanner:
    def __init__(self):
        # 基于design_document_extractor.py需求的关键检查点
        self.checks = {
            # 一级检查：提取器必需信息（权重50%）
            'extractor_required': {
                'weight': 0.5,
                'items': [
                    ('项目名称', r'项目名称|project.*name|#.*[A-Z][a-zA-Z0-9\s]*:', 20, '缺少明确的项目名称'),
                    ('核心定位', r'##\s*(核心定位|目标|愿景|定位)', 15, '缺少核心定位章节'),
                    ('设计哲学', r'设计哲学|设计理念|design.*philosophy', 10, '缺少设计哲学说明'),
                    ('范围边界', r'包含范围|排除范围|scope|边界', 5, '缺少范围边界定义'),
                ]
            },
            # 二级检查：技术栈信息（权重30%）
            'tech_stack': {
                'weight': 0.3,
                'items': [
                    ('Java版本', r'Java\s+(\d+)', 10, '缺少Java版本信息'),
                    ('Spring Boot版本', r'Spring\s+Boot\s+([\d.]+)', 8, '缺少Spring Boot版本'),
                    ('构建工具', r'Maven|Gradle', 5, '缺少构建工具信息'),
                    ('数据库信息', r'PostgreSQL|MySQL|Redis', 5, '缺少数据库技术栈'),
                    ('其他框架', r'HikariCP|JPA|Querydsl|Micrometer|Flyway', 2, '缺少其他重要框架信息'),
                ]
            },
            # 三级检查：文档元数据（权重15%）
            'metadata': {
                'weight': 0.15,
                'items': [
                    ('文档ID', r'文档ID[：:]|document.*id', 5, '缺少文档ID'),
                    ('版本信息', r'版本[：:]|version[：:]', 5, '缺少版本信息'),
                    ('复杂度等级', r'L[123]|复杂度.*等级|complexity.*level', 3, '缺少复杂度等级标识'),
                    ('创建日期', r'创建日期|date', 2, '缺少创建日期'),
                ]
            },
            # 四级检查：可提取性（权重5%）
            'extractability': {
                'weight': 0.05,
                'items': [
                    ('结构化内容', r'```|表格|\|.*\|', 3, '缺少结构化内容（代码块或表格）'),
                    ('章节标题', r'##\s+', 2, '缺少清晰的章节结构'),
                ]
            }
        }
        
        # 基于design_document_extractor.py的反模式检查
        self.anti_patterns = [
            (r'最新版本|latest|当前版本', '模糊版本描述', '高', '使用精确版本号如"Spring Boot 3.4.5"'),
            (r'可能|也许|大概|应该会', '不确定性表述', '中', '使用明确的技术描述'),
            (r'高性能|快速|优化(?![\d\w])', '性能描述模糊', '低', '提供具体指标如"响应时间<100ms"'),
        ]

        # design_document_extractor.py期望的技术栈模式
        self.expected_tech_patterns = {
            "Java": r'Java\s+(\d+)',
            "Spring Boot": r'Spring\s+Boot\s+([\d.]+)',
            "PostgreSQL": r'PostgreSQL\s+([\d.]+)',
            "HikariCP": r'HikariCP',
            "Virtual Threads": r'Virtual\s+Threads',
            "Maven": r'Maven',
            "Gradle": r'Gradle',
            "JPA": r'\bJPA\b',
            "Querydsl": r'Querydsl',
            "Redis": r'Redis',
            "Micrometer": r'Micrometer',
            "Flyway": r'Flyway'
        }

    def scan_file(self, file_path: str) -> Dict:
        """扫描单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f'读取文件失败: {str(e)}'}
        
        result = {
            'file': file_path,
            'scores': {},
            'total_score': 0,
            'issues': [],
            'suggestions': []
        }

        total_weighted_score = 0

        # 执行各类检查
        for check_type, check_config in self.checks.items():
            score = 0
            issues = []

            for item_name, pattern, points, error_msg in check_config['items']:
                if re.search(pattern, content, re.IGNORECASE):
                    score += points
                else:
                    issues.append({
                        'type': check_type,
                        'item': item_name,
                        'message': error_msg,
                        'points_lost': points
                    })

            # 计算该类别得分（满分100）
            max_score = sum(item[2] for item in check_config['items'])
            category_score = (score / max_score * 100) if max_score > 0 else 0
            result['scores'][check_type] = round(category_score, 1)
            result['issues'].extend(issues)

            # 加权计算总分
            total_weighted_score += category_score * check_config['weight']

        result['total_score'] = round(total_weighted_score, 1)
        
        # 检查反模式
        for pattern, issue_type, severity, suggestion in self.anti_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                result['issues'].append({
                    'type': 'anti_pattern',
                    'item': issue_type,
                    'message': f'发现{len(matches)}处{issue_type}',
                    'severity': severity,
                    'examples': list(set(matches))[:3]  # 最多显示3个例子
                })
                result['suggestions'].append({
                    'type': issue_type,
                    'suggestion': suggestion,
                    'priority': severity
                })
        
        # 检查design_document_extractor.py的技术栈提取能力
        self._check_tech_extraction_capability(result, content)

        # 生成改进建议
        self._generate_suggestions(result)

        return result

    def _check_tech_extraction_capability(self, result: Dict, content: str):
        """检查技术栈提取能力"""
        extractable_techs = []
        missing_techs = []

        for tech, pattern in self.expected_tech_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                extractable_techs.append(tech)
            else:
                missing_techs.append(tech)

        # 添加技术栈提取能力评估
        if len(extractable_techs) < 3:
            result['issues'].append({
                'type': 'tech_extraction',
                'item': '技术栈信息不足',
                'message': f'仅能提取{len(extractable_techs)}个技术栈，建议至少3个',
                'extractable': extractable_techs,
                'missing': missing_techs[:5]  # 只显示前5个缺失的
            })
            result['suggestions'].append({
                'type': 'tech_stack',
                'suggestion': f'补充关键技术栈信息: {", ".join(missing_techs[:3])}',
                'priority': '高'
            })

    def _generate_suggestions(self, result: Dict):
        """生成改进建议"""
        high_priority = []
        medium_priority = []

        for issue in result['issues']:
            if issue.get('points_lost', 0) >= 15:
                high_priority.append(issue)
            elif issue.get('points_lost', 0) >= 5:
                medium_priority.append(issue)

        # 针对design_document_extractor.py的特殊建议
        extractor_issues = [i for i in result['issues'] if i.get('type') == 'extractor_required']
        if extractor_issues:
            result['suggestions'].append({
                'type': 'extractor_compatibility',
                'suggestion': '为提高80%提示词生成成功率，优先完善: ' + ', '.join([i['item'] for i in extractor_issues[:3]]),
                'priority': '最高'
            })

        # 高优先级建议
        if high_priority:
            result['suggestions'].append({
                'type': 'structure',
                'suggestion': f'优先添加缺失的关键信息: {", ".join([i["item"] for i in high_priority[:3]])}',
                'priority': '高'
            })

        # 中优先级建议
        if medium_priority:
            result['suggestions'].append({
                'type': 'content',
                'suggestion': f'补充重要内容: {", ".join([i["item"] for i in medium_priority[:3]])}',
                'priority': '中'
            })

    def scan_directory(self, dir_path: str) -> List[Dict]:
        """扫描目录下的所有markdown文件"""
        results = []
        path = Path(dir_path)
        
        # 查找所有.md文件
        md_files = list(path.glob('**/*.md'))
        
        for md_file in md_files:
            result = self.scan_file(str(md_file))
            if 'error' not in result:
                results.append(result)
        
        return results

    def generate_report(self, results: List[Dict]) -> Dict:
        """生成汇总报告"""
        if not results:
            return {'error': '没有找到可扫描的文件'}
        
        total_files = len(results)
        avg_score = sum(r['total_score'] for r in results) / total_files
        high_quality = len([r for r in results if r['total_score'] >= 80])
        needs_improvement = len([r for r in results if r['total_score'] < 60])
        
        # 统计最常见的问题
        all_issues = []
        for result in results:
            all_issues.extend(result['issues'])
        
        issue_counts = {}
        for issue in all_issues:
            key = issue['item']
            issue_counts[key] = issue_counts.get(key, 0) + 1
        
        common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            'summary': {
                'total_files': total_files,
                'average_score': round(avg_score, 1),
                'high_quality_files': high_quality,
                'needs_improvement': needs_improvement,
                'pass_rate': f'{high_quality}/{total_files} ({round(high_quality/total_files*100, 1)}%)'
            },
            'common_issues': common_issues,
            'detailed_results': results,
            'recommendations': self._get_overall_recommendations(results)
        }

    def _get_overall_recommendations(self, results: List[Dict]) -> List[str]:
        """生成整体改进建议"""
        avg_scores = {}
        for check_type in self.checks.keys():
            scores = [r['scores'].get(check_type, 0) for r in results]
            avg_scores[check_type] = sum(scores) / len(scores) if scores else 0

        recommendations = []

        # 基于design_document_extractor.py需求的建议
        if avg_scores['extractor_required'] < 80:
            recommendations.append('🚨 关键：完善提取器必需信息（项目名称、核心定位、设计哲学、范围边界）以确保80%提示词生成成功')

        if avg_scores['tech_stack'] < 70:
            recommendations.append('🔧 技术栈：补充完整的技术栈版本信息，包括Java、Spring Boot、数据库等关键组件')

        if avg_scores['metadata'] < 70:
            recommendations.append('📋 元数据：添加文档ID、版本、复杂度等级等元数据信息')

        if avg_scores['extractability'] < 70:
            recommendations.append('📊 结构化：增加代码块、表格等结构化内容，提高信息提取准确性')

        # 计算整体提取成功率预估
        overall_score = sum(avg_scores[k] * self.checks[k]['weight'] for k in avg_scores.keys())
        if overall_score < 60:
            recommendations.insert(0, f'⚠️ 警告：当前文档质量可能导致提取失败（预估成功率{overall_score:.1f}%），建议优先解决上述问题')

        return recommendations

def main():
    if len(sys.argv) != 2:
        print("用法: python simple-scanner.py <文档路径或目录>")
        sys.exit(1)
    
    input_path = sys.argv[1]
    scanner = DesignDocScanner()
    
    if os.path.isfile(input_path):
        # 扫描单个文件
        result = scanner.scan_file(input_path)
        if 'error' in result:
            print(f"错误: {result['error']}")
            sys.exit(1)
        
        print(f"\n=== 文档扫描结果 ===")
        print(f"文件: {result['file']}")
        print(f"总分: {result['total_score']}/100")
        print(f"结构完整性: {result['scores'].get('structure', 0)}/100")
        print(f"AI友好性: {result['scores'].get('ai_friendly', 0)}/100")
        print(f"可执行性: {result['scores'].get('executable', 0)}/100")
        print(f"内容完整性: {result['scores'].get('completeness', 0)}/100")
        
        if result['issues']:
            print(f"\n发现 {len(result['issues'])} 个问题:")
            for issue in result['issues'][:5]:  # 只显示前5个
                print(f"  - {issue['message']}")
        
        if result['suggestions']:
            print(f"\n改进建议:")
            for suggestion in result['suggestions'][:3]:  # 只显示前3个
                print(f"  - {suggestion['suggestion']}")
    
    elif os.path.isdir(input_path):
        # 扫描目录
        results = scanner.scan_directory(input_path)
        report = scanner.generate_report(results)
        
        if 'error' in report:
            print(f"错误: {report['error']}")
            sys.exit(1)
        
        print(f"\n=== 目录扫描报告 ===")
        print(f"扫描文件数: {report['summary']['total_files']}")
        print(f"平均得分: {report['summary']['average_score']}/100")
        print(f"高质量文档: {report['summary']['high_quality_files']} 个")
        print(f"需要改进: {report['summary']['needs_improvement']} 个")
        print(f"通过率: {report['summary']['pass_rate']}")
        
        if report['common_issues']:
            print(f"\n最常见问题:")
            for issue, count in report['common_issues']:
                print(f"  - {issue}: {count} 次")
        
        if report['recommendations']:
            print(f"\n整体建议:")
            for rec in report['recommendations']:
                print(f"  - {rec}")
        
        # 保存详细报告
        output_file = 'scan_report.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n详细报告已保存到: {output_file}")
    
    else:
        print(f"错误: 路径不存在 - {input_path}")
        sys.exit(1)

if __name__ == '__main__':
    main()
