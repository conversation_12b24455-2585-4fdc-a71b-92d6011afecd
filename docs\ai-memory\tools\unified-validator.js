#!/usr/bin/env node

/**
 * 统一验证器 - 一键完成所有验证
 * 
 * 集成所有验证功能：
 * - 功能文档验证
 * - 权威性验证
 * - 同步状态检查
 * - 文档结构验证
 * - 记忆系统完整性验证
 * 
 * 使用方法:
 * node docs/ai-memory/tools/unified-validator.js
 */

const fs = require('fs');
const path = require('path');

class UnifiedValidator {
    constructor() {
        this.rootPath = path.resolve(__dirname, '../../..');
        this.results = {
            summary: {
                total_checks: 0,
                passed_checks: 0,
                failed_checks: 0,
                warnings: 0,
                overall_status: 'unknown'
            },
            details: {
                structure_check: null,
                feature_validation: null,
                authority_validation: null,
                sync_status: null,
                memory_integrity: null
            },
            recommendations: []
        };
        
        this.loadSubValidators();
    }

    loadSubValidators() {
        try {
            // 加载所有子验证器
            const MemorySystemVerifier = require('./verify-memory-system.js');
            this.memoryVerifier = new MemorySystemVerifier();
            
            const AuthorityValidator = require('./authority-validator.js');
            this.authorityValidator = new AuthorityValidator();
            
            console.log('✓ 所有验证组件加载成功');
        } catch (error) {
            console.error('✗ 验证组件加载失败:', error.message);
            process.exit(1);
        }
    }

    async runCompleteValidation() {
        console.log('\n🚀 启动统一验证器 - 完整系统检查');
        console.log('='.repeat(70));
        
        // 1. 文档结构验证
        await this.runStructureValidation();
        
        // 2. 功能文档验证
        await this.runFeatureValidation();
        
        // 3. 权威性验证
        await this.runAuthorityValidation();
        
        // 4. 同步状态验证
        await this.runSyncValidation();
        
        // 5. 记忆系统完整性验证
        await this.runMemoryIntegrityValidation();
        
        // 6. 生成综合报告
        this.generateComprehensiveReport();
        
        return this.results.summary.overall_status === 'passed';
    }

    async runStructureValidation() {
        console.log('\n📁 1. 文档结构验证');
        console.log('-'.repeat(50));
        
        this.results.summary.total_checks++;
        
        try {
            // 检查关键目录
            const keyDirectories = [
                'docs/ai-memory',
                'docs/ai-memory/L1-core',
                'docs/ai-memory/L2-context', 
                'docs/ai-memory/L3-index',
                'docs/ai-memory/tools',
                'docs/common',
                'docs/features'
            ];

            const missingDirs = [];
            keyDirectories.forEach(dir => {
                const fullPath = path.join(this.rootPath, dir);
                if (fs.existsSync(fullPath)) {
                    console.log(`  ✓ ${dir}`);
                } else {
                    console.log(`  ✗ ${dir} (缺失)`);
                    missingDirs.push(dir);
                }
            });

            // 检查关键文件
            const keyFiles = [
                'docs/feature-status.json',
                'docs/ai-memory/tools/authority-rules.json',
                'docs/ai-memory/templates/feature-index-template.md'
            ];

            const missingFiles = [];
            keyFiles.forEach(file => {
                const fullPath = path.join(this.rootPath, file);
                if (fs.existsSync(fullPath)) {
                    console.log(`  ✓ ${file}`);
                } else {
                    console.log(`  ✗ ${file} (缺失)`);
                    missingFiles.push(file);
                }
            });

            const structureOk = missingDirs.length === 0 && missingFiles.length === 0;
            
            this.results.details.structure_check = {
                status: structureOk ? 'passed' : 'failed',
                missing_directories: missingDirs,
                missing_files: missingFiles
            };

            if (structureOk) {
                console.log('  🎉 文档结构验证通过');
                this.results.summary.passed_checks++;
            } else {
                console.log(`  ❌ 文档结构验证失败: ${missingDirs.length + missingFiles.length} 个缺失项`);
                this.results.summary.failed_checks++;
            }

        } catch (error) {
            console.log(`  💥 文档结构验证出错: ${error.message}`);
            this.results.details.structure_check = { status: 'error', error: error.message };
            this.results.summary.failed_checks++;
        }
    }

    async runFeatureValidation() {
        console.log('\n📋 2. 功能文档验证');
        console.log('-'.repeat(50));
        
        this.results.summary.total_checks++;
        
        try {
            const success = await this.memoryVerifier.run({ fullReport: false });
            
            this.results.details.feature_validation = {
                status: success ? 'passed' : 'failed',
                errors: this.memoryVerifier.errors.length,
                warnings: this.memoryVerifier.warnings.length,
                info_count: this.memoryVerifier.info.length
            };

            if (success) {
                console.log('  🎉 功能文档验证通过');
                this.results.summary.passed_checks++;
            } else {
                console.log(`  ❌ 功能文档验证失败: ${this.memoryVerifier.errors.length} 个错误`);
                this.results.summary.failed_checks++;
            }
            
            this.results.summary.warnings += this.memoryVerifier.warnings.length;

        } catch (error) {
            console.log(`  💥 功能文档验证出错: ${error.message}`);
            this.results.details.feature_validation = { status: 'error', error: error.message };
            this.results.summary.failed_checks++;
        }
    }

    async runAuthorityValidation() {
        console.log('\n🔐 3. 权威性验证');
        console.log('-'.repeat(50));
        
        this.results.summary.total_checks++;
        
        try {
            const authorityReport = await this.authorityValidator.validateAll();
            
            const success = authorityReport.summary.failed === 0;
            
            this.results.details.authority_validation = {
                status: success ? 'passed' : 'failed',
                total_files: authorityReport.summary.total_files,
                passed_files: authorityReport.summary.passed,
                failed_files: authorityReport.summary.failed,
                warnings: authorityReport.summary.warnings
            };

            console.log(`  📊 验证文件: ${authorityReport.summary.total_files} 个`);
            console.log(`  ✓ 通过: ${authorityReport.summary.passed} 个`);
            console.log(`  ✗ 失败: ${authorityReport.summary.failed} 个`);
            console.log(`  ⚠ 警告: ${authorityReport.summary.warnings} 个`);

            if (success) {
                console.log('  🎉 权威性验证通过');
                this.results.summary.passed_checks++;
            } else {
                console.log(`  ❌ 权威性验证失败`);
                this.results.summary.failed_checks++;
            }
            
            this.results.summary.warnings += authorityReport.summary.warnings;

        } catch (error) {
            console.log(`  💥 权威性验证出错: ${error.message}`);
            this.results.details.authority_validation = { status: 'error', error: error.message };
            this.results.summary.failed_checks++;
        }
    }

    async runSyncValidation() {
        console.log('\n🔄 4. 同步状态验证');
        console.log('-'.repeat(50));
        
        this.results.summary.total_checks++;
        
        try {
            // 加载功能状态
            const featureStatusPath = path.join(this.rootPath, 'docs/feature-status.json');
            const featureStatus = JSON.parse(fs.readFileSync(featureStatusPath, 'utf8'));
            
            const features = featureStatus.feature_status_registry.features;
            const syncIssues = [];
            const now = new Date();
            
            Object.entries(features).forEach(([featureId, feature]) => {
                const lastSyncDate = new Date(feature.last_sync_date);
                const daysSinceSync = Math.floor((now - lastSyncDate) / (1000 * 60 * 60 * 24));
                
                const syncRules = featureStatus.feature_status_registry.sync_rules;
                const statusRule = syncRules[feature.status];
                
                console.log(`  📋 ${featureId}: ${feature.status} (${daysSinceSync}天前同步)`);
                
                if (statusRule && daysSinceSync > statusRule.check_frequency_days) {
                    const issue = `${featureId} 同步超时: ${daysSinceSync}天 > ${statusRule.check_frequency_days}天阈值`;
                    console.log(`    ⚠ ${issue}`);
                    syncIssues.push(issue);
                }
                
                if (feature.sync_status === 'requires_update') {
                    const issue = `${featureId} 需要更新`;
                    console.log(`    ⚠ ${issue}`);
                    syncIssues.push(issue);
                }
            });

            const syncOk = syncIssues.length === 0;
            
            this.results.details.sync_status = {
                status: syncOk ? 'passed' : 'warning',
                total_features: Object.keys(features).length,
                sync_issues: syncIssues
            };

            if (syncOk) {
                console.log('  🎉 同步状态验证通过');
                this.results.summary.passed_checks++;
            } else {
                console.log(`  ⚠ 同步状态需要关注: ${syncIssues.length} 个问题`);
                this.results.summary.passed_checks++; // 同步问题算警告，不算失败
                this.results.summary.warnings += syncIssues.length;
            }

        } catch (error) {
            console.log(`  💥 同步状态验证出错: ${error.message}`);
            this.results.details.sync_status = { status: 'error', error: error.message };
            this.results.summary.failed_checks++;
        }
    }

    async runMemoryIntegrityValidation() {
        console.log('\n🧠 5. 记忆系统完整性验证');
        console.log('-'.repeat(50));
        
        this.results.summary.total_checks++;
        
        try {
            // 检查记忆层级完整性
            const layers = ['L1-core', 'L2-context', 'L3-index'];
            const layerStatus = {};
            
            layers.forEach(layer => {
                const layerPath = path.join(this.rootPath, 'docs/ai-memory', layer);
                if (fs.existsSync(layerPath)) {
                    const files = this.getFilesRecursively(layerPath);
                    layerStatus[layer] = {
                        exists: true,
                        file_count: files.length,
                        files: files
                    };
                    console.log(`  ✓ ${layer}: ${files.length} 个文件`);
                } else {
                    layerStatus[layer] = { exists: false };
                    console.log(`  ✗ ${layer}: 目录不存在`);
                }
            });

            const allLayersExist = layers.every(layer => layerStatus[layer].exists);
            const totalFiles = Object.values(layerStatus)
                .filter(status => status.exists)
                .reduce((sum, status) => sum + status.file_count, 0);

            this.results.details.memory_integrity = {
                status: allLayersExist ? 'passed' : 'failed',
                layer_status: layerStatus,
                total_memory_files: totalFiles
            };

            if (allLayersExist) {
                console.log(`  🎉 记忆系统完整性验证通过 (${totalFiles} 个文件)`);
                this.results.summary.passed_checks++;
            } else {
                console.log('  ❌ 记忆系统完整性验证失败');
                this.results.summary.failed_checks++;
            }

        } catch (error) {
            console.log(`  💥 记忆系统完整性验证出错: ${error.message}`);
            this.results.details.memory_integrity = { status: 'error', error: error.message };
            this.results.summary.failed_checks++;
        }
    }

    getFilesRecursively(dir) {
        let files = [];
        const items = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const item of items) {
            const fullPath = path.join(dir, item.name);
            if (item.isDirectory()) {
                files = files.concat(this.getFilesRecursively(fullPath));
            } else {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    generateComprehensiveReport() {
        console.log('\n' + '='.repeat(70));
        console.log('                    统一验证器 - 综合报告');
        console.log('='.repeat(70));

        // 计算整体状态
        const passRate = this.results.summary.total_checks > 0 ?
            (this.results.summary.passed_checks / this.results.summary.total_checks * 100).toFixed(1) : 0;

        if (this.results.summary.failed_checks === 0) {
            this.results.summary.overall_status = 'passed';
        } else {
            this.results.summary.overall_status = 'failed';
        }

        // 验证结果概览
        console.log('\n📊 验证结果概览:');
        console.log(`  总验证项目: ${this.results.summary.total_checks}`);
        console.log(`  通过项目: ${this.results.summary.passed_checks}`);
        console.log(`  失败项目: ${this.results.summary.failed_checks}`);
        console.log(`  警告数量: ${this.results.summary.warnings}`);
        console.log(`  通过率: ${passRate}%`);
        console.log(`  整体状态: ${this.getStatusIcon(this.results.summary.overall_status)} ${this.results.summary.overall_status.toUpperCase()}`);

        // 详细结果
        console.log('\n📋 详细验证结果:');

        const checks = [
            { name: '文档结构验证', key: 'structure_check' },
            { name: '功能文档验证', key: 'feature_validation' },
            { name: '权威性验证', key: 'authority_validation' },
            { name: '同步状态验证', key: 'sync_status' },
            { name: '记忆系统完整性', key: 'memory_integrity' }
        ];

        checks.forEach(check => {
            const result = this.results.details[check.key];
            if (result) {
                const icon = this.getStatusIcon(result.status);
                console.log(`  ${icon} ${check.name}: ${result.status.toUpperCase()}`);

                if (result.status === 'failed' && result.error) {
                    console.log(`    错误: ${result.error}`);
                }
            }
        });

        // 生成建议
        this.generateRecommendations();

        // 显示建议
        if (this.results.recommendations.length > 0) {
            console.log('\n💡 修复建议:');
            this.results.recommendations.forEach((rec, index) => {
                console.log(`  ${index + 1}. ${rec}`);
            });
        }

        // 工具使用指南
        console.log('\n🛠️ 相关工具:');
        console.log('  - 快速检查: node unified-validator.js');
        console.log('  - 权威性同步: node authority-sync-executor.js sync');
        console.log('  - 功能同步: 使用 @sync:feature:{ID} 命令');

        console.log('\n' + '='.repeat(70));

        // 最终状态
        if (this.results.summary.overall_status === 'passed') {
            console.log('🎉 所有验证通过！系统状态良好。');
        } else {
            console.log('❌ 发现问题需要修复。请查看上述建议。');
        }

        console.log('='.repeat(70));
    }

    getStatusIcon(status) {
        switch (status) {
            case 'passed': return '✅';
            case 'failed': return '❌';
            case 'warning': return '⚠️';
            case 'error': return '💥';
            default: return '❓';
        }
    }

    generateRecommendations() {
        this.results.recommendations = [];

        // 基于验证结果生成建议
        if (this.results.details.structure_check?.status === 'failed') {
            this.results.recommendations.push('修复缺失的文档结构目录和文件');
        }

        if (this.results.details.feature_validation?.status === 'failed') {
            this.results.recommendations.push('修复功能文档验证错误，检查功能状态和文档路径');
        }

        if (this.results.details.authority_validation?.status === 'failed') {
            this.results.recommendations.push('为缺少权威性元数据的文件添加元数据，运行权威性同步');
        }

        if (this.results.details.sync_status?.sync_issues?.length > 0) {
            this.results.recommendations.push('执行功能同步更新，解决同步超时问题');
        }

        if (this.results.details.memory_integrity?.status === 'failed') {
            this.results.recommendations.push('修复记忆系统层级结构，确保所有层级目录存在');
        }

        // 通用建议
        if (this.results.summary.warnings > 0) {
            this.results.recommendations.push('关注并逐步解决警告项目');
        }

        if (this.results.summary.failed_checks === 0 && this.results.summary.warnings === 0) {
            this.results.recommendations.push('系统状态良好，建议定期运行验证以保持健康状态');
        }
    }
}

// 主函数
async function main() {
    const validator = new UnifiedValidator();
    const success = await validator.runCompleteValidation();

    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('统一验证器运行失败:', error);
        process.exit(1);
    });
}

module.exports = UnifiedValidator;
