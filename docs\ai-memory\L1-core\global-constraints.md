# 全局硬约束

## 概述

本文档记录了XKongCloud项目中必须严格遵循的全局硬约束，这些约束是项目架构稳定性和数据一致性的基础保障。所有开发活动都必须严格遵循这些约束，特别是在PostgreSQL迁移、UID生成器集成和实体类设计等关键领域。

这些约束经过深度架构分析和实践验证，旨在确保：
- 数据库架构的一致性和可维护性
- 分布式ID生成的可靠性和唯一性
- 代码质量和系统稳定性
- 跨项目集成的标准化

本文档记录了XKongCloud项目中必须严格遵循的全局硬约束，尤其是与PostgreSQL迁移相关的约束。

## Schema命名规范

为了提高数据组织的清晰度和可维护性，必须严格遵循以下Schema命名规范：

1. **业务Schema**：采用`<业务领域>_<可选子域>`格式
   - 例如：`user_management`表示用户管理业务领域
   - 其他业务Schema示例：`identity_core`、`payment_processing`、`order_management`

2. **基础设施Schema**：采用`infra_<组件类型>`格式
   - 例如：`infra_uid`表示UID生成器基础设施组件
   - 其他基础设施Schema示例：`infra_audit`、`infra_cache`、`infra_messaging`

3. **通用功能Schema**：采用`common_<功能类型>`格式
   - 例如：`common_config`表示系统配置通用功能
   - 其他通用功能Schema示例：`common_logging`、`common_security`、`common_reference`

在实现过程中，必须严格遵循这些命名规范，确保数据库结构的一致性和可维护性。

## 实体类Schema指定强制约束

所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema，不允许依赖任何默认Schema设置。这是一项强制性要求，没有例外情况。

**正确的实体类示例**：
```java
@Entity
@Table(name = "user", schema = "user_management")
public class User {
    @Id
    private Long userId;

    @Column(name = "username", nullable = false)
    private String username;

    // 其他字段和方法...
}
```

**错误的实体类示例**：
```java
@Entity
@Table(name = "user")  // 错误：没有指定schema
public class User {
    @Id
    private Long userId;

    @Column(name = "username", nullable = false)
    private String username;

    // 其他字段和方法...
}
```

## 数据库连接配置约束

PostgreSQL数据库连接配置必须遵循以下约束：

1. **必需的KV参数**：以下参数必须在xkongcloud-service-center中配置，且无默认值：
   - `postgresql.url`
   - `postgresql.username`
   - `postgresql.password`
   - `postgresql.ddl-auto`

2. **DDL策略约束**：
   - 开发环境可以使用`create`或`update`
   - 测试环境可以使用`create-drop`
   - 生产环境必须使用`validate`或`none`

3. **Schema验证约束**：
   - 应用程序只负责验证Schema是否存在，不会尝试创建Schema
   - 如果验证发现任何必需的Schema不存在，应用程序将无法启动

## UID生成器配置约束

UID生成器配置必须遵循以下约束：

1. **门面模式强制使用**：
   - 必须通过`UidGeneratorFacade`使用UID库的所有功能
   - 禁止直接创建内部组件

2. **必需的UID核心参数**：以下参数必须配置，且无默认值：
   - `uid.epochStr`
   - `uid.timeBits`
   - `uid.workerBits`
   - `uid.seqBits`

3. **环境差异参数**：
   - 生产环境必须启用`uid.instance.encryption.enabled=true`
   - 生产环境应使用`uid.instance.recovery.strategy=ALERT_AUTO_WITH_TIMEOUT`

## 代码整洁性约束

代码要保持整洁，减少重复代码块，如果发现新增的代码和以前的有重复，要把它重构到一块儿，提高可维护性和可读性。

## 注意事项

### 约束执行优先级
1. **数据库相关约束**：最高优先级，违反将导致数据不一致或系统无法启动
2. **UID生成器约束**：高优先级，违反将影响分布式ID的唯一性
3. **代码质量约束**：中优先级，违反将影响长期维护性

### 约束验证机制
- **编译时验证**：通过注解和静态分析工具检查实体类Schema指定
- **启动时验证**：应用启动时验证数据库连接和Schema存在性
- **运行时验证**：UID生成器运行时验证配置参数完整性

### 违反约束的处理
- **立即停止**：发现违反数据库或UID约束时，立即停止相关操作
- **错误报告**：提供明确的错误信息，指出具体违反的约束和修复建议
- **回滚机制**：对于可回滚的操作，自动回滚到约束满足的状态

### 约束更新流程
- 全局硬约束的任何修改都需要经过架构委员会审查
- 约束变更必须向后兼容，或提供明确的迁移路径
- 约束文档更新后，必须同步更新相关的验证工具和检查脚本