/**
 * PM V2 数据类型映射配置
 * 定义所有组件的数据源、API端点和WebSocket事件映射
 */

// PM V2 数据类型到API端点的映射
const PM_V2_DATA_TYPE_MAPPING = {
    // ProjectProgressComponent 数据类型
    'progress': {
        endpoint: '/pm_v2/progress',
        method: 'GET',
        cache: true,
        cacheTTL: 30000, // 30秒缓存
        mockData: {
            current_stage: 'stage_2',
            overall_progress: 75,
            stage_progress: {
                stage_0: { name: '阶段零：可靠性保证', status: 'completed', progress: 100 },
                stage_1: { name: '阶段一：需求分析', status: 'completed', progress: 100 },
                stage_2: { name: '阶段二：设计实现', status: 'current', progress: 75 },
                stage_3: { name: '阶段三：测试验证', status: 'pending', progress: 0 }
            }
        }
    },
    
    'stage_metrics': {
        endpoint: '/pm_v2/stage_metrics',
        method: 'GET',
        cache: true,
        cacheTTL: 60000, // 1分钟缓存
        mockData: {
            stage_zero_metrics: {
                reliability_score: 95,
                constraint_coverage: 88,
                risk_mitigation: 92,
                validation_passed: true
            },
            current_metrics: {
                documents_processed: 3,
                constraints_identified: 25,
                risks_assessed: 2,
                completion_rate: 0.75
            }
        }
    },

    // RiskAssessmentComponent 数据类型
    'risk_assessment': {
        endpoint: '/pm_v2/risk_assessment',
        method: 'GET',
        cache: true,
        cacheTTL: 45000, // 45秒缓存
        mockData: {
            overall_score: 85,
            risk_level: 'low',
            reliability_dashboard: {
                score: 95,
                status: 'excellent',
                trend: 'stable'
            },
            prevention_measures: [
                {
                    risk_type: '设计不一致性',
                    status: 'prevented',
                    description: '通过AtomicConstraint机制确保设计一致性',
                    prevention_detail: '实时约束验证和自动冲突检测'
                },
                {
                    risk_type: '需求变更风险',
                    status: 'monitoring',
                    description: '监控需求变更对现有设计的影响',
                    prevention_detail: '变更影响分析和回归测试'
                }
            ]
        }
    },

    'health_report': {
        endpoint: '/pm_v2/health_report',
        method: 'GET',
        cache: true,
        cacheTTL: 120000, // 2分钟缓存
        mockData: {
            document_health: {
                total_documents: 5,
                processed_documents: 3,
                health_score: 88
            },
            constraint_health: {
                total_constraints: 25,
                active_constraints: 23,
                health_score: 92
            },
            system_health: {
                api_status: 'healthy',
                websocket_status: 'connected',
                overall_score: 95
            }
        }
    },

    // ManagerStatusComponent 数据类型
    'manager_status': {
        endpoint: '/pm_v2/manager_status',
        method: 'GET',
        cache: true,
        cacheTTL: 15000, // 15秒缓存
        mockData: {
            current_pm_role: '首席架构师AI',
            work_status: 'ACTIVE',
            current_doc: '1-总体架构设计-V2.md'
        }
    },

    'task_status': {
        endpoint: '/pm_v2/task_status',
        method: 'GET',
        cache: true,
        cacheTTL: 10000, // 10秒缓存
        mockData: {
            current_task: '从01号文档中识别设计意图并进行实体分类',
            progress: 75,
            processing_time: '2分30秒'
        }
    },

    // AlgorithmMindsetComponent 数据类型
    'algorithm_logs': {
        endpoint: '/pm_v2/algorithm_logs',
        method: 'GET',
        cache: true,
        cacheTTL: 20000, // 20秒缓存
        mockData: {
            logs: [
                {
                    id: 'log_001',
                    timestamp: '14:23:15',
                    type: 'ai_communication',
                    summary: 'AI分析文档结构，识别出3个核心实体',
                    ai_comm_detail: 'AI模型分析了文档的语义结构，识别出UserStory、Constraint、Risk三个核心实体类型',
                    py_ops_detail: 'Python算法执行实体提取，生成结构化数据并存储到知识图谱'
                },
                {
                    id: 'log_002',
                    timestamp: '14:25:42',
                    type: 'algorithm_processing',
                    summary: '算法处理约束关系，建立依赖图',
                    ai_comm_detail: 'AI识别约束间的逻辑依赖关系，发现2个潜在冲突点',
                    py_ops_detail: '图算法构建约束依赖网络，应用冲突检测算法标记风险节点'
                }
            ]
        }
    },

    // ConstraintReviewComponent 数据类型
    'constraint_detail': {
        endpoint: '/pm_v2/constraint_detail',
        method: 'GET',
        cache: false, // 约束详情不缓存，保证实时性
        mockData: null // 通过组件方法动态设置
    },

    // KnowledgeBaseComponent 数据类型
    'knowledge_graph': {
        endpoint: '/pm_v2/knowledge_graph',
        method: 'GET',
        cache: true,
        cacheTTL: 90000, // 1.5分钟缓存
        mockData: {
            nodes: [
                { id: 'C001', x: 50, y: 50, category: 'constraint', label: '全局约束', description: '系统级别的全局约束' },
                { id: 'C002', x: 150, y: 80, category: 'boundary_condition', label: '边界条件', description: '模块边界约束' },
                { id: 'C003', x: 100, y: 120, category: 'state_machine', label: '状态机', description: '状态转换约束' },
                { id: 'C004', x: 200, y: 60, category: 'guardrail', label: '护栏约束', description: '安全护栏机制', is_forked: true, forked_from: 'C001' }
            ],
            connections: [
                { from: 'C001', to: 'C002', type: 'reference' },
                { from: 'C001', to: 'C004', type: 'fork' },
                { from: 'C002', to: 'C003', type: 'reference' }
            ]
        }
    },

    // HumanInputComponent 数据类型
    'project_list': {
        endpoint: '/pm_v2/project_list',
        method: 'GET',
        cache: true,
        cacheTTL: 300000, // 5分钟缓存
        mockData: {
            projects: [
                { id: 'proj_001', name: '项目管理系统V2', status: 'active' },
                { id: 'proj_002', name: '统一架构重构', status: 'active' },
                { id: 'proj_003', name: '前端组件化', status: 'completed' }
            ],
            current_project: 'proj_001'
        }
    },

    'control_status': {
        endpoint: '/pm_v2/control_status',
        method: 'GET',
        cache: true,
        cacheTTL: 5000, // 5秒缓存
        mockData: {
            meeting_status: 'active',
            scanning_status: 'idle',
            project_status: 'running',
            controls_enabled: true
        }
    },

    'get_and_create_manager': {
        endpoint: '/pm_v2/get_and_create',
        method: 'POST',
        cache: false,
        mockData: null
    },

    // ProjectOutputComponent 数据类型
    'deliverables': {
        endpoint: '/pm_v2/deliverables',
        method: 'GET',
        cache: true,
        cacheTTL: 60000, // 1分钟缓存
        mockData: {
            audit_status: 'processing',
            audit_status_text: '处理中...',
            links: [],
            stats: {
                processed_docs: '3/5',
                total_constraints: '25',
                total_risks: '2',
                total_time: '150秒'
            }
        }
    }
};

// WebSocket事件映射
const PM_V2_WEBSOCKET_EVENTS = {
    // 进度更新事件
    'progress_update': ['progress', 'stage_metrics'],
    
    // 风险评估更新事件
    'risk_update': ['risk_assessment', 'health_report'],
    
    // 管理器状态更新事件
    'manager_status_update': ['manager_status', 'task_status'],
    
    // 算法日志更新事件
    'algorithm_log_update': ['algorithm_logs'],
    
    // 知识图谱更新事件
    'knowledge_graph_update': ['knowledge_graph'],
    
    // 控制状态更新事件
    'control_status_update': ['control_status'],
    
    // 交付结果更新事件
    'deliverables_update': ['deliverables']
};

// 导出配置
window.PM_V2_DATA_TYPE_MAPPING = PM_V2_DATA_TYPE_MAPPING;
window.PM_V2_WEBSOCKET_EVENTS = PM_V2_WEBSOCKET_EVENTS;
