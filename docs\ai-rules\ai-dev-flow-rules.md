# AI-DEV-FLOW Rule System: Task-Oriented Document Reading and Memory Refresh

**Document ID**: G002
**Created Date**: 2023-05-01
**Version**: 1.1
**Status**: Approved

This document defines the AI rule system that works with the AI-DEV-FLOW workflow and RIPER-5 protocol, focusing on task-oriented document reading and memory refresh. These rules guide AI on how to selectively read relevant documents based on task types and phases, refresh memory, and provide more precise support.

## 1. Rule System Overall Structure

```javascript
// AI-DEV-FLOW Rule System
{
  "version": "1.0.0",
  "name": "AI-DEV-FLOW Rule System",
  "description": "AI rule system that works with AI-DEV-FLOW workflow and RIPER-5 protocol, focusing on task-oriented document reading and memory refresh",

  // Basic settings
  "settings": {
    "language": "Chinese",
    "modeDeclaration": true,
    "defaultMode": "RESEARCH"
  },

  // Core rule components
  "components": [
    "modeAwareness",
    "taskRecognition",
    "documentReader",
    "memoryRefresh",
    "contextCompression"
  ],

  // Integration with RIPER-5 protocol
  "integration": {
    "protocol": "RIPER-5",
    "thinkingPrinciples": [
      "systemsThinking",
      "dialecticalThinking",
      "innovativeThinking",
      "criticalThinking"
    ]
  }
}
```

## 2. 文档区域划分

AI-DEV-FLOW的文档区域划分为以下几类：

### 2.1 基础文档区域（非分阶段）

这些文档在所有任务和阶段都应该被读取，提供基础上下文：

- **项目上下文**：项目的基本信息、目标和约束
- **决策日志**：重要决策及其理由
- **系统模式**：系统使用的设计模式和架构模式

### 2.2 任务导向文档区域（分阶段）

这些文档根据RIPER-5的不同阶段有选择地读取：

- **RESEARCH阶段**：
  - 开发日志：开发过程中的记录和笔记
  - 架构文档：系统架构和组件关系
  - 相关计划文档：项目计划和路线图

- **INNOVATE阶段**：
  - 提示模板：创新思考的提示和引导
  - 成功会话记录：过去成功案例的记录
  - 设计文档：设计方案和原型

- **PLAN阶段**：
  - 开发指南：`docs/guides/`
  - 相关计划文档：实施计划和任务分解
  - 提示模板：计划制定的模板和指导

- **EXECUTE阶段**：
  - 代码实现提示：编码规范和最佳实践
  - 测试要求：`docs/guides/troubleshooting.md`
  - 相关会话记录：类似实现的参考案例
  - **文档驱动执行要求**：必须有明确的计划文档支持每个执行步骤

- **REVIEW阶段**：
  - 代码审查提示：代码审查标准和检查清单
  - 最佳实践：`docs/guides/`

### 2.3 任务类型文档区域

这些文档根据任务类型（而非阶段）有选择地读取：

- **功能开发任务**：
  - 功能开发提示：功能开发的指导和最佳实践
  - 相关功能会话记录：类似功能的开发记录

- **错误修复任务**：
  - 错误修复提示：常见错误的修复方法
  - 故障排除指南：`docs/guides/troubleshooting.md`

- **代码重构任务**：
  - 重构提示：代码重构的模式和技巧
  - 系统模式：系统使用的设计模式和架构模式

### 2.4 功能文档区域

这些文档按功能组织，每个功能有专门的目录：

- **功能目录**：`docs/features/[功能ID-功能名称-日期]/`
  - README.md：功能概述和文档索引
  - requirements/：需求文档
  - design/：设计文档
  - plan/：实施计划
  - api/：API文档
  - test/：测试文档

- **功能README模板**：
  ```markdown
  # [功能名称]

  - **功能ID**: [ID]
  - **创建日期**: [YYYY-MM-DD]
  - **状态**: [计划中/进行中/已完成]
  - **负责人**: [负责人]

  ## 功能概述

  [简要描述功能的目的和主要特性]

  ## 文档索引

  ### 需求文档
  - [需求文档名称](./requirements/document.md)

  ### 设计文档
  - [设计文档名称](./design/document.md)

  ### 实施计划
  - [实施计划名称](./plan/document.md)

  ### API文档
  - [API文档名称](./api/document.md)

  ## 相关功能
  - [相关功能ID和名称](../other-feature-folder)

  ## 变更历史
  | 版本 | 日期 | 变更内容 | 变更人 |
  |-----|-----|---------|-------|
  | 1.0 | YYYY-MM-DD | 初始版本 | 作者 |
  ```

- **文档映射表**：`docs/feature-document-map.md`
  ```markdown
  # 功能文档映射表

  | 功能ID | 功能名称 | 项目代号 | 日期 | 状态 | 主目录 | 关键文档 |
  |-------|---------|---------|-----|------|-------|---------|
  | F001 | 功能一 | XKC-CORE | YYYY-MM-DD | 进行中 | [链接](./features/F001-功能一-YYYY-MM-DD) | [设计](./features/F001-功能一-YYYY-MM-DD/design/document.md), [计划](./features/F001-功能一-YYYY-MM-DD/plan/document.md) |
  | F002 | 功能二 | XKC-CORE | YYYY-MM-DD | 计划中 | [链接](./features/F002-功能二-YYYY-MM-DD) | [需求](./features/F002-功能二-YYYY-MM-DD/requirements/document.md) |
  ```

- **文档元数据标准**：
  ```markdown
  ---
  title: 文档标题
  feature_id: 功能ID (如F001)
  feature_name: 功能名称
  document_type: 文档类型 (需求/设计/计划/API)
  created_date: YYYY-MM-DD
  updated_date: YYYY-MM-DD
  status: 草稿/审核中/已批准/已过时
  version: 1.0
  authors: 作者1, 作者2
  related_docs:
    - 相关文档1的路径
    - 相关文档2的路径
  ---

  # 文档内容从这里开始
  ```

## 3. 文档更新规则

AI-DEV-FLOW工作流中的文档更新是一个重要环节，确保文档与代码保持同步，并反映最新的开发状态。

### 3.1 文档更新计划

在PLAN模式下，AI不仅需要生成实现计划，还需要生成文档更新计划。文档更新计划应包含以下内容：

```javascript
// 文档更新计划规则
{
  "name": "documentUpdatePlan",
  "description": "计划任务执行过程中需要更新的文档",

  // 文档更新项
  "updateItems": [
    {
      "documentPath": "docs/features/F001-功能名称-日期/README.md",
      "updateSection": "功能状态",
      "updateType": "修改", // 添加/修改/删除
      "updateContent": "将状态从'计划中'改为'进行中'",
      "updateReason": "功能开发已启动"
    },
    // 更多文档更新项...
  ],

  // 更新优先级
  "updatePriority": {
    "high": ["README.md", "功能状态", "API定义"],
    "medium": ["设计文档", "实施计划", "测试文档"],
    "low": ["注释", "示例代码"]
  },

  // 更新时机
  "updateTiming": {
    "immediate": ["功能状态"], // 立即更新
    "afterImplementation": ["API文档", "设计文档"], // 实现后更新
    "beforeReview": ["测试文档"] // 审查前更新
  }
}
```

### 3.2 文档更新执行

在EXECUTE模式下，AI执行实现计划的同时，也应执行文档更新计划。文档更新的执行应遵循以下规则：

1. 按照文档更新计划中的优先级和时机执行更新
2. 在Task Progress中记录文档更新的状态
3. 请求用户确认文档更新

文档更新的Task Progress记录格式如下：

```markdown
*   [DateTime]
    *   Step: Document Update - [文档路径]
    *   Modifications: [文档更新内容]
    *   Change Summary: [更新摘要]
    *   Reason: [更新原因]
    *   Blockers: [阻塞因素，如果有]
    *   User Confirmation Status: [待确认/已确认/已拒绝]
```

### 3.3 文档更新审查

在REVIEW模式下，AI不仅需要验证实现是否符合计划，还需要验证文档更新是否成功。文档更新审查应包含以下内容：

1. 计划的文档更新数量
2. 成功完成的更新数量
3. 未完成的更新列表
4. 文档更新是否符合计划

文档更新审查的格式如下：

```markdown
## 文档更新审查
- 计划的文档更新数量: [数量]
- 成功完成的更新数量: [数量]
- 未完成的更新: [列出未完成的更新，如果有]
- 文档更新符合计划: [是/否]
```

## 4. 模式感知和任务识别规则

### 4.1 模式感知规则

```javascript
// 模式感知规则
{
  "name": "modeAwareness",
  "description": "使AI能够识别当前处于RIPER-5的哪个模式，并据此读取相应文档",

  // 模式识别
  "modeRecognition": {
    "indicators": {
      "RESEARCH": [
        "分析", "调查", "了解", "查找", "探索",
        "什么是", "如何工作", "为什么"
      ],
      "INNOVATE": [
        "创新", "想法", "方案", "建议", "可能性",
        "有什么方法", "如何改进", "有没有更好的方式"
      ],
      "PLAN": [
        "计划", "步骤", "流程", "设计", "架构",
        "如何实现", "具体怎么做", "详细说明"
      ],
      "EXECUTE": [
        "实现", "编码", "开发", "写代码", "修改",
        "执行", "按照计划", "完成任务"
      ],
      "REVIEW": [
        "审查", "检查", "评估", "验证", "测试",
        "有没有问题", "是否符合要求", "质量如何"
      ]
    },

    // 模式转换
    "transitions": {
      "RESEARCH": {
        "complete": ["信息收集完成", "已经了解", "分析完毕"],
        "nextMode": "INNOVATE"
      },
      "INNOVATE": {
        "complete": ["方案确定", "创新完成", "已有解决方案"],
        "nextMode": "PLAN"
      },
      "PLAN": {
        "complete": ["计划制定完成", "步骤已明确", "设计完毕"],
        "nextMode": "EXECUTE"
      },
      "EXECUTE": {
        "complete": ["实现完成", "代码已写好", "任务已完成"],
        "nextMode": "REVIEW"
      },
      "REVIEW": {
        "complete": ["审查完成", "验证通过", "没有问题"],
        "nextMode": null
      }
    }
  },

  // 模式声明
  "modeDeclaration": {
    "format": "[MODE: {mode}]",
    "position": "beginning",
    "required": true,
    "timing": {
      "normalResponse": "在每个响应开始时声明当前模式",
      "modeTransition": "在模式转换过程中，先声明当前模式，完成转换后声明新模式",
      "decisionRequest": "在请求人类决策时，声明当前模式并说明转换条件"
    },
    "examples": {
      "normal": "[MODE: RESEARCH] 正在分析代码结构...",
      "transition": "[MODE: RESEARCH] 分析完成，准备转换到INNOVATE模式。[MODE: INNOVATE] 开始探索解决方案...",
      "decisionRequest": "[MODE: INNOVATE] 识别到多个可行解决方案，需要请求人类决策后才能转换到PLAN模式。"
    }
  },

  // AI-driven development decision rules - refer to .augment-guidelines for complete decision authority definitions
  "aiDrivenDecisionRules": {
    "note": "Complete decision authority and risk escalation rules are defined in .augment-guidelines",
    "decisionAuthority": {
      "INNOVATE": {
        "trigger": "multiple_viable_solutions_identified",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When multiple solution approaches are identified",
        "requirement": "MANDATORY: Always request human decision before proceeding to PLAN mode",
        "escalationTriggers": ["multiple viable solutions", "architectural impact", "business logic changes"],
        "description": "当识别到多个可行解决方案时，必须请求人类决策后才能转换到PLAN模式"
      },
      "PLAN": {
        "trigger": "architectural_or_business_logic_changes",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When planning involves architectural, directional, foundation-level, or business logic changes",
        "requirement": "MANDATORY: Request human guidance before proceeding to EXECUTE mode",
        "escalationTriggers": ["architectural changes", "foundation-level decisions", "high complexity"],
        "description": "当计划涉及架构、方向性、基础层或业务逻辑变更时，必须请求人类指导后才能转换到EXECUTE模式",
        "documentCompletenessRequirement": {
          "description": "Plans must be sufficiently detailed to support execution without ambiguity",
          "requiredElements": [
            "Specific file paths and modifications",
            "Detailed function signatures and logic",
            "Clear step-by-step implementation sequence",
            "Error handling strategies",
            "Testing approaches"
          ],
          "completenessCheck": "Verify plan provides enough detail for unambiguous execution"
        }
      },
      "EXECUTE": {
        "trigger": "major_plan_deviations",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When major deviations from approved plan or unexpected architectural issues occur",
        "requirement": "MANDATORY: Escalate to human decision",
        "escalationTriggers": ["major deviations from approved plan", "unexpected architectural issues"],
        "description": "当执行过程中出现重大计划偏差或意外架构问题时，必须上报人类决策",
        "documentDrivenExecution": {
          "requirement": "All execution steps must have explicit documentation support",
          "refusalConditions": [
            "Documentation is missing for requested execution steps",
            "Documentation content is incomplete or ambiguous",
            "Plan document lacks sufficient detail for implementation",
            "Conflicting information exists in documentation"
          ],
          "refusalAction": "Refuse execution and request document completion before proceeding"
        }
      }
    }
  }
}
```

### 4.2 Task Recognition Rules

```javascript
// Task recognition rules
{
  "name": "taskRecognition",
  "description": "Identify user request task types and read corresponding documents accordingly",

  // Task types
  "taskTypes": {
    "featureDevelopment": {
      "indicators": ["new feature", "implement feature", "add feature", "develop", "create"],
      "relevantDocs": [
        "docs/ai-dev/prompts/templates/feature-dev.md",
        "docs/ai-dev/prompts/sessions/",
        "docs/guides/workflow.md"
      ]
    },
    "bugFix": {
      "indicators": ["fix", "error", "bug", "issue", "not working", "failure"],
      "relevantDocs": [
        "docs/ai-dev/prompts/templates/bug-fix.md",
        "docs/guides/troubleshooting.md",
        "docs/ai-dev/prompts/sessions/"
      ]
    },
    "refactoring": {
      "indicators": ["refactor", "optimize", "improve", "cleanup", "rewrite"],
      "relevantDocs": [
        "docs/ai-dev/prompts/templates/refactoring.md",
        "docs/ai-dev/context/system-patterns.md",
        "docs/guides/workflow.md"
      ]
    },
    "codeReview": {
      "indicators": ["review", "check", "evaluate", "code quality", "best practices"],
      "relevantDocs": [
        "docs/ai-dev/prompts/templates/code-review.md",
        "docs/ai-dev/context/system-patterns.md",
        "docs/guides/troubleshooting.md"
      ]
    },
    "documentation": {
      "indicators": ["documentation", "explain", "comment", "describe"],
      "relevantDocs": [
        "docs/guides/",
        "docs/ai-dev/context/project-context.md",
        "docs/ai-dev/context/decision-log.md"
      ]
    },
    "testing": {
      "indicators": ["test", "testing", "parameterized testing", "AI testing", "test strategy", "test architecture", "risk analysis", "STRIDE", "FMEA", "attack tree", "business logic testing", "web layer testing", "end-to-end testing", "security testing", "performance testing", "test parameters", "test configuration", "test iteration", "human-AI interaction testing"],
      "relevantDocs": [
        "docs/common/best-practices/testing/testing-index.json"
      ]
    }
  },

  // Task complexity
  "taskComplexity": {
    "simple": {
      "indicators": ["simple", "quick", "small change", "minor adjustment"],
      "docReadDepth": "shallow"
    },
    "medium": {
      "indicators": ["medium", "normal", "standard"],
      "docReadDepth": "normal"
    },
    "complex": {
      "indicators": ["complex", "difficult", "major change", "important"],
      "docReadDepth": "deep"
    }
  }
}
```

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2023-05-01 | 初始版本 | AI助手 |
| 1.1 | 2025-01-XX | 统一决策权限表述格式，实施分层语言策略（技术规则用英文，用户说明用中文） | AI助手 |