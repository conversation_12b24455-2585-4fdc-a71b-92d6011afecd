#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计文档扫描程序
目标：自动检查设计文档合规性，生成结构化改进建议
基于：元提示词逆向分析 + 行业标准补充
"""

import os
import re
import json
import yaml
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import argparse

@dataclass
class ScanResult:
    """扫描结果数据结构"""
    file_path: str
    overall_score: float
    structure_score: float
    content_score: float
    ai_friendly_score: float
    executable_score: float
    issues: List[Dict]
    suggestions: List[Dict]
    
class DesignDocumentScanner:
    """设计文档扫描器"""
    
    def __init__(self):
        self.standards = self._load_standards()
        self.weights = {
            'structure': 0.4,
            'content': 0.3, 
            'ai_friendly': 0.2,
            'executable': 0.1
        }
    
    def _load_standards(self) -> Dict:
        """加载扫描标准"""
        return {
            'required_sections': [
                '文档信息', '核心定位', '架构范围边界', '技术基石',
                '设计哲学', '技术实现', '质量监控', '风险评估'
            ],
            'metadata_fields': [
                '项目名称', '文档类型', '复杂度等级', '技术栈', '版本'
            ],
            'tech_stack_patterns': [
                r'Spring Boot \d+\.\d+\.\d+',
                r'Java \d+',
                r'Maven \d+\.\d+\.\d+',
                r'PostgreSQL \d+\.\d+'
            ],
            'path_patterns': [
                r'src/main/java/[\w/]+\.java',
                r'src/main/resources/[\w/]+\.(yml|yaml|properties)',
                r'pom\.xml'
            ]
        }
    
    def scan_document(self, file_path: str) -> ScanResult:
        """扫描单个文档"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 执行各项检查
            structure_result = self._check_structure(content)
            content_result = self._check_content(content)
            ai_friendly_result = self._check_ai_friendly(content)
            executable_result = self._check_executable(content)
            
            # 计算综合得分
            overall_score = (
                structure_result['score'] * self.weights['structure'] +
                content_result['score'] * self.weights['content'] +
                ai_friendly_result['score'] * self.weights['ai_friendly'] +
                executable_result['score'] * self.weights['executable']
            )
            
            # 收集所有问题和建议
            all_issues = (
                structure_result['issues'] + 
                content_result['issues'] + 
                ai_friendly_result['issues'] + 
                executable_result['issues']
            )
            
            all_suggestions = (
                structure_result['suggestions'] + 
                content_result['suggestions'] + 
                ai_friendly_result['suggestions'] + 
                executable_result['suggestions']
            )
            
            return ScanResult(
                file_path=file_path,
                overall_score=overall_score,
                structure_score=structure_result['score'],
                content_score=content_result['score'],
                ai_friendly_score=ai_friendly_result['score'],
                executable_score=executable_result['score'],
                issues=all_issues,
                suggestions=all_suggestions
            )
            
        except Exception as e:
            return ScanResult(
                file_path=file_path,
                overall_score=0.0,
                structure_score=0.0,
                content_score=0.0,
                ai_friendly_score=0.0,
                executable_score=0.0,
                issues=[{'type': 'error', 'message': f'扫描失败: {str(e)}'}],
                suggestions=[]
            )
    
    def _check_structure(self, content: str) -> Dict:
        """检查文档结构完整性"""
        issues = []
        suggestions = []
        score = 100.0
        
        # 检查必需章节
        missing_sections = []
        for section in self.standards['required_sections']:
            if not re.search(rf'#{1,4}\s*{re.escape(section)}', content, re.IGNORECASE):
                missing_sections.append(section)
        
        if missing_sections:
            score -= len(missing_sections) * 15
            issues.append({
                'type': 'structure',
                'severity': 'high',
                'message': f'缺少必需章节: {", ".join(missing_sections)}'
            })
            suggestions.append({
                'type': 'structure',
                'priority': 'high',
                'suggestion': f'添加缺失章节: {", ".join(missing_sections)}',
                'template': self._get_section_template(missing_sections[0]) if missing_sections else ''
            })
        
        # 检查文档元数据
        metadata_section = re.search(r'## 文档信息.*?(?=##|\Z)', content, re.DOTALL)
        if metadata_section:
            metadata_content = metadata_section.group(0)
            missing_fields = []
            for field in self.standards['metadata_fields']:
                if not re.search(rf'-\s*\*\*{re.escape(field)}\*\*:', metadata_content):
                    missing_fields.append(field)
            
            if missing_fields:
                score -= len(missing_fields) * 5
                issues.append({
                    'type': 'metadata',
                    'severity': 'medium',
                    'message': f'文档元数据缺少字段: {", ".join(missing_fields)}'
                })
                suggestions.append({
                    'type': 'metadata',
                    'priority': 'medium',
                    'suggestion': f'在文档信息章节添加: {", ".join(missing_fields)}',
                    'template': '\n'.join([f'- **{field}**: [请填写{field}]' for field in missing_fields])
                })
        else:
            score -= 20
            issues.append({
                'type': 'structure',
                'severity': 'high',
                'message': '缺少文档信息章节'
            })
        
        # 检查标题层次
        headers = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
        if headers:
            max_level = max(len(h[0]) for h in headers)
            if max_level > 4:
                score -= 5
                issues.append({
                    'type': 'structure',
                    'severity': 'low',
                    'message': f'标题层次过深({max_level}级)，建议不超过4级'
                })
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _check_content(self, content: str) -> Dict:
        """检查内容完整性"""
        issues = []
        suggestions = []
        score = 100.0
        
        # 检查技术栈表格
        if not re.search(r'\|\s*技术类别\s*\|\s*技术选型\s*\|\s*版本要求\s*\|', content):
            score -= 15
            issues.append({
                'type': 'content',
                'severity': 'high',
                'message': '缺少技术栈对比表格'
            })
            suggestions.append({
                'type': 'content',
                'priority': 'high',
                'suggestion': '添加技术栈对比表格',
                'template': '''| 技术类别 | 技术选型 | 版本要求 | 选择原因 |
|---------|---------|---------|---------|
| 框架 | Spring Boot | 2.7.8 | 稳定性和生态完整性 |'''
            })
        
        # 检查性能指标
        performance_keywords = ['性能', '响应时间', 'TPS', 'QPS', '吞吐量']
        has_performance = any(keyword in content for keyword in performance_keywords)
        if not has_performance:
            score -= 10
            issues.append({
                'type': 'content',
                'severity': 'medium',
                'message': '缺少具体的性能指标要求'
            })
        
        # 检查风险评估
        if not re.search(r'P[012]', content):
            score -= 10
            issues.append({
                'type': 'content',
                'severity': 'medium',
                'message': '缺少P0/P1/P2风险等级分类'
            })
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _check_ai_friendly(self, content: str) -> Dict:
        """检查AI友好性"""
        issues = []
        suggestions = []
        score = 100.0
        
        # 检查技术版本精确性
        vague_versions = re.findall(r'(最新版本|latest|当前版本)', content, re.IGNORECASE)
        if vague_versions:
            score -= len(vague_versions) * 10
            issues.append({
                'type': 'ai_friendly',
                'severity': 'high',
                'message': f'发现模糊版本描述: {", ".join(set(vague_versions))}'
            })
            suggestions.append({
                'type': 'ai_friendly',
                'priority': 'high',
                'suggestion': '将模糊版本描述替换为精确版本号',
                'example': '"最新版本" → "Spring Boot 2.7.8"'
            })
        
        # 检查文件路径完整性
        incomplete_paths = re.findall(r'配置文件|源码文件|测试文件', content)
        if incomplete_paths:
            score -= len(incomplete_paths) * 5
            issues.append({
                'type': 'ai_friendly',
                'severity': 'medium',
                'message': '发现不完整的文件路径描述'
            })
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _check_executable(self, content: str) -> Dict:
        """检查可执行性"""
        issues = []
        suggestions = []
        score = 100.0
        
        # 检查验证点
        verification_patterns = [
            r'验证[:：]',
            r'检查[:：]',
            r'确认[:：]',
            r'测试[:：]'
        ]
        
        verification_count = sum(len(re.findall(pattern, content)) for pattern in verification_patterns)
        if verification_count < 3:
            score -= 15
            issues.append({
                'type': 'executable',
                'severity': 'medium',
                'message': f'验证点数量不足({verification_count}个)，建议至少3个'
            })
        
        # 检查回滚方案
        if not re.search(r'回滚|rollback', content, re.IGNORECASE):
            score -= 10
            issues.append({
                'type': 'executable',
                'severity': 'medium',
                'message': '缺少回滚方案说明'
            })
        
        return {
            'score': max(0, score),
            'issues': issues,
            'suggestions': suggestions
        }
    
    def _get_section_template(self, section_name: str) -> str:
        """获取章节模板"""
        templates = {
            '文档信息': '''## 文档信息
- **项目名称**: [请填写项目名称]
- **文档类型**: [架构设计/技术实现/集成方案]
- **复杂度等级**: [L1简单/L2中等/L3复杂]
- **技术栈**: [具体版本的技术栈清单]
- **创建日期**: [YYYY-MM-DD]
- **版本**: [语义化版本号]''',
            
            '核心定位': '''## 核心定位
### 项目目标
- [具体、可量化的目标描述]

### 核心价值
- [明确的价值主张]

### 技术定位
- [在技术生态中的定位]''',
            
            '技术基石': '''## 技术基石
### 核心技术栈
| 技术类别 | 技术选型 | 版本要求 | 选择原因 |
|---------|---------|---------|---------|
| 框架 | [具体框架] | [精确版本] | [技术决策依据] |

### 技术约束
- [强制性技术要求]
- [兼容性约束]
- [性能要求]'''
        }
        return templates.get(section_name, f'## {section_name}\n[请添加{section_name}相关内容]')
    
    def generate_report(self, results: List[ScanResult], output_path: str):
        """生成扫描报告"""
        report = {
            'scan_summary': {
                'total_files': len(results),
                'average_score': sum(r.overall_score for r in results) / len(results) if results else 0,
                'high_quality_files': len([r for r in results if r.overall_score >= 80]),
                'needs_improvement': len([r for r in results if r.overall_score < 60])
            },
            'detailed_results': [asdict(result) for result in results],
            'improvement_priorities': self._generate_improvement_priorities(results)
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
    
    def _generate_improvement_priorities(self, results: List[ScanResult]) -> List[Dict]:
        """生成改进优先级建议"""
        all_issues = []
        for result in results:
            for issue in result.issues:
                issue['file'] = result.file_path
                all_issues.append(issue)
        
        # 按严重程度和类型分组
        high_priority = [i for i in all_issues if i.get('severity') == 'high']
        medium_priority = [i for i in all_issues if i.get('severity') == 'medium']
        
        return {
            'immediate_action': high_priority[:5],  # 前5个高优先级问题
            'planned_improvement': medium_priority[:10],  # 前10个中优先级问题
            'overall_recommendations': [
                '优先解决结构完整性问题，确保必需章节完整',
                '补充技术栈版本信息，提高AI解析精确度',
                '添加具体的验证点和回滚方案',
                '完善性能指标和风险评估内容'
            ]
        }

def main():
    parser = argparse.ArgumentParser(description='设计文档扫描程序')
    parser.add_argument('input_path', help='输入文件或目录路径')
    parser.add_argument('-o', '--output', default='scan_report.json', help='输出报告路径')
    parser.add_argument('-r', '--recursive', action='store_true', help='递归扫描子目录')
    
    args = parser.parse_args()
    
    scanner = DesignDocumentScanner()
    results = []
    
    input_path = Path(args.input_path)
    
    if input_path.is_file():
        if input_path.suffix.lower() == '.md':
            results.append(scanner.scan_document(str(input_path)))
    elif input_path.is_dir():
        pattern = '**/*.md' if args.recursive else '*.md'
        for md_file in input_path.glob(pattern):
            results.append(scanner.scan_document(str(md_file)))
    
    if results:
        scanner.generate_report(results, args.output)
        print(f'扫描完成，共处理 {len(results)} 个文件')
        print(f'平均得分: {sum(r.overall_score for r in results) / len(results):.1f}')
        print(f'报告已保存到: {args.output}')
    else:
        print('未找到可扫描的Markdown文件')

if __name__ == '__main__':
    main()
