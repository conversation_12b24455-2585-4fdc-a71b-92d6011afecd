# 关键词索引：PostgreSQL

## 基本信息

- **关键词**: PostgreSQL
- **类型**: 数据库技术
- **相关功能**: F003-PostgreSQL迁移
- **相关项目**: xkongcloud-business-internal-core, xkongcloud-commons-uid

## 技术概述

PostgreSQL是一个功能强大的开源对象关系数据库系统，在XKongCloud项目中用于替代Cassandra，提供更高的数据一致性、查询灵活性和开发效率。项目使用PostgreSQL 17.4版本，结合Spring Data JPA和Hibernate实现对象关系映射。

## 关键特性

1. **ACID事务支持**: PostgreSQL提供完全符合ACID属性的事务支持，确保数据一致性和完整性
2. **多Schema组织**: 项目采用多Schema组织方式，按业务领域和功能类型划分Schema
3. **丰富的数据类型**: 支持JSON、数组、地理信息等高级数据类型，满足复杂业务需求
4. **强大的索引功能**: 支持多种索引类型，提高查询性能
5. **存储过程和触发器**: 支持服务器端编程，实现复杂业务逻辑

## 配置与使用

### 连接池配置

项目使用HikariCP作为数据库连接池，主要配置参数包括:

```java
HikariConfig config = new HikariConfig();
config.setJdbcUrl(getRequiredParam("postgresql.url"));
config.setUsername(getRequiredParam("postgresql.username"));
config.setPassword(getRequiredParam("postgresql.password"));
config.setMaximumPoolSize(getIntParam("postgresql.pool.max-size"));
config.setMinimumIdle(getIntParam("postgresql.pool.min-idle"));
config.setConnectionTimeout(getLongParam("postgresql.pool.connection-timeout"));
config.setIdleTimeout(getLongParam("postgresql.pool.idle-timeout"));
config.setMaxLifetime(getLongParam("postgresql.pool.max-lifetime"));
```

### JPA和Hibernate配置

```java
Properties properties = new Properties();
properties.setProperty("hibernate.hbm2ddl.auto", getRequiredParam("postgresql.ddl-auto"));
// 启用JDBC元数据访问，让Hibernate自动检测PostgreSQL方言
// 这样可以消除hibernate.dialect的弃用警告，同时保持功能完整性
properties.setProperty("hibernate.boot.allow_jdbc_metadata_access", "true");
properties.setProperty("hibernate.show_sql", getParam("postgresql.show-sql", "false"));
properties.setProperty("hibernate.format_sql", getParam("postgresql.format-sql", "false"));
properties.setProperty("hibernate.jdbc.batch_size", getParam("postgresql.batch-size", "30"));
properties.setProperty("hibernate.jdbc.fetch_size", getParam("postgresql.fetch-size", "100"));
properties.setProperty("hibernate.physical_naming_strategy", "com.xkongcloud.core.config.PostgreSQLNamingStrategy");
```

### Schema命名规范

项目采用以下Schema命名规范:

1. **业务Schema**: `<业务领域>_<可选子域>`
   - 例如: `user_management`, `identity_core`, `payment_processing`

2. **基础设施Schema**: `infra_<组件类型>`
   - 例如: `infra_uid`, `infra_audit`, `infra_cache`

3. **通用功能Schema**: `common_<功能类型>`
   - 例如: `common_config`, `common_logging`, `common_security`

### 实体类示例

```java
@Entity
@Table(name = "user", schema = "user_management")
public class User {
    @Id
    private Long userId;
    
    @Column(name = "username", nullable = false, length = 64)
    private String username;
    
    @Column(name = "email", nullable = false, length = 128)
    private String email;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status;
    
    @Column(name = "regist_time", nullable = false)
    private LocalDateTime registTime;
    
    // Getter和Setter方法...
}
```

## 最佳实践

### 强制约束

1. **实体类Schema指定**
   - 所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
   - 不允许依赖任何默认Schema设置

2. **命名策略**
   - 使用自定义的PostgreSQLNamingStrategy将Java驼峰命名转换为PostgreSQL下划线命名

3. **参数验证**
   - 所有从KV参数服务获取的参数必须进行非空验证
   - 缺少必需参数时应用应无法启动，并显示明确的错误信息

### 性能优化

1. **索引策略**
   - 为常用查询条件创建适当的索引
   - 使用单列索引和复合索引提高查询性能

2. **批量操作**
   - 使用JPA和JdbcTemplate的批量操作功能提高性能
   - 定期清理EntityManager缓存，避免内存溢出

3. **查询优化**
   - 使用预编译语句减少SQL解析开销
   - 使用分页查询避免大结果集返回
   - 使用投影查询只获取需要的字段

4. **缓存策略**
   - 使用JPA二级缓存减少数据库访问
   - 缓存常用查询结果
   - 使用Spring Cache抽象实现应用级缓存

## 相关注意事项

1. **N+1查询问题**
   - 通过适当的关联抓取策略避免N+1查询问题
   - 使用JOIN FETCH或EntityGraph预加载关联实体

2. **大事务**
   - 避免长时间运行的大事务，可能导致锁竞争
   - 将大事务拆分为多个小事务

3. **连接池设置**
   - 根据实际负载调整连接池参数
   - 避免连接池过大或过小

4. **DDL策略**
   - 开发环境可以使用`create`或`update`
   - 测试环境可以使用`create-drop`
   - 生产环境必须使用`validate`或`none`

## 相关资源和文档

1. **PostgreSQL官方文档**: [https://www.postgresql.org/docs/17/](https://www.postgresql.org/docs/17/)
2. **Spring Data JPA文档**: [https://docs.spring.io/spring-data/jpa/docs/current/reference/html/](https://docs.spring.io/spring-data/jpa/docs/current/reference/html/)
3. **Hibernate文档**: [https://hibernate.org/orm/documentation/](https://hibernate.org/orm/documentation/)
4. **HikariCP文档**: [https://github.com/brettwooldridge/HikariCP](https://github.com/brettwooldridge/HikariCP)

## 内部项目参考

1. [PostgreSQL迁移功能](docs/ai-memory/L3-index/feature-index/by-project/xkc-core/F003-PostgreSQL迁移.md)
2. [PostgreSQL技术栈上下文](docs/ai-memory/L2-context/tech-stack/postgresql-stack.md)
3. [Schema规划指南](docs/common/middleware/postgresql/schema-planning-guide.md)
4. [数据库任务上下文](docs/ai-memory/L2-context/task-types/database-tasks.md)
5. [全局硬约束](docs/ai-memory/L1-core/global-constraints.md) 