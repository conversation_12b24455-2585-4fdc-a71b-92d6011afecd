# PM V2统一架构实际代码实现示例

## 概述

本文档提供PM V2统一架构重构的实际代码实现示例，展示如何将现有PM V2代码适配到统一架构。

---

## 1. PM V2统一架构初始化脚本

### 文件：`pm_v2_unified_init.js`

```javascript
/**
 * PM V2统一架构初始化脚本
 * 基于现有的AppManager实现
 */

// PM V2数据类型配置
const PM_V2_DATA_CONFIG = {
    // 数据类型映射
    dataTypes: {
        'progress': '/api/v2/projects/{projectId}/progress',
        'risk_assessment': '/api/v2/projects/{projectId}/risk-assessment',
        'manager_status': '/api/v2/projects/{projectId}/manager-status',
        'algorithm_logs': '/api/v2/projects/{projectId}/algorithm-logs',
        'constraints': '/api/v2/projects/{projectId}/constraints',
        'knowledge_graph': '/api/v2/projects/{projectId}/knowledge-graph',
        'control_status': '/api/v2/projects/{projectId}/control-status',
        'deliverables': '/api/v2/projects/{projectId}/deliverables'
    },
    
    // WebSocket事件映射
    websocketEvents: {
        'stage_progress_update': 'progress',
        'risk_assessment_update': 'risk_assessment',
        'manager_status_update': 'manager_status',
        'algorithm_log_entry': 'algorithm_logs',
        'constraint_created': 'constraints',
        'constraint_updated': 'constraints',
        'knowledge_graph_update': 'knowledge_graph',
        'deliverable_ready': 'deliverables'
    },
    
    // 缓存策略
    cacheStrategies: {
        'progress': { ttl: 30000 },
        'risk_assessment': { ttl: 60000 },
        'manager_status': { ttl: 10000 },
        'algorithm_logs': { ttl: 5000 },
        'constraints': { ttl: 120000 },
        'knowledge_graph': { ttl: 120000 },
        'control_status': { ttl: 30000 },
        'deliverables': { ttl: 300000 }
    }
};

// 应用初始化函数
async function initializePMV2UnifiedArchitecture() {
    try {
        // 1. 获取项目ID
        const projectId = getCurrentProjectId();
        console.log(`初始化PM V2统一架构，项目ID: ${projectId}`);
        
        // 2. 扩展DataManager配置
        extendDataManagerConfig();
        
        // 3. 创建应用管理器
        const appManager = new AppManager(projectId, {
            errorHandler: handlePMV2Error,
            debug: true
        });
        
        // 4. 注册PM V2组件
        registerPMV2Components(appManager);
        
        // 5. 初始化应用
        await appManager.init();
        
        // 6. 设置全局引用
        window.pmV2AppManager = appManager;
        
        // 7. 设置兼容性桥接
        setupCompatibilityBridge();
        
        console.log('✅ PM V2统一架构初始化成功');
        showInitializationSuccess();
        
        return appManager;
        
    } catch (error) {
        console.error('❌ PM V2统一架构初始化失败:', error);
        handleInitializationError(error);
        throw error;
    }
}

// 扩展DataManager配置
function extendDataManagerConfig() {
    // 扩展数据类型映射
    if (window.DataManager && window.DataManager.prototype.registerDataTypes) {
        const originalRegister = window.DataManager.prototype.registerDataTypes;
        window.DataManager.prototype.registerDataTypes = function() {
            originalRegister.call(this);
            
            // 添加PM V2数据类型
            Object.entries(PM_V2_DATA_CONFIG.dataTypes).forEach(([type, endpoint]) => {
                this.registerDataType(type, endpoint);
            });
            
            // 添加WebSocket事件映射
            Object.assign(this.websocketEventMapping, PM_V2_DATA_CONFIG.websocketEvents);
            
            // 添加缓存策略
            Object.assign(this.cacheStrategies, PM_V2_DATA_CONFIG.cacheStrategies);
        };
    }
}

// 注册PM V2组件
function registerPMV2Components(appManager) {
    const components = [
        { type: 'progress', containerId: 'progress-area', ComponentClass: PMV2ProgressComponent },
        { type: 'risk', containerId: 'risk-area', ComponentClass: PMV2RiskComponent },
        { type: 'manager', containerId: 'manager-area', ComponentClass: PMV2ManagerComponent },
        { type: 'algorithm', containerId: 'algorithm-area', ComponentClass: PMV2AlgorithmComponent },
        { type: 'constraint', containerId: 'constraint-area', ComponentClass: PMV2ConstraintComponent },
        { type: 'knowledge', containerId: 'knowledge-area', ComponentClass: PMV2KnowledgeComponent },
        { type: 'control', containerId: 'control-area', ComponentClass: PMV2ControlComponent },
        { type: 'deliverables', containerId: 'deliverables-area', ComponentClass: PMV2DeliverablesComponent }
    ];
    
    appManager.registerComponents(components);
}

// 设置兼容性桥接
function setupCompatibilityBridge() {
    // 桥接传统函数到统一架构
    window.showConstraintDetail = function(constraintId) {
        const constraintComponent = window.pmV2AppManager.getComponent('constraint');
        if (constraintComponent && constraintComponent.selectConstraint) {
            constraintComponent.selectConstraint(constraintId);
        }
    };
    
    window.makeDecision = function(decision) {
        const controlComponent = window.pmV2AppManager.getComponent('control');
        if (controlComponent && controlComponent.makeDecision) {
            controlComponent.makeDecision(decision);
        }
    };
    
    // 其他桥接函数...
}

// 获取项目ID
function getCurrentProjectId() {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('project_id');
    
    if (projectId) {
        localStorage.setItem('current_project_id', projectId);
        return projectId;
    }
    
    // 从localStorage获取
    const storedProjectId = localStorage.getItem('current_project_id');
    if (storedProjectId) {
        return storedProjectId;
    }
    
    // 默认项目ID
    return 'pm_v2_default';
}

// 错误处理
function handlePMV2Error(error) {
    console.error('PM V2应用错误:', error);
    
    // 可以添加错误上报逻辑
    if (window.errorReporting) {
        window.errorReporting.report(error);
    }
    
    // 显示用户友好的错误信息
    showErrorNotification(error);
}

function handleInitializationError(error) {
    console.log('降级到传统模式...');
    
    // 显示降级提示
    showDegradationNotification();
    
    // 可以尝试重新初始化传统模式
    if (window.initializeTraditionalPMV2) {
        window.initializeTraditionalPMV2();
    }
}

// UI提示函数
function showInitializationSuccess() {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 1rem;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 0.9rem;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <span>✅</span>
            <span>PM V2统一架构已启用</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

function showErrorNotification(error) {
    // 实现错误通知UI
}

function showDegradationNotification() {
    // 实现降级通知UI
}

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 检查是否已经有传统模式运行
    if (window.pmV2TraditionalMode) {
        console.log('检测到传统模式，跳过统一架构初始化');
        return;
    }
    
    try {
        await initializePMV2UnifiedArchitecture();
    } catch (error) {
        // 初始化失败，不阻止页面正常运行
        console.warn('统一架构初始化失败，页面将以传统模式运行');
    }
});

// 导出初始化函数供手动调用
window.initializePMV2UnifiedArchitecture = initializePMV2UnifiedArchitecture;
```

---

## 2. PM V2风险评估组件实现示例

### 基于现有BaseComponent的完整实现

```javascript
/**
 * PM V2风险评估组件
 * 基于统一架构BaseComponent实现
 * 复用现有的renderRiskAssessment逻辑
 */
class PMV2RiskComponent extends BaseComponent {
    constructor(containerId, dataManager, config) {
        super(containerId, dataManager, config);
        
        // 组件特定配置
        this.config = {
            refreshInterval: 60000,  // 1分钟刷新一次
            animationDuration: 800,
            showHealthReport: true,
            ...config
        };
        
        // 组件状态
        this.state = {
            lastReliabilityScore: null,
            isAnimating: false
        };
    }
    
    // 必须实现：声明需要的数据类型
    getDataTypes() {
        return ['risk_assessment'];
    }
    
    // 必须实现：渲染组件
    render() {
        const riskData = this.getData('risk_assessment');
        
        if (!riskData) {
            this.renderEmptyState();
            return;
        }
        
        // 复用现有的HTML结构，保持CSS兼容
        this.container.innerHTML = `
            <div class="area-title">⚠️ 风险评估</div>
            <div class="area-content">
                ${this.renderReliabilityScore(riskData.reliability_score)}
                ${this.renderRiskList(riskData.risks)}
                ${this.renderHealthReport(riskData.health_report)}
            </div>
        `;
        
        // 绑定事件
        this.bindRiskEvents();
        
        // 启动动画
        this.animateReliabilityScore(riskData.reliability_score);
    }
    
    // 可选重写：数据更新处理
    onDataUpdate(dataType, data, oldData) {
        if (dataType === 'risk_assessment') {
            console.log('Risk assessment data updated:', data);
            
            // 检查可靠性评分是否变化
            if (oldData && data.reliability_score !== oldData.reliability_score) {
                this.state.lastReliabilityScore = oldData.reliability_score;
                console.log(`可靠性评分变化: ${oldData.reliability_score}% → ${data.reliability_score}%`);
            }
            
            // 重新渲染
            this.render();
        }
    }
    
    // 可选重写：事件绑定
    bindEvents() {
        // 基类的事件绑定
        super.bindEvents();
        
        // 组件特定的事件绑定
        this.bindRiskEvents();
    }
    
    // 可选重写：初始化完成
    onInitialized() {
        console.log('Risk component initialized');
        
        // 启动自动刷新
        if (this.config.refreshInterval > 0) {
            this.startAutoRefresh();
        }
    }
    
    // 可选重写：销毁清理
    onDestroyed() {
        console.log('Risk component destroyed');
        
        // 清理定时器
        this.stopAutoRefresh();
    }
    
    // === 业务逻辑方法 (复用现有代码) ===
    
    renderReliabilityScore(score) {
        // 直接复制project_manager_v2_app.js中的相关代码
        const offset = 226 - (226 * score / 100);
        const color = score >= 80 ? 'success' : score >= 60 ? 'warning' : 'danger';
        
        return `
            <div class="circular-progress" data-score="${score}">
                <svg>
                    <circle class="progress-circle progress-bg" cx="40" cy="40" r="36"></circle>
                    <circle class="progress-circle progress-bar-circle ${color}" cx="40" cy="40" r="36" 
                            style="stroke-dashoffset: ${offset}; transition: stroke-dashoffset ${this.config.animationDuration}ms ease;"></circle>
                </svg>
                <div class="progress-text">
                    <div class="progress-value">${score}%</div>
                    <div class="progress-label">可靠性</div>
                </div>
            </div>
        `;
    }
    
    renderRiskList(risks) {
        if (!risks || risks.length === 0) {
            return '<div class="no-risks">✅ 暂无风险</div>';
        }
        
        return `
            <div class="risk-list">
                ${risks.map(risk => `
                    <div class="risk-item ${risk.level.toLowerCase()}" onclick="this.showRiskDetail('${risk.id}')">
                        <span class="risk-badge ${risk.level.toLowerCase()}">${risk.level}</span>
                        <span class="risk-message">${risk.message}</span>
                        <span class="risk-time">${this.formatTime(risk.timestamp)}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderHealthReport(healthReport) {
        if (!healthReport || !this.config.showHealthReport) {
            return '';
        }
        
        return `
            <div class="health-report">
                <div class="report-title">📊 健康报告</div>
                <div class="report-summary">${healthReport.summary}</div>
                <div class="report-actions">
                    <a href="${healthReport.url}" class="report-link" target="_blank">查看详细报告</a>
                </div>
            </div>
        `;
    }
    
    // === 交互处理方法 ===
    
    bindRiskEvents() {
        // 风险项点击事件
        this.container.addEventListener('click', (e) => {
            if (e.target.closest('.risk-item')) {
                const riskItem = e.target.closest('.risk-item');
                const riskId = riskItem.dataset.riskId;
                this.showRiskDetail(riskId);
            }
        });
        
        // 健康报告链接点击事件
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('report-link')) {
                this.trackHealthReportView();
            }
        });
    }
    
    showRiskDetail(riskId) {
        console.log('显示风险详情:', riskId);
        
        // 可以触发自定义事件，让其他组件响应
        this.triggerEvent('risk-detail-requested', { riskId });
        
        // 或者直接显示详情
        // this.renderRiskDetailModal(riskId);
    }
    
    trackHealthReportView() {
        console.log('用户查看了健康报告');
        
        // 可以发送统计数据
        if (window.analytics) {
            window.analytics.track('health_report_viewed', {
                component: 'risk_assessment',
                timestamp: new Date().toISOString()
            });
        }
    }
    
    // === 动画和效果 ===
    
    animateReliabilityScore(newScore) {
        if (this.state.isAnimating) return;
        
        const progressCircle = this.container.querySelector('.progress-bar-circle');
        if (!progressCircle) return;
        
        this.state.isAnimating = true;
        
        // 如果有之前的分数，从之前的分数开始动画
        const startScore = this.state.lastReliabilityScore || 0;
        const endScore = newScore;
        const duration = this.config.animationDuration;
        
        this.animateScore(progressCircle, startScore, endScore, duration, () => {
            this.state.isAnimating = false;
        });
    }
    
    animateScore(element, start, end, duration, callback) {
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeProgress = this.easeOutCubic(progress);
            const currentScore = start + (end - start) * easeProgress;
            
            // 更新圆形进度条
            const offset = 226 - (226 * currentScore / 100);
            element.style.strokeDashoffset = offset;
            
            // 更新文字
            const valueElement = this.container.querySelector('.progress-value');
            if (valueElement) {
                valueElement.textContent = Math.round(currentScore) + '%';
            }
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                callback && callback();
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
    
    // === 工具方法 ===
    
    formatTime(timestamp) {
        if (!timestamp) return '';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return date.toLocaleDateString();
        }
    }
    
    startAutoRefresh() {
        this.stopAutoRefresh(); // 确保没有重复的定时器
        
        this.refreshTimer = setInterval(async () => {
            try {
                await this.dataManager.fetchData('risk_assessment', { refresh: true });
            } catch (error) {
                console.error('Auto refresh failed:', error);
            }
        }, this.config.refreshInterval);
    }
    
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }
    
    triggerEvent(eventName, detail) {
        const event = new CustomEvent(eventName, {
            detail: {
                ...detail,
                componentId: this.containerId,
                timestamp: new Date().toISOString()
            }
        });
        window.dispatchEvent(event);
    }
}

// 导出组件类
window.PMV2RiskComponent = PMV2RiskComponent;
```

这个实现示例展示了：

1. **完整的BaseComponent继承**: 利用统一架构的所有特性
2. **现有代码复用**: 直接复制renderRiskAssessment的HTML生成逻辑
3. **增强的交互**: 添加动画、事件处理、自动刷新等功能
4. **向后兼容**: 保持HTML结构和CSS类名不变
5. **错误处理**: 完善的错误处理和状态管理

这样的实现方式确保了重构的平滑进行，同时获得了统一架构的所有优势。
