/**
 * 进度监控组件 - 负责显示项目进度、阶段零指标和关键指标
 */
class ProgressComponent extends BaseComponent {
    constructor(containerId, dataManager, config = {}) {
        super(containerId, dataManager, config);
        
        // 组件特定的配置
        this.animationDuration = config.animationDuration || 800;
        this.updateDebounceDelay = config.updateDebounceDelay || 300;
        
        // 防抖的更新方法
        this.debouncedUpdate = this.debounce(this.updateDisplay.bind(this), this.updateDebounceDelay);
    }
    
    /**
     * 获取组件需要的数据类型
     */
    getDataTypes() {
        return ['progress'];
    }
    
    /**
     * 获取数据参数
     */
    getDataParams(dataType) {
        if (dataType === 'progress') {
            return {
                projectId: this.dataManager.projectId
            };
        }
        return {};
    }
    
    /**
     * 数据更新处理
     */
    onDataUpdate(dataType, data, oldData) {
        if (dataType === 'progress') {
            console.log('Progress data updated:', data);
            this.debouncedUpdate(data, oldData);
        }
    }
    
    /**
     * 渲染组件
     */
    render() {
        const progressData = this.getData('progress');
        
        if (!progressData) {
            this.renderEmptyState();
            return;
        }
        
        this.container.innerHTML = `
            <div class="progress-monitoring-container">
                <!-- 四阶段流程进度 -->
                <div class="stage-progress-section">
                    <div class="section-title">
                        <span>📊</span>
                        <span>V4.2四阶段流程进度</span>
                    </div>
                    <div id="stage-progress-${this.containerId}" class="stage-progress"></div>
                </div>
                
                <!-- 阶段零专用指标 -->
                <div class="stage-zero-section">
                    <div class="section-title">
                        <span>🛡️</span>
                        <span>阶段零预验证指标</span>
                    </div>
                    <div id="stage-zero-metrics-${this.containerId}" class="stage-zero-metrics"></div>
                </div>
                
                <!-- 关键指标统计 -->
                <div class="key-metrics-section">
                    <div class="section-title">
                        <span>📈</span>
                        <span>关键指标统计</span>
                    </div>
                    <div id="key-metrics-${this.containerId}" class="key-metrics"></div>
                </div>
                
                <!-- 整体进度 -->
                <div class="overall-progress-section">
                    <div class="section-title">
                        <span>🎯</span>
                        <span>整体进度</span>
                    </div>
                    <div id="overall-progress-${this.containerId}" class="overall-progress"></div>
                </div>
            </div>
        `;
        
        // 渲染各个部分
        this.updateDisplay(progressData);
    }
    
    /**
     * 渲染空状态
     */
    renderEmptyState() {
        this.container.innerHTML = `
            <div class="progress-empty-state" style="
                display: flex;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #666;
                text-align: center;
                font-size: 0.9rem;
            ">
                <div>
                    <div style="font-size: 2rem; margin-bottom: 1rem;">📊</div>
                    <div>暂无进度数据</div>
                    <div style="font-size: 0.8rem; margin-top: 0.5rem; color: #888;">
                        请启动项目处理以查看进度信息
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 更新显示内容
     */
    updateDisplay(progressData, oldData = null) {
        if (!progressData) return;
        
        this.updateStageProgress(progressData.current_stage);
        this.updateStageZeroMetrics(progressData.stage_zero_metrics);
        this.updateKeyMetrics(progressData.key_metrics);
        this.updateOverallProgress(progressData.overall_progress);
    }
    
    /**
     * 更新阶段进度
     */
    updateStageProgress(stageData) {
        const container = this.find(`#stage-progress-${this.containerId}`);
        if (!container || !stageData) return;
        
        const stages = [
            { name: '阶段零：标准化与预验证', badge: '可靠性基石' },
            { name: '阶段一：全局契约生成', badge: null },
            { name: '阶段二：引用式契约生成', badge: null },
            { name: '阶段三：契约履行与审计', badge: null },
            { name: '阶段四：整体性审计', badge: null }
        ];
        
        container.innerHTML = stages.map((stage, index) => {
            const isCurrent = index === stageData.stage_number;
            const isCompleted = index < stageData.stage_number;
            const isPending = index > stageData.stage_number;
            
            let statusClass = 'pending';
            let statusIndicator = 'status-pending';
            
            if (isCurrent) {
                statusClass = 'current';
                statusIndicator = stageData.status_indicator || 'status-thinking';
            } else if (isCompleted) {
                statusClass = 'completed';
                statusIndicator = 'status-active';
            }
            
            return `
                <div class="stage-item ${statusClass} ${index === 0 ? 'stage-zero-highlight' : ''}">
                    <span class="status-indicator ${statusIndicator}"></span>
                    <span class="stage-name">${stage.name}</span>
                    ${stage.badge ? `<span class="badge badge-reliability">${stage.badge}</span>` : ''}
                    ${isCurrent && stageData.progress_percentage ? 
                        `<div class="stage-progress-bar">
                            <div class="progress-fill" style="width: ${stageData.progress_percentage}%"></div>
                        </div>` : ''}
                </div>
            `;
        }).join('');
    }
    
    /**
     * 更新阶段零指标
     */
    updateStageZeroMetrics(metricsData) {
        const container = this.find(`#stage-zero-metrics-${this.containerId}`);
        if (!container || !metricsData) return;
        
        const metrics = [
            {
                label: '预验证通过率',
                value: metricsData.pre_validation_pass_rate,
                suffix: '%',
                type: 'percentage',
                target: 100
            },
            {
                label: '冲突预防数量',
                value: metricsData.conflict_prevention_count,
                suffix: '',
                type: 'count',
                target: 5
            },
            {
                label: 'Schema验证通过',
                value: `${metricsData.schema_validation_passed}/${metricsData.schema_validation_total}`,
                suffix: '',
                type: 'fraction',
                target: metricsData.schema_validation_total
            }
        ];
        
        container.innerHTML = metrics.map(metric => {
            let percentage = 0;
            let colorClass = 'info';
            
            if (metric.type === 'percentage') {
                percentage = metric.value;
            } else if (metric.type === 'count') {
                percentage = Math.min((metric.value / metric.target) * 100, 100);
            } else if (metric.type === 'fraction') {
                percentage = (metricsData.schema_validation_passed / metricsData.schema_validation_total) * 100;
            }
            
            if (percentage >= 90) colorClass = 'success';
            else if (percentage >= 70) colorClass = 'info';
            else if (percentage >= 50) colorClass = 'warning';
            else colorClass = 'danger';
            
            return `
                <div class="metric-item">
                    <div class="metric-header">
                        <span class="metric-label">${metric.label}</span>
                        <span class="metric-value ${colorClass}">${metric.value}${metric.suffix}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill ${colorClass}" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');
    }
    
    /**
     * 更新关键指标
     */
    updateKeyMetrics(keyMetrics) {
        const container = this.find(`#key-metrics-${this.containerId}`);
        if (!container || !keyMetrics) return;
        
        const metrics = [
            {
                label: '约束发现',
                value: keyMetrics.atomic_constraints_discovered,
                target: 40,
                icon: '🔍'
            },
            {
                label: '契约生成',
                value: keyMetrics.global_contracts_generated,
                target: 25,
                icon: '📋'
            },
            {
                label: '文档处理',
                value: `${keyMetrics.documents_processed}/${keyMetrics.documents_total}`,
                target: keyMetrics.documents_total,
                icon: '📄'
            },
            {
                label: '可靠性评分',
                value: `${keyMetrics.current_reliability_score}%`,
                target: 100,
                icon: '🎯'
            }
        ];
        
        container.innerHTML = `
            <div class="metrics-grid">
                ${metrics.map(metric => {
                    let percentage = 0;
                    let colorClass = 'info';
                    
                    if (typeof metric.value === 'string' && metric.value.includes('/')) {
                        const [current, total] = metric.value.split('/').map(Number);
                        percentage = (current / total) * 100;
                    } else if (typeof metric.value === 'string' && metric.value.includes('%')) {
                        percentage = parseFloat(metric.value);
                    } else {
                        percentage = (metric.value / metric.target) * 100;
                    }
                    
                    if (percentage >= 80) colorClass = 'success';
                    else if (percentage >= 60) colorClass = 'info';
                    else if (percentage >= 40) colorClass = 'warning';
                    else colorClass = 'danger';
                    
                    return `
                        <div class="metric-card">
                            <div class="metric-icon">${metric.icon}</div>
                            <div class="metric-content">
                                <div class="metric-label">${metric.label}</div>
                                <div class="metric-value ${colorClass}">${metric.value}</div>
                                <div class="metric-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill ${colorClass}" style="width: ${Math.min(percentage, 100)}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }
    
    /**
     * 更新整体进度
     */
    updateOverallProgress(overallProgress) {
        const container = this.find(`#overall-progress-${this.containerId}`);
        if (!container || !overallProgress) return;
        
        const percentage = overallProgress.percentage || 0;
        let colorClass = 'info';
        
        if (percentage >= 80) colorClass = 'success';
        else if (percentage >= 60) colorClass = 'info';
        else if (percentage >= 40) colorClass = 'warning';
        else colorClass = 'danger';
        
        container.innerHTML = `
            <div class="overall-progress-display">
                <div class="progress-header">
                    <span class="progress-percentage ${colorClass}">${percentage}%</span>
                    <span class="progress-status">${overallProgress.status || '处理中'}</span>
                </div>
                <div class="progress-bar large">
                    <div class="progress-fill ${colorClass}" style="width: ${percentage}%"></div>
                </div>
                ${overallProgress.estimated_completion ? `
                    <div class="progress-eta">
                        预计完成时间: ${new Date(overallProgress.estimated_completion).toLocaleString()}
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 可以添加点击事件、悬停效果等
        const stageItems = this.findAll('.stage-item');
        stageItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const stageName = item.querySelector('.stage-name')?.textContent;
                console.log(`Stage clicked: ${stageName}`);
                // 可以触发详细信息显示等
            });
        });
        
        // 指标卡片悬停效果
        const metricCards = this.findAll('.metric-card');
        metricCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-2px)';
                card.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
                card.style.boxShadow = '';
            });
        });
    }
    
    /**
     * 组件特定的刷新方法
     */
    async refreshProgress() {
        try {
            await this.refresh('progress');
            console.log('Progress data refreshed');
        } catch (error) {
            console.error('Failed to refresh progress data:', error);
        }
    }
    
    /**
     * 获取当前进度百分比
     */
    getCurrentProgress() {
        const progressData = this.getData('progress');
        return progressData?.overall_progress?.percentage || 0;
    }
    
    /**
     * 获取当前阶段信息
     */
    getCurrentStage() {
        const progressData = this.getData('progress');
        return progressData?.current_stage || null;
    }
    
    /**
     * 检查是否在阶段零
     */
    isInStageZero() {
        const currentStage = this.getCurrentStage();
        return currentStage?.stage_number === 0;
    }
}

// 导出组件类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProgressComponent;
} else {
    window.ProgressComponent = ProgressComponent;
}
