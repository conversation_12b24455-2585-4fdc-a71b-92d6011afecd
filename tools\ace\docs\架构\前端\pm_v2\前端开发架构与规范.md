# Project Manager V2 前端开发架构与规范

## 1. 架构概览

### 1.1 整体架构
Project Manager V2 采用基于九宫格布局的单页面应用架构，遵循统一前后端对接架构的设计原则。

```
project_manager_v2/
├── 视图层 (View Layer)
│   ├── nine_grid_layout.html     # 九宫格布局结构
│   └── component_templates/      # 组件模板
├── 样式层 (Style Layer)
│   ├── nine_grid_base.css       # 通用基础样式
│   └── pm_v2_specific.css       # V2专用样式
├── 统一架构层 (Unified Architecture Layer)
│   ├── http-client.js           # 统一HTTP客户端
│   ├── data-manager.js          # 统一数据管理器
│   ├── base-component.js        # 统一组件基类
│   └── app-manager.js           # 统一应用管理器
├── 组件层 (Component Layer)
│   ├── progress-component.js    # 进度监控组件
│   ├── risk-component.js        # 风险评估组件
│   ├── constraint-component.js  # 约束审查组件
│   └── [其他组件...]           # 其他专用组件
└── 传统逻辑层 (Legacy Logic Layer) [兼容性保留]
    ├── nine_grid_base.js        # 通用基础函数
    └── pm_v2_app.js            # V2专用业务逻辑
```

### 1.2 技术栈
- **布局**: CSS Grid (九宫格布局)
- **样式**: 原生CSS + VSCode主题风格
- **脚本**: 原生JavaScript (ES6+) + 统一架构框架
- **通信**: 统一DataManager (WebSocket + HTTP)
- **图标**: Unicode Emoji + 自定义SVG
- **架构**: 统一前后端对接架构

## 2. 九宫格布局规范

### 2.1 区域定义
```css
grid-template-areas:
    "area1-2 area5 area3"
    "area4   area5 area6"
    "area7   area8 area9";
```

### 2.2 区域功能映射
| 区域 | 功能 | 职责 |
|------|------|------|
| 1-2 | 项目进度监控 | 显示关键指标、阶段进度、整体统计 |
| 3 | 风险评估 | 可靠性评分、风险列表、报告链接 |
| 4 | 项目经理状态 | AI状态、当前任务、处理进度 |
| 5 | Python主持人算法思维 | 日志展示、思维过程、交互控制 |
| 6 | 约束审查 | 约束详情、验证结果、审查状态 |
| 7 | 知识库可视化 | 约束图谱、节点交互、关系展示 |
| 8 | 人类输入控制区 | 详细区、项目选择器、控制按钮 |
| 9 | 项目交付结果 | 输出文件、处理统计、下载链接 |

### 2.3 响应式设计
```css
/* 桌面端 (>1200px) */
.nine-grid-container {
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 2px;
}

/* 平板端 (768px-1200px) */
@media (max-width: 1200px) {
    .area-title { font-size: 0.9rem; }
    .constraint-node { width: 40px; height: 40px; }
}

/* 移动端 (<768px) */
@media (max-width: 768px) {
    .nine-grid-container { grid-gap: 1px; }
    .grid-area { padding: 0.5rem; }
}
```

## 3. 组件化设计规范

### 3.1 通用组件

#### 3.1.1 进度条组件
```css
.progress-bar {
    height: 8px;
    background: #2A2D30;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.3rem;
}

.progress-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 4px;
}

/* 颜色主题 */
.progress-fill.success { background: linear-gradient(90deg, #4CAF50, #81C784); }
.progress-fill.warning { background: linear-gradient(90deg, #FF9800, #FFB74D); }
.progress-fill.info { background: linear-gradient(90deg, #2196F3, #64B5F6); }
.progress-fill.danger { background: linear-gradient(90deg, #F44336, #EF5350); }
.progress-fill.primary { background: linear-gradient(90deg, #0078D4, #40A9FF); }
```

#### 3.1.2 状态指示器组件
```css
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

/* 状态类型 */
.status-indicator.status-active { background: #4CAF50; }
.status-indicator.status-pending { background: #FF9800; }
.status-indicator.status-error { background: #F44336; }
.status-indicator.status-thinking { 
    background: #2196F3;
    animation: pulse-thinking 2s infinite;
}
.status-indicator.status-converging { 
    background: #9C27B0;
    animation: pulse-converging 1.5s infinite;
}
```

#### 3.1.3 圆形进度环组件
```html
<div class="circular-progress">
    <svg>
        <circle class="progress-circle progress-bg" cx="40" cy="40" r="36"></circle>
        <circle class="progress-circle progress-bar-circle success" cx="40" cy="40" r="36" 
                style="stroke-dashoffset: 79.1;"></circle>
    </svg>
    <div class="progress-text">
        <div class="progress-value">65%</div>
        <div class="progress-label">可靠性</div>
    </div>
</div>
```

### 3.2 专用组件

#### 3.2.1 知识库可视化组件
```html
<div class="knowledge-graph">
    <div class="constraint-node global" onclick="showConstraintDetail('GB001')">
        GB001
        <div class="node-label">全局</div>
    </div>
    <!-- 更多节点... -->
    <div class="constraint-connection"></div>
    <div class="node-tooltip" id="node-tooltip"></div>
</div>
```

#### 3.2.2 决策中心卡片组件
```html
<div class="decision-card insight">
    <div class="decision-card-title">
        <span>🔍</span>
        <span>关键洞察</span>
        <span class="status-badge">实时</span>
    </div>
    <div class="decision-card-content">
        <!-- 内容 -->
    </div>
</div>
```

## 4. 样式规范

### 4.1 颜色系统
```css
:root {
    /* 主色调 */
    --primary-color: #0078D4;
    --secondary-color: #2A2D30;
    
    /* 状态颜色 */
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --info-color: #2196F3;
    
    /* 背景颜色 */
    --bg-primary: #1E1F22;
    --bg-secondary: #2A2D30;
    --bg-tertiary: #3C3F41;
    
    /* 文字颜色 */
    --text-primary: #BBBBBB;
    --text-secondary: #888888;
    --text-accent: #0078D4;
}
```

### 4.2 字体层次
```css
/* 标题层次 */
.area-title {
    font-size: calc(0.5rem + 0.15vw);
    font-weight: bold;
    color: var(--text-accent);
}

/* 内容层次 */
.area-content {
    font-size: calc(0.8rem + 0.2vw);
    line-height: 1.4;
    color: var(--text-primary);
}

/* 辅助文字 */
.secondary-text {
    font-size: 0.85em;
    opacity: 0.7;
    color: var(--text-secondary);
}
```

### 4.3 间距系统
```css
/* 基础间距单位 */
--spacing-xs: 0.2rem;
--spacing-sm: 0.5rem;
--spacing-md: 1rem;
--spacing-lg: 1.5rem;
--spacing-xl: 2rem;

/* 应用示例 */
.grid-area { padding: var(--spacing-md); }
.status-item { margin-bottom: var(--spacing-sm); }
.decision-card { margin-bottom: var(--spacing-md); }
```

## 5. JavaScript架构规范

### 5.1 统一架构模块结构
```javascript
// 统一架构层 (推荐使用)
├── HttpClient - 统一HTTP客户端
│   ├── get(), post(), put(), delete()
│   ├── upload(), download()
│   └── 自动重试、缓存、错误处理
├── DataManager - 统一数据管理器
│   ├── fetchData(), setData(), subscribe()
│   ├── WebSocket自动处理
│   └── 智能缓存管理
├── BaseComponent - 统一组件基类
│   ├── init(), render(), destroy()
│   ├── getDataTypes(), onDataUpdate()
│   └── 生命周期管理
└── AppManager - 统一应用管理器
    ├── registerComponent(), getComponent()
    ├── 组件初始化和管理
    └── 全局错误处理

// 传统模块结构 (兼容性保留)
├── nine_grid_base.js - 通用基础函数
│   ├── 提示框管理 (showTooltip, hideTooltip)
│   ├── 日志处理 (toggleLogDetail, showLogDetail)
│   ├── 会议控制 (startMeeting, pauseMeeting, stopMeeting)
│   └── 工具函数 (formatTimestamp, addLogEntry)
└── pm_v2_app.js - V2专用业务逻辑
    ├── 约束管理 (showConstraintDetail)
    ├── 决策处理 (makeDecision)
    ├── 项目控制 (startCheck, startGovernance)
    └── WebSocket通信 (socket事件处理)
```

### 5.2 统一架构开发规范
```javascript
// 新组件开发 (推荐方式)
class ProgressComponent extends BaseComponent {
    getDataTypes() {
        return ['progress', 'stage_metrics'];
    }

    render() {
        const progressData = this.getData('progress');
        // 渲染逻辑
    }

    onDataUpdate(dataType, data) {
        // 数据更新处理
    }
}

// 应用初始化 (统一方式)
document.addEventListener('DOMContentLoaded', async () => {
    const appManager = await initializeApp('project-id');

    appManager.registerComponents([
        { type: 'progress', containerId: 'progress-area', ComponentClass: ProgressComponent },
        { type: 'risk', containerId: 'risk-area', ComponentClass: RiskComponent }
    ]);
});
```

### 5.3 函数命名规范
```javascript
// 统一架构组件方法
class ComponentName extends BaseComponent {
    // 必须实现的方法
    getDataTypes() { }
    render() { }

    // 可重写的方法
    onDataUpdate(dataType, data) { }
    bindEvents() { }
    onInitialized() { }
    onDestroyed() { }
}

// 传统函数命名 (兼容性保留)
// 动作函数：动词开头
function showConstraintDetail(constraintId) { }
function hideTooltip(tooltipId) { }
function startMeeting() { }

// 获取函数：get开头
function getExpandedDetailContent(detailType, logId) { }
function getLogDetailContent(logId) { }

// 处理函数：handle开头
function handleScanningClick() { }
function handleWebSocketMessage(data) { }

// 工具函数：描述性命名
function formatTimestamp(date) { }
function addLogEntry(containerId, message, type) { }
```

### 5.4 事件处理规范
```javascript
// 统一架构事件处理 (推荐)
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 初始化统一架构
        const appManager = await initializeApp(getCurrentProjectId());

        // 注册所有组件
        appManager.registerComponents(COMPONENT_CONFIGS);

        console.log('应用初始化成功');
    } catch (error) {
        console.error('应用初始化失败:', error);
        showInitializationError(error);
    }
});

// 传统事件处理 (兼容性保留)
document.addEventListener('DOMContentLoaded', function() {
    // 初始化代码
    initializeComponents();
    setupEventListeners();
    connectWebSocket();
});

// 统一错误处理
function handleError(error, context) {
    console.error(`[${context}] 错误:`, error);

    // 使用统一架构的错误处理
    if (window.appManager) {
        window.appManager.handleGlobalError(error);
    } else {
        // 传统错误处理
        addLogEntry('error-log', `${context}: ${error.message}`, 'error');
    }
}
```

## 6. 性能优化规范

### 6.1 CSS优化
```css
/* 使用CSS变量减少重复 */
:root { --primary-color: #0078D4; }

/* 合理使用动画 */
.status-indicator.status-thinking {
    animation: pulse-thinking 2s infinite;
    will-change: transform, opacity;
}

/* 避免重排重绘 */
.constraint-node:hover {
    transform: scale(1.2); /* 使用transform而非width/height */
}
```

### 6.2 JavaScript优化
```javascript
// 防抖处理
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 事件委托
document.getElementById('knowledge-graph').addEventListener('click', function(e) {
    if (e.target.classList.contains('constraint-node')) {
        const constraintId = e.target.textContent.trim();
        showConstraintDetail(constraintId);
    }
});
```

## 7. 开发工作流

### 7.1 文件组织
```
tools/ace/src/web_interface/
├── templates/
│   └── project_manager_v2.html    # 主页面模板
├── static/
│   ├── css/
│   │   ├── nine_grid_base.css     # 通用样式
│   │   └── pm_v2_specific.css     # 专用样式(如需要)
│   └── js/
│       ├── nine_grid_base.js      # 通用函数
│       └── project_manager_v2_app.js # 专用逻辑
└── docs/
    └── 架构/前端/pm_v2/           # 文档目录
```

### 7.2 开发步骤
1. **需求分析** - 确定功能需求和交互设计
2. **组件设计** - 设计可复用的UI组件
3. **样式开发** - 实现CSS样式，优先使用通用组件
4. **逻辑开发** - 实现JavaScript逻辑，区分通用vs专用
5. **集成测试** - 测试组件交互和数据流
6. **性能优化** - 优化加载速度和运行性能
7. **文档更新** - 更新开发文档和使用说明

### 7.3 代码审查清单
- [ ] 是否遵循九宫格布局规范
- [ ] 是否正确使用通用组件
- [ ] 是否符合颜色和字体规范
- [ ] 是否实现响应式设计
- [ ] 是否正确分离通用vs专用代码
- [ ] 是否添加必要的错误处理
- [ ] 是否优化性能和用户体验
- [ ] 是否更新相关文档

## 8. 数据流架构

### 8.1 统一架构数据流向
```
用户交互 → 组件事件 → DataManager → 后端API/WebSocket
    ↓                    ↓
视图更新 ← 组件渲染 ← 数据订阅 ← 数据更新通知
```

### 8.2 统一状态管理
```javascript
// 统一架构状态管理 (推荐)
class DataManager {
    constructor(projectId) {
        this.data = new Map();           // 数据存储
        this.subscribers = new Map();    // 订阅者管理
        this.cache = new Map();          // 智能缓存
    }

    // 统一数据获取
    async fetchData(dataType, params = {}) {
        const endpoint = this.getEndpoint(dataType);
        const response = await this.httpClient.get(endpoint, params);
        this.setData(dataType, response.data);
        return response.data;
    }

    // 统一数据订阅
    subscribe(dataType, callback) {
        // 订阅逻辑
        return unsubscribeFunction;
    }

    // WebSocket自动处理
    handleWebSocketMessage(message) {
        const { type, data } = message;
        const dataType = this.getDataTypeFromEvent(type);
        this.setData(dataType, data);
    }
}

// 传统状态管理 (兼容性保留)
const AppState = {
    // 项目状态
    project: {
        currentProject: 'default',
        documentPath: '',
        processingStatus: 'idle'
    },

    // UI状态
    ui: {
        selectedConstraint: null,
        activeLogEntry: null,
        detailAreaContent: null
    },

    // 数据状态
    data: {
        constraints: [],
        risks: [],
        progress: {}
    }
};

// 状态更新函数
function updateState(path, value) {
    const keys = path.split('.');
    let current = AppState;
    for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;

    // 触发视图更新
    updateView(path, value);
}
```

### 8.3 统一通信协议
```javascript
// 统一数据类型映射
const DATA_TYPE_MAPPING = {
    'progress': '/projects/{projectId}/progress',
    'risk_assessment': '/projects/{projectId}/risk-assessment',
    'constraints': '/projects/{projectId}/constraints',
    'knowledge_graph': '/projects/{projectId}/knowledge-graph',
    'manager_status': '/projects/{projectId}/manager-status',
    'algorithm_logs': '/projects/{projectId}/algorithm-logs',
    'deliverables': '/projects/{projectId}/deliverables'
};

// WebSocket事件映射
const WEBSOCKET_EVENT_MAPPING = {
    'stage_progress_update': 'progress',
    'reliability_score_update': 'risk_assessment',
    'constraint_created': 'constraints',
    'constraint_updated': 'constraints',
    'knowledge_graph_update': 'knowledge_graph',
    'manager_status_update': 'manager_status',
    'algorithm_log_entry': 'algorithm_logs',
    'deliverable_ready': 'deliverables'
};

// 传统消息格式 (兼容性保留)
{
    "type": "constraint_update",
    "data": {
        "constraint_id": "GB001",
        "status": "validated",
        "details": { ... }
    },
    "timestamp": "2025-01-31T14:17:30Z"
}

// 传统事件类型定义
const MESSAGE_TYPES = {
    CONSTRAINT_UPDATE: 'constraint_update',
    RISK_ASSESSMENT: 'risk_assessment',
    PROGRESS_UPDATE: 'progress_update',
    LOG_ENTRY: 'log_entry',
    DECISION_REQUEST: 'decision_request'
};
```

## 9. 测试规范

### 9.1 统一架构组件测试
```javascript
// 测试BaseComponent子类
describe('ProgressComponent', () => {
    let component;
    let mockDataManager;
    let container;

    beforeEach(() => {
        // 创建测试容器
        container = document.createElement('div');
        container.id = 'test-progress-area';
        document.body.appendChild(container);

        // 创建模拟数据管理器
        mockDataManager = {
            getData: jest.fn(),
            subscribe: jest.fn(() => () => {}),
            fetchData: jest.fn()
        };

        // 创建组件实例
        component = new ProgressComponent('test-progress-area', mockDataManager);
    });

    afterEach(() => {
        if (component) component.destroy();
        if (container && container.parentElement) container.remove();
    });

    test('should initialize correctly', async () => {
        await component.init();
        expect(component.isInitialized).toBe(true);
    });

    test('should render progress data', () => {
        const mockData = {
            current_stage: { stage_number: 0, status: 'in_progress' },
            stage_zero_metrics: { pre_validation_pass_rate: 100 }
        };

        mockDataManager.getData.mockReturnValue(mockData);
        component.render();

        expect(container.innerHTML).toContain('阶段零');
    });
});

// 测试传统函数 (兼容性保留)
describe('formatTimestamp', () => {
    test('should format date correctly', () => {
        const date = new Date('2025-01-31T14:17:30Z');
        const result = formatTimestamp(date);
        expect(result).toBe('14:17:30');
    });
});

// 测试传统组件交互
describe('showConstraintDetail', () => {
    test('should display constraint details', () => {
        const mockElement = document.createElement('div');
        mockElement.id = 'constraint-review-content';
        document.body.appendChild(mockElement);

        showConstraintDetail('GB001');

        expect(mockElement.innerHTML).toContain('GB001');
        expect(mockElement.innerHTML).toContain('约束详情');
    });
});
```

### 9.2 集成测试
```javascript
// 测试完整交互流程
describe('Constraint Interaction Flow', () => {
    test('should handle constraint selection and detail display', async () => {
        // 1. 点击约束节点
        const constraintNode = document.querySelector('.constraint-node[onclick*="GB001"]');
        constraintNode.click();

        // 2. 验证详情区域更新
        const detailArea = document.getElementById('constraint-review-content');
        expect(detailArea.innerHTML).toContain('GB001');

        // 3. 验证节点高亮
        expect(constraintNode.style.transform).toBe('scale(1.3)');
    });
});
```

### 9.3 视觉回归测试
```javascript
// 使用截图对比进行视觉测试
describe('Visual Regression Tests', () => {
    test('should match nine grid layout snapshot', async () => {
        await page.goto('http://localhost:25526/pm_v2');
        const screenshot = await page.screenshot();
        expect(screenshot).toMatchImageSnapshot();
    });
});
```

## 10. 部署与维护

### 10.1 构建流程
```bash
# 开发环境
npm run dev          # 启动开发服务器
npm run test         # 运行测试
npm run lint         # 代码检查

# 生产环境
npm run build        # 构建生产版本
npm run optimize     # 优化资源文件
npm run deploy       # 部署到服务器
```

### 10.2 版本管理
```
v2.1.0 - 基础九宫格布局和组件
v2.2.0 - 添加约束可视化功能
v2.3.0 - 集成决策中心和日志系统
v2.4.0 - 性能优化和响应式改进
v2.5.0 - 添加实时通信和状态同步
```

### 10.3 监控指标
```javascript
// 性能监控
const performanceMetrics = {
    pageLoadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
    domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
    firstPaint: performance.getEntriesByType('paint')[0]?.startTime,
    interactionLatency: 0 // 用户交互响应时间
};

// 错误监控
window.addEventListener('error', (event) => {
    console.error('JavaScript Error:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
    });
});
```

## 11. 最佳实践

### 11.1 代码质量
- 使用ESLint进行代码检查
- 遵循一致的命名约定
- 添加必要的注释和文档
- 实现适当的错误处理
- 编写可测试的代码

### 11.2 用户体验
- 提供即时的视觉反馈
- 实现平滑的动画过渡
- 确保响应式设计
- 优化加载性能
- 提供清晰的状态指示

### 11.3 可维护性
- 模块化代码结构
- 分离关注点
- 使用配置文件管理常量
- 实现版本兼容性
- 保持文档更新

### 11.4 安全性
- 验证用户输入
- 防止XSS攻击
- 安全的WebSocket通信
- 适当的错误信息展示
- 敏感数据保护

## 12. 扩展指南

### 12.1 添加新组件
1. 在nine_grid_base.css中添加通用样式
2. 在nine_grid_base.js中添加通用函数
3. 在具体页面中实现专用逻辑
4. 更新文档和测试

### 12.2 集成新功能
1. 分析功能需求和数据流
2. 设计API接口和消息格式
3. 实现前端交互逻辑
4. 添加相应的测试用例
5. 更新用户文档

### 12.3 性能优化
1. 分析性能瓶颈
2. 优化关键渲染路径
3. 实现懒加载和缓存
4. 减少不必要的重绘
5. 监控性能指标

---

## 附录

### A. 浏览器兼容性
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### B. 依赖库
- Socket.IO 4.0.1 (WebSocket通信)
- 无其他外部依赖 (原生JavaScript实现)

### C. 相关文档
- [Nine Grid 基础组件文档](../nine_grid_base/)
- [WebSocket API文档](../../后端/websocket_api.md)
- [V2治理引擎接口文档](../../后端/v2_engine_api.md)
- [统一前后端对接架构使用规范](../统一对接后端/前端统一架构使用规范.md)
- [统一架构快速上手指南](../统一对接后端/快速上手指南.md)
- [统一架构组件开发模板](../统一对接后端/组件开发模板.md)
- [统一架构API对接规范](../统一对接后端/API对接规范.md)

### D. 架构迁移指南
#### 从传统架构迁移到统一架构
1. **阅读统一架构文档**: 了解新的开发模式和最佳实践
2. **渐进式迁移**: 新功能使用统一架构，现有功能保持兼容
3. **组件重构**: 将传统函数逐步重构为BaseComponent子类
4. **测试验证**: 确保迁移后功能正常且性能提升
5. **文档更新**: 及时更新开发文档和使用示例

#### 迁移优先级
- **高优先级**: 新增功能和组件
- **中优先级**: 频繁修改的现有组件
- **低优先级**: 稳定运行的传统代码

#### 迁移收益
- **开发效率**: 减少80%的样板代码
- **代码质量**: 统一的开发规范和错误处理
- **维护成本**: 标准化架构降低维护难度
- **团队协作**: 统一模式提升协作效率
