# Document Reading and Memory Refresh Rules

**Document ID**: G003
**Created Date**: 2023-05-01
**Version**: 1.1
**Status**: Approved

This document defines the document reading and memory refresh rules in the AI-DEV-FLOW workflow, guiding AI on how to read relevant documents based on current mode and task type, and when to refresh memory.

## 1. 文档读取规则

```javascript
// 文档读取规则
{
  "name": "documentReader",
  "description": "根据当前模式和任务类型读取相关文档",

  // 技术领域识别规则
  "taskIdentification": {
    "postgresql": {
      "keywords": ["postgresql", "数据库", "pg", "sql", "表", "schema", "索引", "查询", "数据模型", "jpa", "jdbc", "事务", "连接池"],
      "documentPaths": ["docs/common/middleware/postgresql/postgresql-index.json"],
      "triggerModes": ["RESEARCH", "PLAN", "EXECUTE"],
      "priorityLevel": "critical",
      "autoLoad": true,
      "description": "PostgreSQL相关任务在所有相关模式下都应自动加载索引文档"
    },
    "testing": {
      "keywords": ["测试", "test", "testing", "参数化测试", "AI测试", "测试策略", "测试架构", "风险分析", "STRIDE", "FMEA", "攻击树", "业务逻辑测试", "Web层测试", "端到端测试", "安全测试", "性能测试", "测试参数", "测试配置", "测试迭代", "人-AI交互测试"],
      "documentPaths": ["docs/common/best-practices/testing/testing-index.json"],
      "triggerModes": ["RESEARCH", "INNOVATE", "PLAN", "EXECUTE"],
      "priorityLevel": "critical",
      "autoLoad": true,
      "description": "测试相关任务在所有相关模式下都应自动加载测试索引文档，根据智能权重系统选择最相关的测试文档"
    }
  },

  // 基础文档（非分阶段）
  "baseDocs": [
    {
      "path": "docs/ai-dev/context/project-context.md",
      "priority": "high",
      "refreshFrequency": "session_start"
    },
    {
      "path": "docs/ai-dev/context/decision-log.md",
      "priority": "medium",
      "refreshFrequency": "mode_change"
    },
    {
      "path": "docs/ai-dev/context/system-patterns.md",
      "priority": "low",
      "refreshFrequency": "on_demand"
    }
  ],

  // 分阶段文档
  "phaseDocs": {
    "RESEARCH": [
      {
        "path": "docs/ai-dev/context/development-log.md",
        "priority": "high",
        "refreshFrequency": "session_start"
      },
      {
        "path": "docs/architecture/",
        "priority": "medium",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/plans/",
        "priority": "medium",
        "refreshFrequency": "mode_change"
      }
    ],
    "INNOVATE": [
      {
        "path": "docs/ai-dev/prompts/templates/",
        "priority": "high",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/ai-dev/prompts/sessions/",
        "priority": "medium",
        "refreshFrequency": "task_change"
      },
      {
        "path": "docs/architecture/design-docs/",
        "priority": "medium",
        "refreshFrequency": "mode_change"
      }
    ],
    "PLAN": [
      {
        "path": "docs/guides/",
        "priority": "high",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/plans/",
        "priority": "high",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/ai-dev/prompts/templates/feature-dev.md",
        "priority": "medium",
        "refreshFrequency": "task_change"
      }
    ],
    "EXECUTE": [
      {
        "path": "docs/ai-dev/prompts/templates/code-review.md",
        "priority": "high",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/guides/troubleshooting.md",
        "priority": "medium",
        "refreshFrequency": "on_demand"
      },
      {
        "path": "docs/ai-dev/prompts/sessions/",
        "priority": "medium",
        "refreshFrequency": "task_change"
      }
    ],
    "REVIEW": [
      {
        "path": "docs/ai-dev/prompts/templates/code-review.md",
        "priority": "high",
        "refreshFrequency": "mode_change"
      },
      {
        "path": "docs/guides/",
        "priority": "medium",
        "refreshFrequency": "mode_change"
      }
    ]
  },

  // Unified priority system - refer to .augment-guidelines for complete priority definitions
  "unifiedPrioritySystem": {
    "note": "Complete unified priority system is defined in .augment-guidelines",
    "implementationReference": ".augment-guidelines - Memory Priorities (Unified Priority System)"
  },

  // 读取策略（基于统一优先级）
  "readStrategy": {
    "readEntire": {
      "description": "完整读取文档",
      "applicablePriority": ["critical", "high"],
      "extractSummary": false,
      "maxTokens": null
    },
    "extractSummary": {
      "description": "提取摘要和关键部分",
      "applicablePriority": ["medium", "low"],
      "extractSummary": true,
      "maxTokens": 500,
      "focusSections": "根据优先级定义"
    },
    "skipOrSummarize": {
      "description": "跳过或极简摘要",
      "applicablePriority": ["minimal"],
      "extractSummary": true,
      "maxTokens": 100,
      "focusSections": ["标题", "摘要"]
    }
  },

  // 功能文档
  "featureDocs": {
    "strategy": {
      "useFeatureMapping": true,
      "readFeatureReadme": true,
      "followRelatedFeatures": true
    },
    "priority": {
      "currentFeature": "high",
      "relatedFeatures": "medium",
      "otherFeatures": "low"
    },
    "refreshFrequency": {
      "currentFeature": "task_change",
      "relatedFeatures": "on_demand",
      "otherFeatures": "never"
    }
  }
}
```

## 2. 记忆刷新规则

```javascript
// 记忆刷新规则
{
  "name": "memoryRefresh",
  "description": "定义何时以及如何刷新AI的记忆",

  // 刷新触发点
  "refreshTriggers": {
    "session_start": {
      "description": "在每次会话开始时",
      "actions": [
        "读取基础文档",
        "识别任务类型",
        "读取任务相关文档",
        "读取当前模式文档"
      ]
    },
    "mode_change": {
      "description": "当模式从一个转换到另一个时",
      "actions": [
        "保留基础文档记忆",
        "读取新模式相关文档",
        "如果新模式是PLAN且任务涉及PostgreSQL，读取docs/common/middleware/postgresql/postgresql-index.json",
        "如果任务涉及测试，根据当前模式读取docs/common/best-practices/testing/testing-index.json中对应权重的文档",
        "更新当前上下文"
      ],
      "decisionAuthority": {
        "INNOVATE_to_PLAN": {
          "trigger": "multiple_solutions_identified",
          "action": "ESCALATE_TO_HUMAN",
          "requirement": "MANDATORY: Request human decision before proceeding to PLAN mode",
          "description": "在INNOVATE模式下，如识别到多个解决方案，必须请求人类决策后才能转换到PLAN模式"
        },
        "PLAN_to_EXECUTE": {
          "trigger": "architectural_or_business_logic_changes",
          "action": "ESCALATE_TO_HUMAN",
          "requirement": "MANDATORY: Request human decision before proceeding to EXECUTE mode",
          "description": "在PLAN模式下，如涉及架构、方向性、基础层或业务逻辑变更，必须请求人类决策后才能转换到EXECUTE模式"
        }
      }
    },
    "task_change": {
      "description": "当任务类型发生变化时",
      "actions": [
        "保留基础文档记忆",
        "识别新任务类型",
        "读取新任务相关文档",
        "读取当前模式文档"
      ]
    },
    "periodic": {
      "description": "定期刷新（每10个交互）",
      "actions": [
        "检查开发日志更新",
        "如有更新，重新读取开发日志",
        "保持其他文档记忆不变"
      ]
    },
    "explicit": {
      "description": "用户显式请求刷新",
      "triggers": ["刷新记忆", "刷新[文档名]"],
      "actions": [
        "如指定文档，重新读取该文档",
        "否则重新读取所有相关文档"
      ]
    },
    "on_demand": {
      "description": "根据需要刷新",
      "conditions": [
        "AI发现信息不足",
        "AI发现信息可能过时",
        "任务需要特定文档"
      ],
      "actions": [
        "识别所需文档",
        "读取所需文档",
        "更新当前上下文"
      ]
    }
  },

  // 记忆管理
  "memoryManagement": {
    "prioritization": {
      "recent": "high",
      "relevant": "high",
      "base": "medium",
      "historical": "low"
    },
    "retention": {
      "high": "完整保留",
      "medium": "保留关键信息",
      "low": "仅保留摘要"
    },
    "decay": {
      "recent": "无衰减",
      "medium": "24小时后开始衰减",
      "old": "7天后显著衰减"
    }
  }
}
```

## 3. 记忆刷新机制

以下是AI-DEV-FLOW工作流中的记忆刷新机制，确保AI在需要时能够获取最新信息：

### 3.1 会话开始刷新

在每次会话开始时，AI应该：
- 读取基础文档区域的所有文档
- 根据用户初始请求判断任务类型，读取相应的任务类型文档
- 进入默认的RESEARCH模式，读取RESEARCH阶段相关文档

### 3.2 模式转换刷新

当AI从一个模式转换到另一个模式时：
- 读取新模式对应的分阶段文档
- 保留基础文档和任务类型文档的记忆
- 更新当前上下文，反映新模式的重点

### 3.3 任务变更刷新

当用户提出新任务或任务方向发生重大变化时：
- 重新评估任务类型，读取相应的任务类型文档
- 保留基础文档的记忆
- 根据当前模式读取相应的分阶段文档

### 3.4 定期刷新

在长会话中，定期（例如每10个交互）：
- 检查开发日志是否有更新
- 如果有更新，重新读取开发日志
- 保持其他文档的记忆不变

### 3.5 显式刷新

用户可以通过特定命令触发显式刷新：
- "刷新记忆"：重新读取所有相关文档
- "刷新[文档名]"：重新读取特定文档

### 3.6 功能变更刷新

当用户开始处理新功能或切换到不同功能时：
- 查询文档映射表找到相关功能目录
- 读取功能README获取功能概述和文档索引
- 根据当前任务和模式读取相关文档
- 保留基础文档和当前模式文档的记忆

## 4. 实现示例

### 4.1 会话开始时的文档读取

```python
def session_start_refresh():
    # 读取基础文档
    project_context = read_document("项目上下文")
    decision_log = read_document("决策日志")

    # 识别任务类型
    task_type = identify_task_type(user_query)

    # 读取任务相关文档
    task_docs = read_task_documents(task_type)

    # 读取当前模式文档（默认RESEARCH）
    mode_docs = read_mode_documents("RESEARCH")

    # 更新AI记忆
    update_memory({
        "base_docs": [project_context, decision_log],
        "task_docs": task_docs,
        "mode_docs": mode_docs
    })
```

### 4.2 模式转换时的文档读取

```python
def mode_change_refresh(old_mode, new_mode, task_context=None):
    # 保留基础文档记忆
    base_docs = get_memory("base_docs")
    task_docs = get_memory("task_docs")

    # 读取新模式相关文档
    new_mode_docs = read_mode_documents(new_mode)

    # 特殊处理：如果新模式是PLAN且任务涉及PostgreSQL，读取PostgreSQL索引文档
    special_docs = []
    if new_mode == "PLAN" and task_context and is_postgresql_related(task_context):
        postgresql_index = read_document("docs/common/middleware/postgresql/postgresql-index.json")
        special_docs.append(postgresql_index)
        # 主动提醒AI查阅PostgreSQL索引文档
        remind_to_check_postgresql_index()

    # 更新AI记忆
    update_memory({
        "base_docs": base_docs,
        "task_docs": task_docs,
        "mode_docs": new_mode_docs,
        "special_docs": special_docs,  # 新增的特殊文档
        "current_mode": new_mode
    })
```

### 4.3 功能变更时的文档读取

```python
def feature_change_refresh(feature_id):
    # 查询文档映射表
    feature_info = query_feature_mapping(feature_id)

    # 读取功能README
    feature_readme = read_document(feature_info["readme_path"])

    # 读取相关文档
    feature_docs = []
    for doc_path in feature_info["relevant_docs"]:
        feature_docs.append(read_document(doc_path))

    # 读取相关功能
    related_features = []
    for related_id in feature_info["related_features"]:
        related_info = query_feature_mapping(related_id)
        related_features.append(read_document(related_info["readme_path"]))

    # 更新AI记忆
    update_memory({
        "current_feature": feature_readme,
        "feature_docs": feature_docs,
        "related_features": related_features
    })
```

### 4.4 PostgreSQL任务识别和提醒

```python
def is_postgresql_related(task_context):
    """
    判断任务是否与PostgreSQL相关

    参数:
        task_context: 任务上下文，通常是用户的查询或任务描述

    返回:
        bool: 如果任务与PostgreSQL相关，返回True；否则返回False
    """
    postgresql_keywords = ["postgresql", "数据库", "pg", "sql", "表", "schema", "索引", "查询",
                          "数据模型", "jpa", "jdbc", "事务", "连接池"]

    # 检查任务上下文中是否包含PostgreSQL相关关键词
    for keyword in postgresql_keywords:
        if keyword.lower() in task_context.lower():
            return True

    return False

def remind_to_check_postgresql_index():
    """
    提醒AI查阅PostgreSQL索引文档
    """
    reminder = "检测到当前任务涉及PostgreSQL，请务必查阅docs/common/middleware/postgresql/postgresql-index.json索引文档，了解相关最佳实践和规范。"
    display_reminder(reminder)
```

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | 2023-05-01 | 初始版本 | AI助手 |
| 1.1 | 2025-01-XX | 统一模式转换决策权限表述格式，实施分层语言策略 | AI助手 |
