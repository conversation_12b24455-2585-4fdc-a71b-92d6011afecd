# 设计文档规范扫描程序

## 🎯 目标

这个扫描程序专门为 `design_document_extractor.py` 服务，确保设计文档包含生成80%覆盖率提示词所需的关键信息。

## 🔍 检查内容

### 一级检查：提取器必需信息（权重50%）
- **项目名称**: 明确的项目标识
- **核心定位**: 项目目标和价值主张
- **设计哲学**: 设计理念和原则
- **范围边界**: 包含和排除的功能范围

### 二级检查：技术栈信息（权重30%）
- **Java版本**: 如 "Java 21"
- **Spring Boot版本**: 如 "Spring Boot 3.4.5"
- **构建工具**: Maven 或 Gradle
- **数据库**: PostgreSQL、MySQL、Redis等
- **其他框架**: HikariCP、JPA、Querydsl等

### 三级检查：文档元数据（权重15%）
- **文档ID**: 唯一标识符
- **版本信息**: 文档版本号
- **复杂度等级**: L1/L2/L3标识
- **创建日期**: 文档创建时间

### 四级检查：可提取性（权重5%）
- **结构化内容**: 代码块、表格等
- **章节标题**: 清晰的文档结构

## 🚀 使用方法

### 扫描单个文件
```bash
python simple-scanner.py "path/to/design-document.md"
```

### 扫描整个目录
```bash
python simple-scanner.py "path/to/design/directory/"
```

## 📊 输出说明

### 单文件扫描输出
```
=== 文档扫描结果 ===
文件: path/to/document.md
总分: 75.0/100
提取器必需信息: 80.0/100
技术栈信息: 70.0/100
文档元数据: 60.0/100
可提取性: 90.0/100

发现 3 个问题:
  - 缺少文档ID
  - 缺少版本信息
  - 缺少设计哲学说明

改进建议:
  - 为提高80%提示词生成成功率，优先完善: 设计哲学
  - 补充重要内容: 文档ID, 版本信息
```

### 目录扫描输出
```
=== 目录扫描报告 ===
扫描文件数: 14
平均得分: 42.3/100
高质量文档: 1 个
需要改进: 11 个
通过率: 1/14 (7.1%)

最常见问题:
  - 核心定位: 14 次
  - 文档ID: 14 次
  - 设计哲学: 12 次

整体建议:
  - 🚨 关键：完善提取器必需信息以确保80%提示词生成成功
  - 📋 元数据：添加文档ID、版本、复杂度等级等元数据信息

详细报告已保存到: scan_report.json
```

## 📋 改进建议模板

### 高优先级问题（影响提取成功率）
1. **添加项目名称**
   ```markdown
   # XKongCloud Commons DB V3: 务实的企业级数据访问架构
   ```

2. **添加核心定位章节**
   ```markdown
   ## 🎯 核心定位
   一个强大、灵活、可扩展的、与Spring生态深度集成的数据访问基础库...
   ```

3. **添加设计哲学**
   ```markdown
   ## 设计哲学
   ### 组合优化设计哲学
   技术特性协同效应：性能倍增原理...
   ```

### 中优先级问题（提高提取质量）
1. **添加文档元数据**
   ```markdown
   ## 文档元数据
   - **文档ID**: `xkongcloud-commons-db-v3-pragmatic`
   - **版本**: `V3.0`
   - **复杂度等级**: `L2-中等复杂度`
   - **创建日期**: `2025-01-16`
   ```

2. **补充技术栈版本**
   ```markdown
   - **Java**: Java 21
   - **Spring Boot**: Spring Boot 3.4.5
   - **PostgreSQL**: PostgreSQL 17
   ```

## 🎯 与design_document_extractor.py的配合

1. **扫描检查**: 使用本程序检查文档质量
2. **问题修复**: 根据建议修复文档问题
3. **重新扫描**: 确认得分达到80分以上
4. **运行提取器**: 使用 `design_document_extractor.py` 生成提示词

### 推荐工作流
```bash
# 1. 扫描文档
python simple-scanner.py "docs/design/v1/"

# 2. 根据报告修复问题
# (手动编辑文档)

# 3. 重新扫描确认
python simple-scanner.py "docs/design/v1/"

# 4. 运行提取器生成提示词
python design_document_extractor.py \
  --design-docs-dir "docs/design/v1" \
  --output-dir "output/"
```

## 🔧 技术细节

### 检查算法
- 基于正则表达式模式匹配
- 权重计算确保关键信息优先
- 反模式检测避免常见问题

### 评分标准
- **80分以上**: 可以成功生成80%覆盖率提示词
- **60-79分**: 可能成功，建议优化
- **60分以下**: 提取失败风险高，需要重点改进

### 输出格式
- 控制台：简洁的问题和建议
- JSON文件：详细的扫描结果和统计信息

## 📈 成功案例

经过扫描和优化后的文档，`design_document_extractor.py` 的成功率可以从原来的20-30%提升到80%以上。

关键改进点：
1. 明确的项目名称和定位
2. 完整的技术栈版本信息
3. 结构化的文档组织
4. 必要的元数据信息
