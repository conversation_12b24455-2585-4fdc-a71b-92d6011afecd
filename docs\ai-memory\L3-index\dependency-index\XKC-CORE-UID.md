# 依赖关系索引：XKC-CORE与XKC-UID

## 基本信息

- **依赖类型**: 项目间依赖
- **主项目**: xkongcloud-business-internal-core (XKC-CORE)
- **依赖项目**: xkongcloud-commons-uid (XKC-UID)
- **依赖方向**: XKC-CORE依赖XKC-UID
- **依赖级别**: 强依赖
- **依赖范围**: 编译时和运行时
- **相关功能**: F003-PostgreSQL迁移, F004-CommonsUidLibrary

## 依赖说明

XKC-CORE项目强依赖XKC-UID项目，使用后者提供的分布式唯一ID生成功能。此依赖关系是PostgreSQL迁移过程中的重要部分，确保在分布式环境中实体对象能够获取全局唯一的ID，保证数据的一致性和完整性。

## 依赖配置

### Maven依赖

XKC-CORE项目在pom.xml中添加对XKC-UID的依赖：

```xml
<dependency>
    <groupId>com.xkongcloud</groupId>
    <artifactId>xkongcloud-commons-uid</artifactId>
    <version>${xkongcloud.uid.version}</version>
</dependency>
```

### 配置类依赖

XKC-CORE项目中的PostgreSQLConfig和UidGeneratorConfig类有依赖关系，UidGeneratorConfig需要使用PostgreSQLConfig提供的数据源：

```java
@Configuration
@DependsOn("kvParamService")
public class UidGeneratorConfig {

    @Autowired
    private KVParamService kvParamService;

    @Autowired
    private DataSource dataSource; // 依赖PostgreSQLConfig提供的数据源

    // 配置代码...
}
```

### 启动顺序依赖

UidGeneratorConfig依赖PostgreSQLConfig先初始化：

```java
@Configuration
@DependsOn({"kvParamService", "postgresqlDataSource"})
public class UidGeneratorConfig {
    // 配置代码...
}
```

### 关闭顺序依赖

UidGeneratorFacade需要在数据源关闭前先关闭：

```java
@Bean
@DependsOn("uidGeneratorFacade")
public UidShutdownOrderBean uidShutdownOrder(UidGeneratorFacade facade) {
    return new UidShutdownOrderBean(facade);
}

public static class UidShutdownOrderBean implements DisposableBean {
    private final UidGeneratorFacade facade;

    public UidShutdownOrderBean(UidGeneratorFacade facade) {
        this.facade = facade;
    }

    @Override
    @PreDestroy
    public void destroy() {
        if (facade != null) {
            facade.close();
        }
    }
}
```

## 依赖组件

### 核心依赖组件

1. **UidGeneratorFacade**
   - 提供简单的API获取UID
   - 隐藏内部复杂性
   - 自动管理资源生命周期

2. **PersistentInstanceManager**
   - 生成和存储唯一实例ID
   - 收集机器指纹特征码
   - 实现特征码匹配算法

3. **PersistentInstanceWorkerIdAssigner**
   - 分配持久化的WorkerId
   - 管理WorkerId的租约
   - 确保WorkerId的唯一性

4. **UidTableManager**
   - 创建和管理UID相关的表
   - 执行数据库迁移和升级
   - 提供表结构验证

### 服务接口依赖

1. **UidGenerator接口**
   - XKC-CORE使用此接口获取UID
   - UidGeneratorFacade实现此接口
   - 提供向后兼容性

2. **WorkerIdAssigner接口**
   - 负责分配WorkerId
   - PersistentInstanceWorkerIdAssigner实现此接口
   - 支持持久化和恢复

## 数据依赖

### 数据源共享

XKC-UID使用XKC-CORE提供的数据源访问数据库：

```java
@Bean
@Primary
public UidGeneratorFacade uidGeneratorFacade() {
    UidGeneratorFacade facade = new UidGeneratorFacadeBuilder()
        .withDataSource(dataSource) // 使用XKC-CORE提供的数据源
        .withSchemaName(getRequiredParam("uid.schema.name"))
        // 其他配置...
        .build();

    return facade;
}
```

### 表依赖

XKC-UID创建和管理以下表：

1. **infra_uid.worker_node表**：存储工作节点信息

2. **infra_uid.instance_registry表**：存储实例注册信息

### 参数依赖

XKC-UID通过XKC-CORE的KVParamService获取配置参数：

```java
@Autowired
private KVParamService kvParamService;

// 获取参数
private String getRequiredParam(String paramName) {
    String value = kvParamService.getParam(paramName);
    if (value == null || value.trim().isEmpty()) {
        throw new IllegalStateException("Required parameter '" + paramName + "' is missing");
    }
    return value;
}
```

## 依赖冲突与解决

### 潜在冲突

1. **数据源关闭顺序冲突**
   - 问题：如果数据源在UidGeneratorFacade关闭前关闭，会导致异常
   - 解决：使用UidShutdownOrderBean确保正确的关闭顺序

2. **参数冲突**
   - 问题：XKC-CORE和XKC-UID可能使用相同前缀的参数
   - 解决：使用不同的参数前缀，如"postgresql."和"uid."

3. **Schema冲突**
   - 问题：Schema名称可能冲突
   - 解决：使用"infra_uid"作为XKC-UID的专用Schema

## 依赖验证

### 启动时验证

1. **参数验证**
   - 验证所有必需的UID生成器参数是否存在
   - 验证参数值是否有效

2. **数据源验证**
   - 验证数据源是否可用
   - 验证数据库连接是否正常

3. **Schema验证**
   - 验证"infra_uid" Schema是否存在
   - 验证Schema权限是否正确

### 运行时验证

1. **心跳检测**
   - 定期更新WorkerId租约
   - 检测WorkerId是否有效

2. **异常处理**
   - 捕获并处理UID生成异常
   - 提供合适的降级策略

## 最佳实践

1. **门面模式强制使用**
   - 必须通过UidGeneratorFacade使用UID库的所有功能
   - 禁止直接创建或使用内部组件

2. **参数验证**
   - 所有从KV参数服务获取的参数必须进行非空验证
   - 缺少必需参数时应用应无法启动，并显示明确的错误信息

3. **资源管理**
   - 使用@DependsOn和DisposableBean确保正确的初始化和关闭顺序
   - 使用AutoCloseable接口自动管理资源

4. **错误处理**
   - 提供明确的错误信息和日志记录
   - 实现适当的恢复策略和降级机制

## 相关资源和文档

### 内部项目文档

1. [XKC-CORE与XKC-UID集成上下文](docs/ai-memory/L2-context/project-groups/xkc-core+uid.md)
2. [项目关系矩阵](docs/ai-memory/L1-core/project-matrix.md)
3. [F003-PostgreSQL迁移](docs/ai-memory/L3-index/feature-index/by-project/xkc-core/F003-PostgreSQL迁移.md)
4. [F004-CommonsUidLibrary](docs/features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md)
5. [全局硬约束](docs/ai-memory/L1-core/global-constraints.md)

### 相关技术文档

1. [百度UidGenerator PostgreSQL实现](docs/common/middleware/integration/baidu-uid-generator-postgresql-implementation.md)
2. [PostgreSQL技术栈上下文](docs/ai-memory/L2-context/tech-stack/postgresql-stack.md)
3. [数据库任务上下文](docs/ai-memory/L2-context/task-types/database-tasks.md) 