# 模块化 Attention Commands 使用指南

## 概述

本指南介绍如何使用 Phase 4 实现的模块化 attention commands 系统。该系统采用三级加载架构，提供了更高效、更灵活的命令管理机制。

## 三级加载架构

### 1. Immediate 加载 (立即加载)
**用途**: 核心约束和基础验证命令  
**加载时机**: 系统启动时立即加载  
**模块位置**: `docs/ai-memory/L1-core/commands/core/`

#### 包含的模块:
- `database-ops.json`: 数据库操作命令
  - `@ENFORCE_SCHEMA_NAMING`
  - `@ENFORCE_ENTITY_SCHEMA` 
  - `@DATABASE_CONFIG_CHECK`

- `config-validation.json`: 配置验证命令
  - `@UID_FACADE_ENFORCE`
  - `@PARAM_VALIDATION_CHECK`
  - `@DYNAMIC_PARAMETER_ARCHITECTURE_CHECK`
  - `@MEMORY_STATUS_QUICK_CHECK`

- `error-handling.json`: 错误处理命令
  - `@ERROR_HANDLING_CHECK`
  - `@RESOURCE_MANAGEMENT_CHECK`

### 2. Context 加载 (上下文加载)
**用途**: 基于任务类型和开发阶段的专业化命令  
**加载时机**: 根据任务类型和 RIPER-5 模式智能激活  
**模块位置**: `docs/ai-memory/L1-core/commands/`

#### Architecture 模块:
- `architecture/evolution.json`: `@EVOLUTIONARY_ARCHITECTURE_CHECK`
- `architecture/service-interface.json`: `@SERVICE_INTERFACE_CHECK`

#### Quality 模块:
- `quality/best-practices.json`: `@BEST_PRACTICE_CHECK`
- `quality/duplication-check.json`: `@CODE_DUPLICATION_CHECK`

#### Document 模块:
- `document/creation.json`: `@DOCUMENT_CREATION_CHECK`
- `document/standards.json`: `@DOCUMENT_STANDARDS_CHECK`, `@MEMORY_INTEGRATION_CHECK`
- `document/execution.json`: `@DOCUMENT_COMPLETENESS_CHECK`, `@EXECUTION_SCOPE_CONTROL`, `@DOCUMENT_PATH_VALIDATION`

### 3. On-Demand 加载 (按需加载)
**用途**: 复杂分析框架和专业化分析工具  
**加载时机**: 基于关键词检测和用户意图推理  
**模块位置**: `docs/ai-memory/L1-core/commands/analysis/`

#### Analysis 模块:
- `analysis/security.json`: `@STRIDE_THREAT_MODELING`, `@ATTACK_TREE_ANALYSIS`
- `analysis/technical.json`: `@FMEA_FAILURE_ANALYSIS`
- `analysis/business.json`: `@BUSINESS_LOGIC_RISK_ANALYSIS`, `@CONFIDENCE_ASSESSMENT`
- `analysis/metacognitive.json`: `@USER_PATTERN_ANALYSIS`, `@INTERACTION_OPTIMIZATION`, `@COMMUNICATION_ADAPTATION`

## 智能激活机制

### 任务类型映射
系统根据任务类型自动激活相应的模块:

```json
{
  "database_tasks": ["core/database-ops.json"],
  "architecture_tasks": ["architecture/evolution.json", "architecture/service-interface.json"],
  "testing_tasks": ["quality/best-practices.json", "analysis/technical.json"],
  "documentation_creation_tasks": ["document/creation.json"],
  "documentation_standards_tasks": ["document/standards.json"],
  "documentation_execution_tasks": ["document/execution.json"]
}
```

### RIPER-5 模式集成
不同开发阶段自动激活对应的命令模块:

- **RESEARCH**: `quality/best-practices.json`, `document/standards.json`
- **INNOVATE**: `architecture/evolution.json`, `document/creation.json`
- **PLAN**: `core/config-validation.json`, `core/error-handling.json`
- **EXECUTE**: `core/database-ops.json`, `core/config-validation.json`
- **REVIEW**: `core/database-ops.json`, `quality/best-practices.json`

### 关键词触发
系统基于关键词自动激活分析框架:

- **安全分析**: `安全风险评估`, `威胁分析`, `攻击路径` → `analysis/security.json`
- **技术分析**: `技术实现要点`, `失效模式`, `组件分析` → `analysis/technical.json`
- **业务分析**: `业务逻辑风险`, `业务规则分析` → `analysis/business.json`
- **综合分析**: `深度分析`, `系统性分析` → 激活所有分析框架

## 使用方法

### 1. 基本使用
模块化系统对用户透明，无需特殊操作。系统会根据上下文自动加载相应模块。

### 2. 手动激活特定模块
如需手动激活特定分析框架，可使用关键词:

```
用户: "请进行安全风险评估"
系统: [自动激活 analysis/security.json]

用户: "分析技术实现要点和约束条件"  
系统: [自动激活 analysis/technical.json]

用户: "深度分析系统架构"
系统: [自动激活所有分析框架]
```

### 3. 验证系统状态
使用增强的验证工具检查系统状态:

```bash
# 完整验证 (包含模块化功能)
node docs/ai-memory/tools/verify-memory-system.js

# 验证模块完整性
node docs/ai-memory/tools/verify-memory-system.js --module-integrity

# 性能监控
node docs/ai-memory/tools/verify-memory-system.js --performance

# 完整报告 (包含性能分析)
node docs/ai-memory/tools/verify-memory-system.js --full-report
```

## 配置管理

### 主配置文件
`docs/ai-memory/L1-core/attention-commands.json` 包含:

- `loading_strategy`: 三级加载策略配置
- `context_mapping`: 任务类型到模块的映射
- `on_demand_triggers`: 按需加载触发条件
- `riper5_integration`: RIPER-5 模式集成配置

### 模块文件结构
每个模块文件包含:

```json
{
  "metadata": {
    "module": "模块标识",
    "loading_priority": "加载优先级",
    "dependencies": "依赖关系",
    "version": "版本号"
  },
  "commands": {
    "@COMMAND_NAME": {
      "description": "命令描述",
      "execution_effects": ["执行效果"],
      "validation_rules": "验证规则"
    }
  },
  "activation_rules": {
    "keywords": "激活关键词",
    "task_patterns": "任务模式",
    "riper5_modes": "RIPER-5 模式"
  }
}
```

## 性能优化

### 加载优化
- **immediate 模块**: 系统启动时加载，确保核心功能立即可用
- **context 模块**: 智能路由，只加载相关模块
- **on-demand 模块**: 按需激活，减少内存占用

### 缓存机制
- 已加载模块自动缓存，避免重复加载
- 智能缓存清理，释放不再使用的模块
- 会话级缓存，提高响应速度

## 故障排除

### 常见问题

#### 1. 模块加载失败
**症状**: 特定命令无法激活  
**解决方案**: 
```bash
node docs/ai-memory/tools/verify-memory-system.js --module-integrity
```

#### 2. 性能问题
**症状**: 系统响应缓慢  
**解决方案**:
```bash
node docs/ai-memory/tools/verify-memory-system.js --performance
```

#### 3. 配置错误
**症状**: 智能激活不工作  
**解决方案**: 检查主配置文件格式和内容

### 回退机制
如遇到严重问题，可回退到 legacy 版本:

1. 备份当前配置
2. 重命名 `attention-commands-legacy.json` 为 `attention-commands.json`
3. 重启系统

## 扩展开发

### 添加新模块
1. 在相应目录创建新的 JSON 文件
2. 按照标准结构定义模块内容
3. 更新主配置文件的映射关系
4. 运行验证工具确认配置正确

### 修改现有模块
1. 直接编辑模块文件
2. 保持 JSON 格式正确性
3. 运行验证工具确认修改有效
4. 更新版本号

## 最佳实践

1. **模块职责单一**: 每个模块专注特定领域
2. **依赖关系清晰**: 明确模块间依赖关系
3. **版本控制**: 模块级别的版本管理
4. **定期验证**: 使用验证工具定期检查系统健康状态
5. **性能监控**: 定期进行性能监控和优化

## 总结

模块化 attention commands 系统提供了:
- **更高效的加载机制**: 三级加载架构
- **更智能的激活策略**: 基于上下文的自动激活
- **更好的维护性**: 模块化管理和独立更新
- **更强的扩展性**: 易于添加新功能和模块
- **更完善的质量保证**: 全面的验证和测试机制

通过合理使用这些功能，可以显著提高开发效率和系统性能。
