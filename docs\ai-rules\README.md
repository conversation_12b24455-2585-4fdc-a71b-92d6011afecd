# AI-DEV-FLOW规则系统

**文档ID**: G005
**创建日期**: 2023-05-01
**版本**: 1.0
**状态**: 已批准

本目录包含AI-DEV-FLOW工作流的AI规则系统，专注于任务导向的文档读取和记忆刷新。这些规则指导AI如何根据任务类型和阶段有选择地读取相关文档，刷新记忆，并提供更精准的支持。

## 目录结构

- [AI-DEV-FLOW规则系统概述](ai-dev-flow-rules.md) (G002)：整体规则系统结构、文档区域划分、模式感知和任务识别规则
- [文档读取和记忆刷新规则](document-reader-memory-refresh.md) (G003)：文档读取规则、记忆刷新规则、记忆刷新机制和实现示例
- [上下文压缩和RIPER-5协议集成](context-compression-riper5-integration.md) (G004)：上下文压缩技术、与RIPER-5协议的集成规则和实现示例
- [AI-DEV-FLOW规则系统使用指南](../guides/ai-dev-flow-usage-guide.md) (G001)：人工使用AI规则系统的操作指南

## 核心理念

AI-DEV-FLOW规则系统基于以下核心理念：

**AI-Driven Development**: This system is designed for AI-driven development where AI serves as the primary developer and humans provide supervision and decision-making guidance.

1. **任务导向的文档读取**：不是让AI读取所有文档，而是根据当前任务和阶段有选择地读取相关文档
2. **分阶段与非分阶段文档区域**：区分需要在特定阶段读取的文档和需要在所有阶段都读取的基础文档
3. **功能导向的文档组织**：按功能组织文档，每个功能有唯一ID和创建日期，便于查找和关联
4. **记忆刷新机制**：设计明确的记忆刷新触发点和机制，确保AI在需要时能够获取最新信息
5. **模式感知**：让AI能够识别当前处于RIPER-5的哪个模式，并据此读取相应文档
6. **上下文压缩**：优化文档内容的提取和压缩，确保在有限的上下文窗口中包含最重要的信息

## Document Audience Classification

**AI-Specific Documents**: All documents in the `docs/ai-rules` directory are primarily designed for AI consumption, focusing on technical specifications and automated processes.

**Human-AI Shared Documents**: Strategic documents requiring both AI execution and human oversight, including feature requirements and architectural decisions.

## 使用指南

### 1. 文档区域划分

AI-DEV-FLOW的文档区域划分为以下几类：

#### 1.1 基础文档区域（非分阶段）

这些文档在所有任务和阶段都应该被读取，提供基础上下文：

- **项目上下文**：项目的基本信息、目标和约束
- **决策日志**：重要决策及其理由
- **系统模式**：系统使用的设计模式和架构模式

#### 1.2 任务导向文档区域（分阶段）

这些文档根据RIPER-5的不同阶段有选择地读取：

- **RESEARCH阶段**：开发日志、架构文档、相关计划文档
- **INNOVATE阶段**：提示模板、成功会话记录、设计文档
- **PLAN阶段**：开发指南、相关计划文档、提示模板
- **EXECUTE阶段**：代码实现提示、测试要求、相关会话记录
- **REVIEW阶段**：代码审查提示、最佳实践

#### 1.3 任务类型文档区域

这些文档根据任务类型（而非阶段）有选择地读取：

- **功能开发任务**：功能开发提示、相关功能会话记录
- **错误修复任务**：错误修复提示、故障排除指南
- **代码重构任务**：重构提示、系统模式
- **测试相关任务**：测试策略制定、AI参数化测试、测试架构设计、风险分析

### 2. 记忆刷新机制

AI-DEV-FLOW采用以下记忆刷新机制：

- **会话开始刷新**：在每次会话开始时读取基础文档和任务相关文档
- **模式转换刷新**：在模式转换时读取新模式相关文档
- **任务变更刷新**：在任务变更时重新评估任务类型并读取相关文档
- **定期刷新**：在长会话中定期检查文档更新
- **显式刷新**：用户可以通过特定命令触发显式刷新

### 3. 与RIPER-5协议集成

AI-DEV-FLOW规则系统与RIPER-5协议无缝集成，支持：

- **模式映射**：将RIPER-5的模式映射到文档读取策略
- **思维原则应用**：根据当前模式应用相应的思维原则
- **模式转换规则**：定义模式转换的触发条件和转换行为

### 4. 功能文档组织与查找

AI-DEV-FLOW采用功能导向的文档组织方式：

- **功能目录**：每个功能有专门的目录 `docs/features/[功能ID-功能名称-日期]/`
- **功能README**：每个功能目录包含README.md，提供功能概述和文档索引
- **文档映射表**：中央文档映射表 `docs/feature-document-map.md` 列出所有功能及其文档
- **文档元数据**：每个文档包含功能ID、名称和相关文档链接
- **文档ID命名规则**：
  - 功能文档：F前缀（Feature），如F001、F002
  - 共享文档：C前缀（Common），如C001、C002
  - 指南文档：G前缀（Guide），如G001、G002
  - 模板文档：T前缀（Template），如T001、T002

查找特定功能的文档：
1. 通过文档映射表找到功能目录
2. 查看功能README获取文档索引
3. 访问具体文档

## 实施建议

1. **创建文档结构**：按照AI-DEV-FLOW的文档区域划分创建相应的目录结构
2. **编写基础文档**：首先编写基础文档区域的文档，提供项目的基础上下文
3. **添加分阶段文档**：根据项目需求，逐步添加分阶段文档
4. **配置AI助手**：使用本目录中的规则配置AI助手，使其能够根据任务和阶段读取相关文档
5. **持续优化**：根据实际使用情况，持续优化文档结构和规则系统

## 与其他工作流的比较

与SPARC+记忆库等其他工作流相比，AI-DEV-FLOW规则系统具有以下优势：

1. **轻量级**：减少不必要的文档负担，专注于最有价值的信息记录
2. **任务导向**：根据任务类型和阶段有选择地读取文档，提高效率
3. **上下文压缩**：优化文档内容的提取和压缩，确保在有限的上下文窗口中包含最重要的信息
4. **与RIPER-5集成**：与RIPER-5协议无缝集成，支持多维思考和严格的执行协议
5. **自动化程度高**：设计明确的记忆刷新触发点和机制，减少手动操作

## 贡献指南

欢迎对AI-DEV-FLOW规则系统进行贡献！您可以通过以下方式参与：

1. **提交问题**：如果您发现规则系统中的问题或有改进建议，请提交问题
2. **提交改进**：如果您有改进规则系统的想法，请提交改进建议
3. **分享经验**：分享您使用AI-DEV-FLOW规则系统的经验和最佳实践

## 许可证

本规则系统采用MIT许可证。
