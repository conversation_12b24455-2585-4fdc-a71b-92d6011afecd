/**
 * 项目约束审查 (ConstraintReviewComponent)
 * 
 * 功能：
 * - 默认显示提示信息。
 * - 接收来自其他组件的调用，显示特定约束的详细信息。
 * 
 * 数据依赖：
 * - constraint_detail: 包含要显示的约束的详细数据。
 */
class ConstraintReviewComponent extends BaseComponent {
    getDataTypes() {
        return ['constraint_detail'];
    }

    render() {
        const constraintData = this.getData('constraint_detail');

        this.container.innerHTML = `
            <div class="area-title">项目约束审查 (Constraint Review)</div>
            <div class="area-content" id="constraint-review-content">
                ${constraintData ? this.renderConstraintDetail(constraintData) : this.renderPlaceholder()}
            </div>
        `;
    }
    
    renderPlaceholder() {
        return `
            <div style="color: #666; text-align: center; margin-top: 2rem;">
                点击左侧知识库中的约束节点查看详细的AtomicConstraint结构
            </div>
        `;
    }

    renderConstraintDetail(data) {
        // 使用一个更详细的渲染模板
        return `
            <div class="constraint-detail-enhanced">
                <div class="constraint-header">
                    <span class="constraint-id">${data.id || 'N/A'}</span>
                    <span class="constraint-category category-${(data.category || 'constraint').toLowerCase().replace(/_/g, '-')}">${data.category || 'CONSTRAINT'}</span>
                </div>
                <div class="constraint-section">
                    <div class="section-title">基本信息</div>
                    <div class="field-grid">
                        <div class="field-item"><span class="field-label">来源:</span><span class="field-value">${data.source || '未知'}</span></div>
                        <div class="field-item"><span class="field-label">状态:</span><span class="field-value">${data.status || 'ACTIVE'}</span></div>
                    </div>
                </div>
                <div class="constraint-section">
                    <div class="section-title">参数</div>
                    <div class="params-tree">
                        ${Object.entries(data.parameters || {}).map(([key, value]) => `
                            <div class="param-node">
                                <span class="param-key">${key}:</span>
                                <span class="param-value">${JSON.stringify(value)}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                 <div class="constraint-section">
                    <div class="section-title">血统信息</div>
                    <div class="lineage-info">
                        <div class="lineage-item"><span class="lineage-label">父约束:</span><span class="lineage-value">${data.lineage?.parent_id || '无'}</span></div>
                        <div class="lineage-item"><span class="lineage-label">分叉自:</span><span class="lineage-value">${data.lineage?.forked_from || '无'}</span></div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 无直接事件绑定，通过公共方法更新
    }

    /**
     * 公共方法：显示约束详情
     * @param {object} constraintData - 要显示的约束数据
     */
    displayConstraint(constraintData) {
        this.dataManager.setData('constraint_detail', constraintData);
        // setData会自动触发onDataUpdate，然后调用render
    }
}