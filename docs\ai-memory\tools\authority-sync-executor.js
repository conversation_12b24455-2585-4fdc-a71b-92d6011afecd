/**
 * Authority Sync Executor for docs/common → docs/ai-memory Synchronization
 * 
 * This tool implements the single-direction synchronization from docs/common
 * to docs/ai-memory, ensuring AI memory content is always derived from
 * authoritative sources.
 */

const fs = require('fs');
const path = require('path');

class AuthoritySyncExecutor {
    constructor() {
        this.authorityRules = this.loadAuthorityRules();
        this.syncHistory = [];
        this.conflictLog = [];
    }

    loadAuthorityRules() {
        try {
            const rulesPath = path.join(__dirname, 'authority-rules.json');
            return JSON.parse(fs.readFileSync(rulesPath, 'utf8'));
        } catch (error) {
            console.error('Failed to load authority rules:', error);
            return null;
        }
    }

    async executeFullSync() {
        console.log('Starting full authority synchronization...');
        
        const syncResults = {
            timestamp: new Date().toISOString(),
            total_files: 0,
            synced_files: 0,
            failed_files: 0,
            conflicts_resolved: 0,
            errors: []
        };

        try {
            // Sync L1-core files
            await this.syncL1CoreFiles(syncResults);
            
            // Sync L2-context files
            await this.syncL2ContextFiles(syncResults);
            
            // Sync L3-index files
            await this.syncL3IndexFiles(syncResults);
            
            // Generate sync report
            return this.generateSyncReport(syncResults);
            
        } catch (error) {
            console.error('Full sync failed:', error);
            syncResults.errors.push(`Full sync error: ${error.message}`);
            return syncResults;
        }
    }

    async syncL1CoreFiles(syncResults) {
        console.log('Syncing L1-core files...');
        
        const l1CoreMappings = [
            {
                source: 'docs/common/best-practices/coding-standards/configuration-class-standards.md',
                target: 'docs/ai-memory/L1-core/global-constraints.json',
                syncType: 'constraint_update'
            },
            {
                source: 'docs/common/best-practices/coding-standards/dynamic-parameter-architecture-guide.md',
                target: 'docs/ai-memory/L1-core/attention-commands.json',
                syncType: 'command_update'
            },
            {
                source: 'docs/common/architecture/patterns/parameter-management-pattern.md',
                target: 'docs/ai-memory/L1-core/key-patterns.json',
                syncType: 'pattern_update'
            }
        ];

        for (const mapping of l1CoreMappings) {
            await this.syncFile(mapping, syncResults);
        }
    }

    async syncL2ContextFiles(syncResults) {
        console.log('Syncing L2-context files...');
        
        // Check if configuration-tasks.json needs to be created or updated
        const configTasksPath = 'docs/ai-memory/L2-context/task-types/configuration-tasks.json';
        
        if (!fs.existsSync(configTasksPath)) {
            await this.createConfigurationTasksContext(syncResults);
        } else {
            await this.updateConfigurationTasksContext(syncResults);
        }
    }

    async syncL3IndexFiles(syncResults) {
        console.log('Syncing L3-index files...');
        
        // L3-index files are typically feature-specific and managed separately
        // This would implement feature-specific index synchronization
        console.log('L3-index sync implementation would go here');
    }

    async syncFile(mapping, syncResults) {
        syncResults.total_files++;
        
        try {
            console.log(`Syncing ${mapping.source} → ${mapping.target}`);
            
            // Validate source authority
            if (!this.validateSourceAuthority(mapping.source)) {
                throw new Error(`Source file ${mapping.source} is not an authoritative source`);
            }

            // Check for conflicts
            const conflicts = await this.detectConflicts(mapping);
            if (conflicts.length > 0) {
                await this.resolveConflicts(mapping, conflicts);
                syncResults.conflicts_resolved += conflicts.length;
            }

            // Perform the sync
            await this.performSync(mapping);
            
            // Record sync history
            this.recordSyncHistory(mapping);
            
            syncResults.synced_files++;
            console.log(`Successfully synced ${mapping.target}`);
            
        } catch (error) {
            console.error(`Failed to sync ${mapping.target}:`, error);
            syncResults.failed_files++;
            syncResults.errors.push(`${mapping.target}: ${error.message}`);
        }
    }

    validateSourceAuthority(sourcePath) {
        // Validate that the source file is in docs/common/
        if (!sourcePath.startsWith('docs/common/')) {
            return false;
        }

        // Check if source file exists
        if (!fs.existsSync(sourcePath)) {
            return false;
        }

        return true;
    }

    async detectConflicts(mapping) {
        const conflicts = [];
        
        if (!fs.existsSync(mapping.target)) {
            return conflicts; // No conflicts if target doesn't exist
        }

        try {
            const targetContent = JSON.parse(fs.readFileSync(mapping.target, 'utf8'));
            
            // Check for authority metadata conflicts
            const authorityMetadata = targetContent.authority_metadata;
            if (authorityMetadata && authorityMetadata.source_file !== mapping.source) {
                conflicts.push({
                    type: 'authority_conflict',
                    description: `Target claims different source: ${authorityMetadata.source_file}`,
                    resolution: 'overwrite_with_correct_source'
                });
            }

            // Check for sync status conflicts
            if (authorityMetadata && authorityMetadata.sync_status === 'sync_in_progress') {
                conflicts.push({
                    type: 'sync_in_progress',
                    description: 'Another sync operation may be in progress',
                    resolution: 'wait_or_force_sync'
                });
            }

        } catch (error) {
            conflicts.push({
                type: 'parse_error',
                description: `Cannot parse target file: ${error.message}`,
                resolution: 'recreate_from_source'
            });
        }

        return conflicts;
    }

    async resolveConflicts(mapping, conflicts) {
        console.log(`Resolving ${conflicts.length} conflicts for ${mapping.target}`);
        
        for (const conflict of conflicts) {
            console.log(`Resolving conflict: ${conflict.type} - ${conflict.description}`);
            
            switch (conflict.resolution) {
                case 'overwrite_with_correct_source':
                case 'recreate_from_source':
                    // Authority takes precedence - will be handled in performSync
                    break;
                case 'wait_or_force_sync':
                    // For now, we force sync (could implement waiting logic)
                    console.log('Forcing sync despite sync_in_progress status');
                    break;
                default:
                    console.warn(`Unknown conflict resolution: ${conflict.resolution}`);
            }
            
            this.conflictLog.push({
                timestamp: new Date().toISOString(),
                mapping: mapping,
                conflict: conflict,
                resolution_applied: conflict.resolution
            });
        }
    }

    async performSync(mapping) {
        // This is a simplified implementation
        // In a full implementation, this would parse the source document
        // and generate appropriate AI memory content
        
        console.log(`Performing sync for ${mapping.syncType}`);
        
        switch (mapping.syncType) {
            case 'constraint_update':
                await this.syncConstraints(mapping);
                break;
            case 'command_update':
                await this.syncCommands(mapping);
                break;
            case 'pattern_update':
                await this.syncPatterns(mapping);
                break;
            default:
                throw new Error(`Unknown sync type: ${mapping.syncType}`);
        }
    }

    async syncConstraints(mapping) {
        // Update authority metadata in target file
        if (fs.existsSync(mapping.target)) {
            const targetContent = JSON.parse(fs.readFileSync(mapping.target, 'utf8'));
            
            // Update authority metadata
            targetContent.authority_metadata = {
                source_file: mapping.source,
                last_sync_date: new Date().toISOString(),
                sync_status: 'up_to_date',
                authority_validation: 'confirmed'
            };

            // Write updated content
            fs.writeFileSync(mapping.target, JSON.stringify(targetContent, null, 2));
        }
    }

    async syncCommands(mapping) {
        // Similar to syncConstraints but for command updates
        await this.syncConstraints(mapping);
    }

    async syncPatterns(mapping) {
        // Similar to syncConstraints but for pattern updates
        await this.syncConstraints(mapping);
    }

    async createConfigurationTasksContext(syncResults) {
        console.log('Creating configuration-tasks.json context...');
        
        const configTasksContent = {
            metadata: {
                title: "Configuration Tasks Context",
                description: "Task type context for configuration-related development tasks",
                version: "1.0",
                layer: "L2-context",
                type: "task-type",
                created_date: new Date().toISOString(),
                authority_metadata: {
                    source_file: "docs/common/best-practices/coding-standards/configuration-class-standards.md",
                    last_sync_date: new Date().toISOString(),
                    sync_status: "up_to_date",
                    authority_validation: "confirmed"
                }
            },
            task_patterns: {
                configuration_class_development: {
                    keywords: ["配置类", "configuration class", "@Parameter", "@RequiredParameters"],
                    auto_activate_commands: ["@DYNAMIC_PARAMETER_ARCHITECTURE_CHECK"],
                    related_documents: [
                        "docs/common/templates/config-class-template.java",
                        "docs/common/best-practices/coding-standards/configuration-class-standards.md"
                    ]
                },
                parameter_management: {
                    keywords: ["参数管理", "parameter management", "DynamicParameterAnalyzer"],
                    auto_activate_commands: ["@DYNAMIC_PARAMETER_ARCHITECTURE_CHECK", "@PARAM_VALIDATION_CHECK"],
                    related_documents: [
                        "docs/common/best-practices/coding-standards/dynamic-parameter-architecture-guide.md",
                        "docs/common/architecture/patterns/parameter-management-pattern.md"
                    ]
                }
            }
        };

        const targetPath = 'docs/ai-memory/L2-context/task-types/configuration-tasks.json';
        const targetDir = path.dirname(targetPath);
        
        // Ensure directory exists
        if (!fs.existsSync(targetDir)) {
            fs.mkdirSync(targetDir, { recursive: true });
        }

        fs.writeFileSync(targetPath, JSON.stringify(configTasksContent, null, 2));
        syncResults.synced_files++;
        console.log('Created configuration-tasks.json');
    }

    async updateConfigurationTasksContext(syncResults) {
        console.log('Updating configuration-tasks.json context...');
        
        const targetPath = 'docs/ai-memory/L2-context/task-types/configuration-tasks.json';
        const content = JSON.parse(fs.readFileSync(targetPath, 'utf8'));
        
        // Update authority metadata
        content.metadata.authority_metadata = {
            source_file: "docs/common/best-practices/coding-standards/configuration-class-standards.md",
            last_sync_date: new Date().toISOString(),
            sync_status: "up_to_date",
            authority_validation: "confirmed"
        };

        fs.writeFileSync(targetPath, JSON.stringify(content, null, 2));
        syncResults.synced_files++;
        console.log('Updated configuration-tasks.json');
    }

    recordSyncHistory(mapping) {
        const historyEntry = {
            timestamp: new Date().toISOString(),
            source: mapping.source,
            target: mapping.target,
            sync_type: mapping.syncType,
            status: 'completed'
        };

        this.syncHistory.push(historyEntry);
    }

    generateSyncReport(syncResults) {
        const report = {
            ...syncResults,
            sync_history: this.syncHistory,
            conflict_log: this.conflictLog,
            summary: {
                success_rate: syncResults.total_files > 0 ? 
                    (syncResults.synced_files / syncResults.total_files * 100).toFixed(2) + '%' : '0%',
                total_conflicts: this.conflictLog.length,
                authority_compliance: 'enforced'
            }
        };

        console.log('\n=== Authority Sync Report ===');
        console.log(`Total files processed: ${report.total_files}`);
        console.log(`Successfully synced: ${report.synced_files}`);
        console.log(`Failed: ${report.failed_files}`);
        console.log(`Conflicts resolved: ${report.conflicts_resolved}`);
        console.log(`Success rate: ${report.summary.success_rate}`);

        if (report.errors.length > 0) {
            console.log('\nErrors:');
            report.errors.forEach(error => console.log(`  ${error}`));
        }

        return report;
    }

    async rollbackToAuthority(targetFile) {
        console.log(`Rolling back ${targetFile} to authority source...`);
        
        // This would implement rollback logic
        // For now, we log the intended action
        console.log(`Would rollback ${targetFile} to its docs/common source`);
    }
}

// Export for use as a module
module.exports = AuthoritySyncExecutor;

// CLI usage
if (require.main === module) {
    const executor = new AuthoritySyncExecutor();
    
    const command = process.argv[2];
    
    if (command === 'sync') {
        executor.executeFullSync().then(report => {
            process.exit(report.failed_files > 0 ? 1 : 0);
        });
    } else if (command === 'rollback' && process.argv[3]) {
        executor.rollbackToAuthority(process.argv[3]);
    } else {
        console.log('Usage: node authority-sync-executor.js [sync|rollback <file>]');
        process.exit(1);
    }
}
