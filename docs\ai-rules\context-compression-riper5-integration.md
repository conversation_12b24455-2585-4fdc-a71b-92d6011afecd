# 上下文压缩和RIPER-5协议集成

**文档ID**: G004
**创建日期**: 2023-05-01
**版本**: 1.1
**状态**: 已批准

本文档定义了AI-DEV-FLOW工作流中的上下文压缩技术和与RIPER-5协议的集成规则，帮助AI在有限的上下文窗口中包含最重要的信息，并与RIPER-5协议无缝集成。

## 1. 上下文压缩规则

```javascript
// 上下文压缩规则
{
  "name": "contextCompression",
  "description": "优化文档内容的提取和压缩，确保在有限的上下文窗口中包含最重要的信息",

  // 文档摘要
  "documentSummarization": {
    "longDocument": {
      "threshold": 1000, // 字符数
      "strategy": {
        "extractSections": ["摘要", "目标", "关键点", "最近更新"],
        "summarizeRest": true,
        "maxLength": 500 // 字符数
      }
    },
    "mediumDocument": {
      "threshold": 500, // 字符数
      "strategy": {
        "extractSections": ["摘要", "关键点"],
        "summarizeRest": false,
        "maxLength": 300 // 字符数
      }
    },
    "shortDocument": {
      "threshold": 200, // 字符数
      "strategy": {
        "extractSections": [],
        "summarizeRest": false,
        "maxLength": null // 保持原样
      }
    }
  },

  // 相关性过滤
  "relevanceFiltering": {
    "taskBased": {
      "description": "根据当前任务过滤文档内容",
      "strategy": {
        "extractRelevantSections": true,
        "prioritizeRecentUpdates": true,
        "ignoreIrrelevantModules": true
      }
    },
    "modeBased": {
      "description": "根据当前模式过滤文档内容",
      "strategies": {
        "RESEARCH": {
          "focus": ["背景", "问题描述", "现状分析"],
          "ignore": ["实现细节", "测试结果"]
        },
        "INNOVATE": {
          "focus": ["可能方案", "创新点", "优缺点分析"],
          "ignore": ["历史记录", "实现细节"]
        },
        "PLAN": {
          "focus": ["步骤", "架构", "设计决策", "测试策略", "参数配置"],
          "ignore": ["背景", "历史记录"]
        },
        "EXECUTE": {
          "focus": ["实现细节", "代码示例", "测试要求", "参数化测试", "AI测试执行"],
          "ignore": ["背景", "可能方案"]
        },
        "REVIEW": {
          "focus": ["测试结果", "质量标准", "最佳实践"],
          "ignore": ["背景", "可能方案"]
        }
      }
    },
    "featureBased": {
      "description": "根据功能相关性过滤文档内容",
      "strategy": {
        "currentFeature": {
          "readEntire": true,
          "extractSummary": false
        },
        "relatedFeatures": {
          "readEntire": false,
          "extractSummary": true,
          "focusSections": ["功能概述", "相关功能", "API接口"]
        },
        "otherFeatures": {
          "readEntire": false,
          "extractSummary": true,
          "focusSections": ["功能概述"]
        }
      }
    }
  },

  // 时间衰减
  "timeDecay": {
    "recent": {
      "threshold": "7天",
      "weight": 1.0
    },
    "medium": {
      "threshold": "30天",
      "weight": 0.7
    },
    "old": {
      "threshold": "90天",
      "weight": 0.4
    },
    "veryOld": {
      "threshold": "180天",
      "weight": 0.2
    }
  },

  // 上下文窗口管理
  "contextWindowManagement": {
    "totalSize": 16000, // 令牌数
    "allocation": {
      "baseContext": 0.2, // 20%
      "taskContext": 0.3, // 30%
      "modeContext": 0.3, // 30%
      "conversationHistory": 0.2 // 20%
    },
    "overflowStrategy": {
      "priority": ["taskContext", "modeContext", "baseContext", "conversationHistory"],
      "action": "compress"
    },
    "boundaryConditionHandling": {
      "documentTooLarge": {
        "threshold": "单个文档超过分配空间的80%",
        "strategies": [
          {
            "condition": "critical priority document",
            "action": "intelligent_chunking",
            "description": "将文档分块，优先保留关键部分"
          },
          {
            "condition": "high priority document",
            "action": "aggressive_summarization",
            "description": "使用更激进的摘要策略"
          },
          {
            "condition": "medium/low priority document",
            "action": "skip_with_notification",
            "description": "跳过文档并通知用户"
          }
        ]
      },
      "multipleDocumentsOverflow": {
        "threshold": "总文档大小超过上下文窗口",
        "strategies": [
          {
            "step": 1,
            "action": "apply_priority_filtering",
            "description": "按优先级过滤文档"
          },
          {
            "step": 2,
            "action": "compress_lower_priority",
            "description": "压缩低优先级文档"
          },
          {
            "step": 3,
            "action": "temporal_filtering",
            "description": "应用时间衰减过滤"
          },
          {
            "step": 4,
            "action": "request_user_guidance",
            "description": "请求用户指导哪些文档最重要"
          }
        ]
      },
      "postgresqlSpecialCase": {
        "condition": "PostgreSQL任务且索引文档过大",
        "strategy": {
          "action": "selective_loading",
          "description": "只加载与当前任务直接相关的PostgreSQL索引部分",
          "implementation": "根据任务关键词过滤索引文档内容"
        }
      }
    }
  },

  // 边界情况处理规则
  "boundaryConditionRules": {
    "documentConflictResolution": {
      "description": "当多个文档对同一问题给出不同指导时的解决机制",
      "priorityOrder": [
        "总规则文档 (最高优先级 - 顶层设计)",
        ".augment-guidelines (项目规则)",
        "docs/ai-rules/ (AI规则实现)",
        "docs/common/ (共享文档)",
        "docs/features/ (功能文档)"
      ],
      "resolution": [
        "始终以总规则文档的规定为准",
        "低优先级文档必须服从高优先级文档",
        "发现冲突时，记录冲突并按优先级执行",
        "通知用户存在文档冲突需要解决"
      ]
    },
    "decisionAuthorityConflict": {
      "description": "当AI规则与总规则的决策权限定义冲突时",
      "resolution": [
        "严格遵循总规则中的决策权限边界",
        "AI只能在技术实现范围内自主决策",
        "涉及架构、方向性、基础层、业务逻辑的决策必须请求人类指导",
        "多个解决方案时必须请求人类决策"
      ]
    },
    "conflictingPriorities": {
      "description": "当多个文档都声明为critical优先级时的处理",
      "resolution": [
        "按照任务直接相关性排序",
        "优先保留当前模式最需要的文档",
        "将其他critical文档降级为high优先级"
      ]
    },
    "insufficientContext": {
      "description": "上下文窗口不足以包含最小必要信息时的处理",
      "resolution": [
        "保留核心项目信息和当前任务上下文",
        "暂时跳过历史信息和辅助信息",
        "通知用户上下文限制情况"
      ]
    },
    "rapidTaskSwitching": {
      "description": "用户快速切换任务导致频繁文档加载的处理",
      "resolution": [
        "实施文档缓存机制",
        "延迟加载非关键文档",
        "合并相似任务的文档需求"
      ]
    }
  }
}
```

## 2. 上下文压缩技术

为了在有限的上下文窗口中包含最重要的信息，AI-DEV-FLOW采用以下上下文压缩技术：

### 2.1 文档摘要

对于较长的文档，AI应该：
- 提取文档的关键信息（标题、摘要、最近更新）
- 忽略历史记录和详细解释
- 保留关键决策和当前状态

示例：
```python
def summarize_document(doc, doc_type):
    if len(doc) > 1000:  # 长文档
        summary = extract_sections(doc, ["摘要", "目标", "关键点", "最近更新"])
        if len(summary) < 500:
            return summary
        else:
            return summarize_text(summary, 500)
    elif len(doc) > 500:  # 中等文档
        return extract_sections(doc, ["摘要", "关键点"])
    else:  # 短文档
        return doc
```

### 2.2 相关性过滤

根据当前任务和模式，AI应该：
- 过滤掉与当前任务无关的文档部分
- 优先保留与当前模块/功能相关的信息
- 忽略与当前技术栈无关的内容

示例：
```python
def filter_by_relevance(doc, task_type, mode):
    # 任务相关过滤
    doc = filter_by_task(doc, task_type)

    # 模式相关过滤
    focus_sections = MODE_FOCUS_MAP[mode]
    ignore_sections = MODE_IGNORE_MAP[mode]

    filtered_doc = ""
    for section in doc.sections:
        if section.title in focus_sections:
            filtered_doc += section.full_content
        elif section.title not in ignore_sections:
            filtered_doc += section.summary

    return filtered_doc
```

### 2.3 时间衰减

对于历史信息，AI应该：
- 优先保留最近的信息
- 对较旧的信息进行更严格的相关性过滤
- 只保留关键的历史决策和背景

### 2.4 跨功能关联处理

对于涉及多个功能的任务，AI应该：
- 识别主要功能和相关功能
- 优先读取主要功能的完整文档
- 读取相关功能的摘要和关键接口
- 建立功能之间的关联关系

示例：
```python
def process_cross_feature_task(main_feature_id, related_feature_ids):
    # 读取主要功能文档
    main_feature_docs = read_feature_documents(main_feature_id, "full")

    # 读取相关功能摘要
    related_feature_summaries = []
    for feature_id in related_feature_ids:
        summary = read_feature_documents(feature_id, "summary")
        related_feature_summaries.append(summary)

    # 建立关联关系
    relationships = analyze_feature_relationships(main_feature_docs, related_feature_summaries)

    # 合并上下文
    context = {
        "main_feature": main_feature_docs,
        "related_features": related_feature_summaries,
        "relationships": relationships
    }

    return context
```

示例：
```python
def apply_time_decay(doc_sections):
    result = ""
    for section in doc_sections:
        age = calculate_age(section.last_updated)

        if age < 7:  # 7天内
            weight = 1.0
        elif age < 30:  # 30天内
            weight = 0.7
        elif age < 90:  # 90天内
            weight = 0.4
        else:  # 超过90天
            weight = 0.2

        if weight == 1.0:
            result += section.full_content
        elif weight >= 0.7:
            result += section.summary
        elif weight >= 0.4:
            result += section.key_points
        else:
            if section.is_critical:
                result += section.key_points

    return result
```

## 3. 与RIPER-5协议集成

```javascript
// RIPER-5协议集成规则
{
  "name": "riper5Integration",
  "description": "定义AI-DEV-FLOW如何与RIPER-5协议集成",

  // 模式映射 - Decision authority rules are synchronized with ai-dev-flow-rules.md (G002)
  "modeMapping": {
    "RESEARCH": {
      "purpose": "信息收集和深入理解",
      "thinkingPrinciples": ["systemsThinking", "criticalThinking"],
      "documentFocus": ["背景", "问题描述", "现状分析"],
      "outputFormat": "观察和问题"
    },
    "INNOVATE": {
      "purpose": "头脑风暴潜在方法",
      "thinkingPrinciples": ["dialecticalThinking", "innovativeThinking"],
      "documentFocus": ["可能方案", "创新点", "优缺点分析"],
      "outputFormat": "可能性和考虑因素",
      "decisionAuthority": {
        "trigger": "multiple_viable_solutions_identified",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When multiple solution approaches are identified",
        "requirement": "MANDATORY: Always request human decision before proceeding to PLAN mode",
        "escalationTriggers": ["multiple viable solutions", "architectural impact", "business logic changes"],
        "description": "当识别到多个可行解决方案时，必须请求人类决策后才能转换到PLAN模式"
      },
      "escalationRequired": true
    },
    "PLAN": {
      "purpose": "创建详尽的技术规范",
      "thinkingPrinciples": ["systemsThinking", "criticalThinking"],
      "documentFocus": ["步骤", "架构", "设计决策"],
      "outputFormat": "规范和实现细节",
      "decisionAuthority": {
        "trigger": "architectural_or_business_logic_changes",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When planning involves architectural, directional, foundation-level, or business logic changes",
        "requirement": "MANDATORY: Request human guidance before proceeding to EXECUTE mode",
        "escalationTriggers": ["architectural changes", "foundation-level decisions", "high complexity"],
        "description": "当计划涉及架构、方向性、基础层或业务逻辑变更时，必须请求人类指导后才能转换到EXECUTE模式",
        "documentCompletenessRequirement": {
          "description": "Plans must be sufficiently detailed to support execution without ambiguity",
          "requiredElements": [
            "Specific file paths and modifications",
            "Detailed function signatures and logic",
            "Clear step-by-step implementation sequence",
            "Error handling strategies",
            "Testing approaches"
          ],
          "completenessCheck": "Verify plan provides enough detail for unambiguous execution"
        }
      },
      "escalationRequired": true
    },
    "EXECUTE": {
      "purpose": "严格实施计划",
      "thinkingPrinciples": ["systemsThinking"],
      "documentFocus": ["实现细节", "代码示例", "测试要求"],
      "outputFormat": "实现代码",
      "decisionAuthority": {
        "trigger": "major_plan_deviations",
        "action": "ESCALATE_TO_HUMAN",
        "condition": "When major deviations from approved plan or unexpected architectural issues occur",
        "requirement": "MANDATORY: Escalate to human decision",
        "escalationTriggers": ["major deviations from approved plan", "unexpected architectural issues"],
        "description": "当执行过程中出现重大计划偏差或意外架构问题时，必须上报人类决策",
        "documentDrivenExecution": {
          "requirement": "All execution steps must have explicit documentation support",
          "refusalConditions": [
            "Documentation is missing for requested execution steps",
            "Documentation content is incomplete or ambiguous",
            "Plan document lacks sufficient detail for implementation",
            "Conflicting information exists in documentation"
          ],
          "refusalAction": "Refuse execution and request document completion before proceeding"
        }
      }
    },
    "REVIEW": {
      "purpose": "验证实现与计划的一致性",
      "thinkingPrinciples": ["criticalThinking"],
      "documentFocus": ["测试结果", "质量标准", "最佳实践"],
      "outputFormat": "系统比较和判断"
    }
  },

  // 思维原则应用
  "thinkingPrinciplesApplication": {
    "systemsThinking": {
      "description": "从整体架构到具体实现进行分析",
      "applicationStrategy": {
        "decomposeComponents": true,
        "analyzeRelationships": true,
        "considerBroaderImpacts": true
      }
    },
    "dialecticalThinking": {
      "description": "评估多种解决方案及其优缺点",
      "applicationStrategy": {
        "exploreSolutions": true,
        "evaluateProsAndCons": true,
        "synthesizeApproaches": true
      }
    },
    "innovativeThinking": {
      "description": "打破常规模式寻求创新解决方案",
      "applicationStrategy": {
        "challengeAssumptions": true,
        "exploreUnconventional": true,
        "combineIdeas": true
      }
    },
    "criticalThinking": {
      "description": "从多个角度验证和优化解决方案",
      "applicationStrategy": {
        "questionAssumptions": true,
        "evaluateEvidence": true,
        "identifyWeaknesses": true
      }
    }
  },

  // 模式转换规则
  "modeTransitionRules": {
    "autoTransition": true,
    "transitionIndicators": {
      "RESEARCH_to_INNOVATE": [
        "信息收集完成",
        "已经了解",
        "分析完毕"
      ],
      "INNOVATE_to_PLAN": [
        "方案确定",
        "创新完成",
        "已有解决方案"
      ],
      "PLAN_to_EXECUTE": [
        "计划制定完成",
        "步骤已明确",
        "设计完毕"
      ],
      "EXECUTE_to_REVIEW": [
        "实现完成",
        "代码已写好",
        "任务已完成"
      ]
    },
    "transitionActions": {
      "documentRefresh": true,
      "contextUpdate": true,
      "modeDeclaration": true
    }
  }
}
```

## 4. 实现示例

### 4.1 模式感知和文档读取集成

```python
def process_user_request(user_query):
    # 识别当前模式
    current_mode = identify_mode(user_query, conversation_history)

    # 声明模式
    declare_mode(current_mode)

    # 识别任务类型
    task_type = identify_task_type(user_query)

    # 读取相关文档
    base_docs = read_base_documents()
    task_docs = read_task_documents(task_type)
    mode_docs = read_mode_documents(current_mode)

    # 压缩上下文
    compressed_context = compress_context(base_docs, task_docs, mode_docs, current_mode, task_type)

    # 应用思维原则
    thinking_principles = get_thinking_principles(current_mode)
    response = apply_thinking_principles(user_query, compressed_context, thinking_principles)

    # 检查是否需要模式转换
    if should_transition_mode(current_mode, response):
        next_mode = get_next_mode(current_mode)
        transition_to_mode(next_mode)

    return response
```

### 4.2 RIPER-5思维原则应用

```python
def apply_thinking_principles(query, context, principles):
    response = ""

    if "systemsThinking" in principles:
        # 从整体架构到具体实现进行分析
        components = decompose_components(context)
        relationships = analyze_relationships(components)
        broader_impacts = consider_broader_impacts(relationships)
        response += generate_systems_thinking_response(query, components, relationships, broader_impacts)

    if "dialecticalThinking" in principles:
        # 评估多种解决方案及其优缺点
        solutions = explore_solutions(context, query)
        pros_cons = evaluate_pros_and_cons(solutions)
        synthesis = synthesize_approaches(solutions, pros_cons)
        response += generate_dialectical_thinking_response(query, solutions, pros_cons, synthesis)

    if "innovativeThinking" in principles:
        # 打破常规模式寻求创新解决方案
        assumptions = challenge_assumptions(context, query)
        unconventional = explore_unconventional(context, query)
        combined_ideas = combine_ideas(unconventional, context)
        response += generate_innovative_thinking_response(query, assumptions, unconventional, combined_ideas)

    if "criticalThinking" in principles:
        # 从多个角度验证和优化解决方案
        questioned_assumptions = question_assumptions(context, query)
        evidence = evaluate_evidence(context, query)
        weaknesses = identify_weaknesses(context, query)
        response += generate_critical_thinking_response(query, questioned_assumptions, evidence, weaknesses)

    return response
```
