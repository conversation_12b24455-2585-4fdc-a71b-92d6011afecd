# AI检查列表模板使用示例

## DRY原则引用方式

### 1. 基础引用模式

在创建具体功能的检查列表时，使用以下引用模式：

```markdown
## 🚨 AI记忆护栏检查清单
@checklist-templates:ai_memory_guardrail_system

## 🚨 AI执行目录位置提醒（必读）
@checklist-templates:directory_location_reminder_system

## 🚨 代码类型声明和边界管理
@checklist-templates:code_type_declaration_and_boundary_management
```

### 2. 完整检查列表模板引用

```markdown
# [功能名称]实施检查清单

**文档更新时间**: [当前时间]
**用途**: AI记忆辅助和进度跟踪
**检查频率**: 每完成一个步骤后更新
**关联计划**: [相关实施计划文档]

## 🚨 AI记忆护栏检查清单
@checklist-templates:ai_memory_guardrail_system

### 每日开始前检查
- [ ] 确认昨日完成的组件状态
- [ ] 验证现有代码编译通过
- [ ] 检查测试套件运行正常
- [ ] 确认工作目录状态正确
- [ ] [功能特定的额外检查项]

### 每个步骤完成后检查
- [ ] 代码编译无错误
- [ ] 单元测试通过
- [ ] 功能验证完成
- [ ] 文档更新完成
- [ ] 回滚方案确认
- [ ] [功能特定的验证项]

## 🚨 AI执行目录位置提醒（必读）
@checklist-templates:directory_location_reminder_system

### 当前文档位置
```
文档路径: [具体文档路径]
工作目录: c:\ExchangeWorks\xkong\xkongcloud (项目根目录)
相对路径: [具体相对路径]
```

## 🚨 AI记忆800行以内编程检查
@checklist-templates:cognitive_load_management
@checklist-templates:boundary_guardrail_activation
@checklist-templates:ai_hallucination_prevention
@checklist-templates:temporary_code_management

## 📋 [功能特定的实施步骤]

### 步骤1：[具体步骤名称]
**状态**: 🔲 未开始 / 🟡 进行中 / ✅ 已完成 / ❌ 失败

**完成标准**:
- [ ] [具体完成标准1]
- [ ] [具体完成标准2]

**验证命令**:
@checklist-templates:standardized_verification_commands
```bash
# 执行目录: c:\ExchangeWorks\xkong\xkongcloud (项目根目录)

# 编译验证
[具体编译命令]

# 功能验证
[具体功能验证命令]
```

**回滚方案**: @checklist-templates:rollback_mechanism
[具体回滚方案]

## 🔍 关键验证点
@checklist-templates:standardized_verification_commands

### 代码质量检查
```bash
# 编译检查
mvn clean compile

# 代码风格检查
mvn checkstyle:check

# 静态代码分析
mvn spotbugs:check
```

### 功能验证检查
```bash
# 单元测试
mvn test

# 集成测试
mvn verify
```

## 🧹 临时代码清理专项检查
@checklist-templates:temporary_code_management

### 最终清理验证
- [ ] **所有临时测试代码已删除**
  ```bash
  # 最终临时代码检查
  find [项目目录] -name "*Temporary*" -o -name "*Debug*" -o -name "*temp*"
  # 预期结果：无任何输出
  ```

- [ ] **所有调试输出已清理**
  ```bash
  # 检查调试输出
  grep -r "System\.out\|System\.err" [项目目录] | grep -v "// Expected"
  # 预期结果：无调试输出或仅有标记为预期的输出
  ```

## 📊 进度跟踪

### 总体进度
- Phase1：🔲 0% / 🟡 50% / ✅ 100% ([阶段描述])
- Phase2：🔲 0% / 🟡 50% / ✅ 100% ([阶段描述])

### 风险状态
- 🟢 低风险：按计划进行
- 🟡 中风险：需要关注
- 🔴 高风险：需要立即处理

### 问题记录
| 日期 | 问题描述 | 影响程度 | 解决方案 | 状态 |
|------|----------|----------|----------|------|
| 待记录 | 待发现问题 | 待评估 | 待制定 | 待处理 |
```

### 3. 特定检查项的引用

对于特定的检查需求，可以单独引用：

```markdown
## AI认知负载管理
@checklist-templates:cognitive_load_management

## 边界护栏激活
@checklist-templates:boundary_guardrail_activation

## 临时代码管理
@checklist-templates:temporary_code_management
```

### 4. 自动命令引用

在需要激活特定自动命令时：

```markdown
**AI实施提醒**:
- ⚠️ @BOUNDARY_GUARD_ACTIVATION
- ⚠️ @AI_COGNITIVE_CONSTRAINTS
- ⚠️ @MEMORY_BOUNDARY_CHECK
- ⚠️ @HALLUCINATION_PREVENTION
```

## 使用原则

1. **DRY原则**：避免重复内容，优先使用模板引用
2. **功能特定**：只添加功能特定的检查项，通用项使用引用
3. **保持更新**：模板更新时，所有引用自动生效
4. **标准化**：确保所有检查列表都有一致的结构和质量标准

## 模板更新流程

1. 识别新的共同元素
2. 更新 `checklist-templates.json`
3. 更新 `memory-index.json`
4. 更新现有检查列表使用新的引用
5. 验证所有引用正确工作

通过这种方式，我们可以确保所有检查列表都保持一致性，同时避免重复内容，提高维护效率。
