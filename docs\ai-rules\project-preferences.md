---
title: 项目偏好记录
document_id: G006
document_type: 指南
category: AI规则
scope: 全局
keywords: [项目偏好, 项目代码, 代码项目, 学习机制]
created_date: 2025-05-08
updated_date: 2025-05-08
status: 草稿
version: 0.1
authors: [AI助手]
related_docs:
  - docs/ai-rules/ai-dev-flow-rules.md
  - docs/feature-document-map.md
---

# 项目偏好记录

**文档ID**: G006
**创建日期**: 2025-05-08
**版本**: 0.1
**状态**: 草稿

本文档记录项目特定的模式和偏好，包括项目代码与代码项目的映射关系，以及在用户交互中学习到的项目偏好。这些信息帮助AI更好地理解项目结构和开发者的工作习惯，提供更精准的辅助。

## 1. 项目代码与代码项目映射

项目代码是功能模块的分组标识符，每个功能属于特定的项目代码。项目代码映射到实际的代码项目，便于快速定位相关功能文档。

### 1.1 映射表

| 项目代码 | 代码项目 | 描述 |
|---------|---------|------|
| XKC-CORE | xkongcloud-business-internal-core | 业务内部核心模块 |
| XKC-CENTER | xkongcloud-service-center | 服务中心模块 |
| XKC-COMMON | xkongcloud-common-exception | 通用异常处理模块 |

### 1.2 使用说明

- 当用户提供项目代码时，使用上述映射表查找对应的代码项目
- 当用户提供代码项目名称时，使用上述映射表查找对应的项目代码
- 使用项目代码查询 `docs/feature-document-map.md` 中的项目代码列，找到所有相关功能

## 2. 项目偏好记录

本节记录在与用户交互过程中学习到的项目偏好，这些偏好将指导AI在后续任务中提供更符合项目风格和要求的辅助。

### 2.1 代码风格偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| 异常处理 | 使用xkongcloud-common-exception进行统一异常管理 | 2025-05-08 | 所有异常处理场景 |
| 代码简洁性 | 保持代码简洁，减少重复代码块 | 2025-05-08 | 代码生成和重构 |

### 2.2 架构决策偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| 模块化 | 多模块项目结构，每个功能模块独立 | 2025-05-08 | 架构设计和代码组织 |

### 2.3 命名约定偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| 项目命名 | 使用xkongcloud-前缀命名项目 | 2025-05-08 | 项目和模块命名 |

### 2.4 文档格式偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| 文档语言 | 英文文档更易于AI识别和理解 | 2025-05-08 | 文档创建和更新 |
| 图表表示 | 使用mermaid图表表示流程和关系 | 2025-05-08 | 流程图和关系图创建 |

### 2.5 工作流偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| 文档先行 | 先修改文档再进行代码实现 | 2025-05-08 | 开发流程安排 |
| 严格遵循文档 | 严格按照文档执行操作，超出范围需先确认 | 2025-05-08 | 任务执行 |
| AI主导开发 | AI作为主要开发者，人类负责监督和决策指导 | 2025-05-08 | 所有开发活动 |
| 多方案决策 | 当识别到多个可行解决方案时，必须请求人类决策后才能转换到下一模式 | 2025-05-08 | INNOVATE和PLAN阶段 |

### 2.6 测试相关偏好

| 类别 | 偏好描述 | 学习日期 | 应用场景 |
|------|---------|---------|---------|
| AI测试流程 | AI制定开发和测试计划→执行测试输出优化的测试日志→AI分析日志等待人工审核后迭代 | 2025-05-08 | 测试执行流程 |
| 参数化测试系统 | 偏好复杂的参数化测试系统，包括流程参数、极限恶意情况测试，要求三层参数继承和覆盖规则 | 2025-05-08 | 测试架构设计 |
| AI-程序交互 | AI和程序之间使用JSON文档格式进行参数配置和交互，人和AI使用结构化自然语言交互 | 2025-05-08 | 测试参数配置 |
| 端到端测试 | 偏好在测试中增加从controller模拟浏览器开始的端到端测试，包括Web层验证 | 2025-05-08 | Web层测试 |

## 3. 学习触发点

以下是触发项目偏好学习的关键点：

1. **代码风格偏好**：用户重复纠正同类型的代码风格问题
2. **架构决策偏好**：用户明确表达对特定架构模式的偏好
3. **命名约定偏好**：用户重复修改同类型的命名
4. **文档格式偏好**：用户重复调整文档格式
5. **工作流偏好**：用户重复强调特定工作流程

## 4. 偏好应用机制

AI在以下情况下应用已学习的项目偏好：

1. 生成代码前，检查代码风格偏好
2. 提出架构方案前，检查架构决策偏好
3. 命名变量和函数前，检查命名约定偏好
4. 创建文档前，检查文档格式偏好
5. 规划任务执行前，检查工作流偏好

## 5. 反馈循环

项目偏好学习是一个持续的过程，包含以下步骤：

1. 记录用户对AI行为的反馈
2. 分析反馈模式，识别潜在的项目偏好
3. 更新项目偏好记录
4. 在后续任务中应用更新后的偏好
5. 持续监控用户反馈，优化偏好记录

## 变更历史

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 0.1 | 2025-05-08 | 初始版本 | AI助手 |
