/**
 * 项目进度监控 (ProjectProgressComponent)
 *
 * 功能：
 * - 显示项目四个阶段的总体进度。
 * - 展示阶段零的详细预验证指标。
 * - 统计关键指标，如原子约束、全局契约等。
 * - 显示整体项目完成百分比。
 * - 支持任务驱动工作流的工作区状态显示。
 * - 显示四阶段审查进度。
 *
 * 数据依赖：
 * - progress: 包含整体进度和关键指标的数据。
 * - stage_metrics: 包含阶段零的详细指标。
 * - admission_review: 包含审查任务的进度和状态。
 * - workspace_status: 包含工作区状态信息。
 */
class ProjectProgressComponent extends BaseComponent {
    constructor(containerId, dataManager) {
        super(containerId, dataManager);
        this.workspaceStatus = 'idle'; // idle, initializing, ready, reviewing, completed, failed
        this.reviewProgress = null;
    }

    getDataTypes() {
        return ['progress', 'stage_metrics', 'admission_review', 'workspace_status'];
    }

    render() {
        const progressData = this.getData('progress') || {};
        const metricsData = this.getData('stage_metrics') || {};
        const admissionReviewData = this.getData('admission_review') || {};
        const workspaceStatusData = this.getData('workspace_status') || {};

        // 更新内部状态
        this.workspaceStatus = workspaceStatusData.status || this.workspaceStatus;
        this.reviewProgress = admissionReviewData.progress || this.reviewProgress;

        this.container.innerHTML = `
            <div class="area-title">项目进度监控 (Process Overview)</div>
            <div class="area-content">
                <!-- 工作区状态指示器 -->
                ${this.renderWorkspaceStatus()}

                <!-- 审查进度（如果正在审查） -->
                ${this.workspaceStatus === 'reviewing' ? this.renderReviewProgress() : ''}

                <!-- V4.2四阶段流程进度 -->
                <div class="stage-progress">
                    <div class="stage-item stage-zero-highlight current">
                        <span class="status-indicator status-thinking"></span>
                        <span>阶段零：标准化与预验证</span>
                        <span class="badge badge-reliability">可靠性基石</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段一：全局契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段二：引用式契约生成</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段三：契约履行与审计</span>
                    </div>
                    <div class="stage-item pending">
                        <span class="status-indicator status-pending"></span>
                        <span>阶段四：整体性审计</span>
                    </div>
                </div>

                <!-- 阶段零专用指标 -->
                <div class="stage-zero-metrics">
                    <div class="metrics-title">
                        <span>🛡️</span>
                        <span>阶段零预验证指标</span>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">预验证通过率</span>
                            <span class="metric-value success">${metricsData.pre_validation_pass_rate || '100%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: ${metricsData.pre_validation_pass_rate || '100%'};"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">冲突预防数量</span>
                            <span class="metric-value info">${metricsData.conflict_prevention_count || '3'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 75%;"></div>
                        </div>
                    </div>
                    <div class="metric-item">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span class="metric-label">Schema验证通过</span>
                            <span class="metric-value success">${metricsData.schema_validation_passed || '25'}/${metricsData.schema_validation_total || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 100%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 关键指标统计 -->
                <div style="margin-top: 1rem;">
                    <div style="font-weight: bold; margin-bottom: 0.5rem;">关键指标:</div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已发现原子约束</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="atomic-constraints-count">${progressData.atomic_constraints_count || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 62.5%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已生成全局契约点</span>
                            <span style="color: #2196F3; font-weight: bold;" id="global-contracts-count">${progressData.global_contracts_count || '18'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 72%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>已处理文档</span>
                            <span style="color: #FF9800; font-weight: bold;"><span id="processed-docs-count">${progressData.processed_docs_count || '3'}</span>/${progressData.total_docs_count || '5'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 60%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>当前可靠性评分</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="current-reliability-score">${progressData.current_reliability_score || '87.7%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 87.7%;"></div>
                        </div>
                    </div>
                </div>

                <!-- 整体进度 -->
                <div style="margin-top: 1rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">整体进度:</div>
                    <div style="background: #2A2D30; padding: 0.8rem; border-radius: 4px; border: 1px solid #3C3F41;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.3rem;">
                            <span>${progressData.overall_progress_stage || '阶段零进行中'}</span>
                            <span style="color: #0078D4; font-weight: bold;">${progressData.overall_progress_percentage || '25%'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill primary" id="overall-progress" style="width: ${progressData.overall_progress_percentage || '25%'};"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 监听任务驱动工作流相关事件
        document.addEventListener('pm_v2_data_update', (event) => {
            const { eventType, data } = event.detail;
            switch (eventType) {
                case 'workspace_created':
                    this.handleWorkspaceCreated(data);
                    break;
                case 'admission_review_started':
                    this.handleReviewStarted(data);
                    break;
                case 'review_stage_progress':
                    this.handleReviewProgress(data);
                    break;
                case 'review_stage_completed':
                    this.handleStageCompleted(data);
                    break;
                case 'review_completed':
                    this.handleReviewCompleted(data);
                    break;
            }
        });
    }

    // 工作区状态渲染
    renderWorkspaceStatus() {
        const statusColors = {
            'idle': '#666',
            'initializing': '#FF9800',
            'ready': '#4CAF50',
            'reviewing': '#2196F3',
            'completed': '#4CAF50',
            'failed': '#F44336'
        };

        const statusTexts = {
            'idle': '等待初始化',
            'initializing': '正在初始化...',
            'ready': '工作区就绪',
            'reviewing': '审查进行中',
            'completed': '审查完成',
            'failed': '审查失败'
        };

        return `
            <div style="margin-bottom: 1rem; padding: 0.8rem; background: #2A2D30; border-radius: 4px; border: 1px solid #3C3F41;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: ${statusColors[this.workspaceStatus]};"></div>
                    <span style="font-weight: bold;">工作区状态:</span>
                    <span style="color: ${statusColors[this.workspaceStatus]};">${statusTexts[this.workspaceStatus]}</span>
                </div>
            </div>
        `;
    }

    // 审查进度渲染
    renderReviewProgress() {
        if (!this.reviewProgress) return '';

        const stages = [
            { name: '阶段一：全局契约生成', key: 'stage_1' },
            { name: '阶段二：引用式契约生成', key: 'stage_2' },
            { name: '阶段三：契约履行与审计', key: 'stage_3' },
            { name: '阶段四：整体性审计', key: 'stage_4' }
        ];

        return `
            <div style="margin-bottom: 1rem; padding: 0.8rem; background: #2A2D30; border-radius: 4px; border: 1px solid #3C3F41;">
                <div style="font-weight: bold; margin-bottom: 0.5rem;">四阶段审查进度:</div>
                ${stages.map(stage => {
                    const stageData = this.reviewProgress[stage.key] || {};
                    const status = stageData.status || 'pending';
                    const progress = stageData.progress || 0;

                    return `
                        <div style="margin-bottom: 0.3rem;">
                            <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                                <span style="font-size: 0.9rem;">${stage.name}</span>
                                <span style="color: #0078D4; font-size: 0.8rem;">${progress}%</span>
                            </div>
                            <div class="progress-bar" style="height: 4px; background: #3C3F41; border-radius: 2px;">
                                <div class="progress-fill" style="width: ${progress}%; height: 100%; background: ${status === 'completed' ? '#4CAF50' : '#2196F3'}; border-radius: 2px; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        `;
    }

    // 事件处理方法
    handleWorkspaceCreated(data) {
        this.workspaceStatus = 'ready';
        this.render();
    }

    handleReviewStarted(data) {
        this.workspaceStatus = 'reviewing';
        this.reviewProgress = data.progress || {};
        this.render();
    }

    handleReviewProgress(data) {
        if (this.reviewProgress) {
            Object.assign(this.reviewProgress, data);
            this.render();
        }
    }

    handleStageCompleted(data) {
        if (this.reviewProgress && data.stage) {
            this.reviewProgress[data.stage] = { ...this.reviewProgress[data.stage], status: 'completed', progress: 100 };
            this.render();
        }
    }

    handleReviewCompleted(data) {
        this.workspaceStatus = 'completed';
        this.render();
    }
}