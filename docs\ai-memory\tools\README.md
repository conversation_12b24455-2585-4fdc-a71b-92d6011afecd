# AI记忆系统工具集使用指南

本目录包含用于管理和验证AI记忆系统的工具集，支持功能文档同步验证和docs/common权威性管理。

## 🚀 推荐工具

### unified-validator.js (统一验证器) ⭐
**用途**: 一键完成所有验证，无需分别运行多个工具
**推荐使用**: 日常验证的首选工具
**当前状态**: ✅ 完全可用，权威性验证100%通过

```bash
# 完整系统验证
node docs/ai-memory/tools/unified-validator.js
```

**验证覆盖范围**:
- 📁 **文档结构验证** - 检查关键目录和文件存在性
- 📋 **功能文档验证** - 验证功能状态和文档同步状态
- 🔐 **权威性验证** - 检查docs/common权威性合规（14/14通过）
- 🔄 **同步状态验证** - 检查功能同步时效性和超时警告
- 🧠 **记忆系统完整性** - 验证AI记忆层级结构完整性

**智能特性**:
- 🎯 **综合报告** - 自动生成修复建议和优先级排序
- 📊 **健康度评估** - 提供系统健康度评分和状态分析
- 🔧 **问题诊断** - 智能识别问题类型并提供解决方案
- 📈 **趋势分析** - 支持系统状态变化趋势监控

**快速开始**: [QUICK-START.md](./QUICK-START.md) | **详细文档**: [UNIFIED-VALIDATOR-README.md](./UNIFIED-VALIDATOR-README.md)

## 工具概览

> **注意**: 推荐使用上述 `unified-validator.js` 进行日常验证。以下工具主要用于特定场景或高级用户。

### 核心验证工具

#### 1. memory-system-validator.js (分层验证入口)
**用途**: 协调多个验证工具，提供分层验证策略
**推荐使用**: 日常验证的主要入口

```bash
# 快速检查 (Level 1)
node memory-system-validator.js --quick

# 功能验证 (Level 2, 默认)
node memory-system-validator.js
node memory-system-validator.js --feature F003

# 权威性验证 (Level 3)
node memory-system-validator.js --level3

# 完整验证 (Level 4)
node memory-system-validator.js --full
```

#### 2. verify-memory-system.js (功能文档验证)
**用途**: 验证功能文档与记忆索引的同步状态
**适用场景**: 功能开发过程中的状态检查

```bash
# 基本验证
node verify-memory-system.js

# 验证特定功能
node verify-memory-system.js --feature F003

# 快速检查 (实现@MEMORY_STATUS_QUICK_CHECK)
node verify-memory-system.js --quick-check

# 权威性验证
node verify-memory-system.js --check-authority

# 完整报告
node verify-memory-system.js --full-report
```

### 权威性管理工具

#### 3. authority-validator.js (权威性验证)
**用途**: 验证docs/ai-memory内容的权威性合规
**当前状态**: ✅ 验证通过率100% (14/14文件)
**适用场景**: 标准更新后的权威性检查

```bash
# 验证权威性
node authority-validator.js validate

# 修复权威性问题
node authority-validator.js fix
```

#### 4. authority-sync-executor.js (权威性同步)
**用途**: 执行docs/common → docs/ai-memory同步
**限制**: 仅支持部分预定义文件映射
**适用场景**: 标准文档更新后的同步操作

```bash
# 执行完整同步
node authority-sync-executor.js sync

# 回滚到权威源
node authority-sync-executor.js rollback <file>
```

#### 5. fix-authority-sources.js (权威性源文件修复器) 🆕
**用途**: 智能修复权威性源文件路径错误
**特性**: 自动匹配存在的docs/common文件
**适用场景**: 权威性验证失败时的自动修复

```bash
# 智能修复权威性源文件路径
node fix-authority-sources.js
```

#### 6. add-authority-metadata.js (权威性元数据批量添加器) 🆕
**用途**: 批量为缺少权威性元数据的文件添加元数据
**特性**: 自动检测已有元数据，避免重复添加
**适用场景**: 新文件缺少权威性元数据时的批量处理

```bash
# 批量添加权威性元数据
node add-authority-metadata.js
```

#### 7. adaptation-detector.js (变更检测)
**用途**: 监控docs/common变更并触发自动适应
**适用场景**: 持续监控模式

```bash
# 启动监控
node adaptation-detector.js
```

#### 6. authority-rules.json (权威性规则)
**用途**: 定义权威性原则和语义映射规则
**维护**: 配置文件，通常不需要直接修改

## 使用场景和最佳实践

### 🚀 推荐工作流（使用统一验证器）

#### 日常开发工作流
1. **开发前检查**
   ```bash
   node unified-validator.js
   ```

2. **开发后验证**
   ```bash
   node unified-validator.js
   ```

3. **问题修复**
   ```bash
   # 根据验证报告的修复建议执行相应命令
   node fix-authority-sources.js  # 权威性问题
   @sync:feature:F003             # 同步问题
   ```

#### 权威性问题修复工作流
1. **发现权威性验证失败**
   ```bash
   node unified-validator.js  # 识别问题
   ```

2. **自动修复（推荐）**
   ```bash
   node fix-authority-sources.js      # 修复源文件路径
   node add-authority-metadata.js     # 添加缺失元数据
   ```

3. **验证修复结果**
   ```bash
   node unified-validator.js  # 确认修复成功
   ```

### 🔧 高级用户工作流（使用专用工具）

#### 日常开发工作流
1. **开始功能开发前**
   ```bash
   node memory-system-validator.js --quick --feature F003
   ```

2. **功能开发过程中**
   ```bash
   node verify-memory-system.js --feature F003
   ```

3. **功能开发完成后**
   ```bash
   node memory-system-validator.js --level2 --feature F003
   ```

#### 标准文档更新工作流
1. **更新docs/common文档后**
   ```bash
   node authority-sync-executor.js sync
   ```

2. **验证同步结果**
   ```bash
   node memory-system-validator.js --level3
   ```

3. **完整系统验证**
   ```bash
   node memory-system-validator.js --full
   ```

#### 故障排除工作流
1. **发现问题时**
   ```bash
   node memory-system-validator.js --full
   ```

2. **权威性问题修复**
   ```bash
   node authority-validator.js fix
   node authority-sync-executor.js sync
   ```

3. **验证修复结果**
   ```bash
   node memory-system-validator.js --level3
   ```

## 工具关系和职责边界

### 功能层级验证
- **verify-memory-system.js**: 当前开发功能文档 ↔ 记忆索引同步
- **范围**: docs/features (仅active_development) ↔ docs/ai-memory/L3-index
- **关注点**: 当前开发状态、同步时效性
- **重要原则**: docs/features是临时工作区域，不作为历史参考

### 标准层级验证
- **authority-validator.js**: docs/common权威性合规
- **authority-sync-executor.js**: docs/common → docs/ai-memory同步
- **范围**: docs/common ↔ docs/ai-memory/L1-core+L2-context
- **关注点**: 架构标准、权威性一致性

### 统一协调
- **memory-system-validator.js**: 协调所有验证工具
- **范围**: 全系统验证
- **关注点**: 分层验证策略、统一报告

## 验证级别说明

### Level 1: 快速检查
- **目的**: 快速确认功能状态和文档使用建议
- **时间**: < 5秒
- **适用**: 开发过程中的频繁检查

### Level 2: 功能验证
- **目的**: 验证功能文档同步状态和一致性
- **时间**: 10-30秒
- **适用**: 功能开发完成后的验证

### Level 3: 权威性验证
- **目的**: 验证docs/common权威性和AI记忆合规性
- **时间**: 30-60秒
- **适用**: 标准文档更新后的验证

### Level 4: 完整验证
- **目的**: 全系统完整性验证
- **时间**: 1-2分钟
- **适用**: 重要变更后的全面检查

## 错误处理和恢复

### 常见错误类型

1. **功能状态不一致**
   - 症状: 功能状态与实际开发状态不符
   - 解决: 更新docs/feature-status.json

2. **记忆索引过期**
   - 症状: sync_status为requires_update
   - 解决: 手动同步或使用@sync:feature命令

3. **权威性验证失败**
   - 症状: 缺少authority_metadata或源文件不匹配
   - 解决:
     ```bash
     # 推荐方案（自动修复）
     node fix-authority-sources.js
     node add-authority-metadata.js

     # 传统方案
     node authority-sync-executor.js sync
     ```

4. **文档结构问题**
   - 症状: 关键目录或文件缺失
   - 解决: 检查文档结构，重建缺失组件

### 恢复策略

1. **渐进式修复**: 从Level 1开始，逐级修复问题
2. **权威性优先**: 权威性问题优先修复，确保数据源正确
3. **备份恢复**: 严重问题时回滚到已知良好状态

## 性能优化建议

1. **选择合适的验证级别**: 避免过度验证
2. **缓存验证结果**: 短时间内避免重复验证
3. **并行验证**: 独立验证项目可以并行执行
4. **增量验证**: 只验证变更相关的部分

## 集成和自动化

### CI/CD集成
```bash
# 在CI/CD管道中使用
node memory-system-validator.js --level3
```

### Git Hooks集成
```bash
# pre-commit hook
node memory-system-validator.js --quick
```

### 监控集成
```bash
# 持续监控
node adaptation-detector.js &
```

## docs/features使用原则

### 重要说明
docs/features目录有特殊的使用原则，必须严格遵循：

1. **临时工作区域**: docs/features仅用于当前功能开发索引，不是永久存储
2. **不作历史参考**: 历史功能信息应通过记忆索引系统访问
3. **状态感知验证**: 只有active_development状态的功能才验证原始文档存在性
4. **生命周期管理**: 功能完成后，相关文档应归档到记忆索引系统

### 验证工具的遵循原则
- **verify-memory-system.js**: 只验证active_development功能的docs/features文档
- **memory-system-validator.js**: 协调验证时遵循相同原则
- **authority-validator.js**: 专注于docs/common权威性，不涉及docs/features

### 常见误区避免
- ❌ 不要将docs/features作为历史功能的参考源
- ❌ 不要验证所有功能的原始文档存在性
- ❌ 不要依赖docs/features进行长期文档管理
- ✅ 使用记忆索引系统访问历史功能信息
- ✅ 只关注当前开发功能的文档状态
- ✅ 遵循文档生命周期管理原则

## 故障排除清单

- [ ] 检查Node.js版本兼容性
- [ ] 确认所有依赖文件存在
- [ ] 验证文件权限设置
- [ ] 检查JSON文件格式正确性
- [ ] 确认docs/common和docs/ai-memory目录结构
- [ ] 验证feature-status.json内容完整性
- [ ] 确认遵循docs/features使用原则
