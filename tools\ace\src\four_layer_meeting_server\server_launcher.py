#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
四重会议Web服务器 - 提供Web界面，直接调用现有指挥官
V4.5客户端-服务端分离架构 - Web服务器端实现

核心作用：
1. 人机交互桥梁：用户和指挥官之间的交互界面
2. MCP客户端通讯管理：管理与MCP客户端的WebSocket连接
3. 远程执行代理：为指挥官提供透明的远程执行能力

设计原则：
- 直接调用现有指挥官（不迁移任何组件）
- 透明化远程执行（指挥官不知道是本地还是远程）
- 严格概念分离（Web服务器概念，不使用MCP术语）
"""

import asyncio
import json
import threading
import signal
import subprocess
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, List

class ProjectManager:
    """统一的项目管理器 - V45边界清晰化改进"""

    def __init__(self):
        self.project_containers = {}  # {project_name: UniversalProjectContainer}
        self.project_commanders = {}  # {project_name: PythonCommanderMeetingCoordinatorV45Enhanced}
        print("✅ 统一项目管理器初始化完成")

    def create_project(self, project_name: str, meeting_path: str = None):
        """权威的项目创建方法"""
        try:
            if project_name in self.project_containers:
                print(f"⚠️ 项目已存在: {project_name}")
                return self.project_containers[project_name]

            # 创建项目级指挥官实例
            commander = PythonCommanderMeetingCoordinatorV45Enhanced()
            self.project_commanders[project_name] = commander

            # 创建项目容器
            container = self._create_project_container(project_name)

            # 绑定指挥官到容器
            if container:
                commander.bind_universal_container(container)
                print(f"✅ 项目 {project_name} 的指挥官已绑定到容器")

            # 如果有meeting_path，设置项目路径
            if meeting_path and container:
                container.update_state("system", {"meeting_path": meeting_path}, "project_manager")

            print(f"✅ 项目创建成功: {project_name}")
            return container

        except Exception as e:
            print(f"❌ 创建项目失败 {project_name}: {e}")
            return None

    def get_project(self, project_name: str):
        """权威的项目获取方法"""
        return self.project_containers.get(project_name)

    def get_project_commander(self, project_name: str):
        """获取项目指挥官"""
        return self.project_commanders.get(project_name)

    def list_projects(self):
        """列出所有项目"""
        return list(self.project_containers.keys())

    def _create_project_container(self, project_name: str):
        """创建项目容器的内部方法"""
        try:
            # 导入必要的模块
            from project_container.universal_project_container import UniversalProjectContainer
            from four_layer_meeting_system.project_context_manager import ProjectContextManager

            # 🚀 V45容器化改造：通过ProjectContextManager获取项目配置
            project_config = ProjectContextManager.get_project_context_by_name(project_name)

            if not project_config:
                # 如果没有找到项目配置，使用默认配置
                project_config = ProjectContextManager._get_default_context()
                project_config["project_name"] = project_name
                print(f"⚠️ 项目 {project_name} 使用默认配置")

            # 创建项目容器（使用配置文件驱动的配置）
            container = UniversalProjectContainer(project_name, project_config)
            self.project_containers[project_name] = container

            print(f"✅ 项目容器创建成功: {project_name}")
            return container

        except Exception as e:
            print(f"❌ 创建项目容器失败 {project_name}: {e}")
            return None

def _get_restart_flag_file_path_static() -> str:
    """静态函数：获取重启标志文件路径 - V45容器化改造：配置驱动"""
    try:
        # 通过配置管理系统获取路径
        from unified_config_manager import UnifiedConfigManager

        # 尝试从runtime_config获取
        restart_flag_file = UnifiedConfigManager.get_config("temporary.restart_flag_file")
        if restart_flag_file:
            # 确保目录存在
            restart_flag_dir = UnifiedConfigManager.get_config("temporary.restart_flag_directory", "data/runtime")
            os.makedirs(restart_flag_dir, exist_ok=True)
            return restart_flag_file

    except Exception as e:
        print(f"⚠️ 无法从配置获取重启标志文件路径: {e}")

    # 回退到相对路径（避免硬编码绝对路径）
    fallback_dir = "data/runtime"
    os.makedirs(fallback_dir, exist_ok=True)
    return os.path.join(fallback_dir, ".restart_requested")

# WebSocket依赖处理
try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    print("⚠️ websockets模块不可用，WebSocket功能将不可用")
    WEBSOCKETS_AVAILABLE = False

# 直接使用现有组件（不迁移）
import sys
import os

# 添加src路径以便导入
src_path = os.path.join(os.path.dirname(__file__), '..')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    from web_interface.app import create_app as create_web_app
    from python_host.python_host_core_engine import PythonCommanderMeetingCoordinatorV45Enhanced
    WEB_INTERFACE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Web界面组件不可用: {e}")
    WEB_INTERFACE_AVAILABLE = False


class ServerStateManager:
    """服务器状态持久化管理器 - V45架构：状态外置，不维护复杂状态"""

    def __init__(self):
        # ✅ V45架构：状态外置，不维护任何持久化状态
        print("✅ ServerStateManager初始化完成（V45状态外置架构）")

    def save_tokens(self):
        """
        V45架构：状态外置，不保存任何状态
        """
        # ✅ V45架构：状态外置，不维护任何持久化状态
        pass

    def load_persistent_state(self):
        """
        V45架构：状态外置，不加载任何状态
        """
        # ✅ V45架构：状态外置，不维护任何持久化状态
        print("ℹ️ [StateManager] V45架构：状态外置，无需加载状态")
        return

class SimplifiedMCPServer:
    """简化的MCP服务器 - 状态中心化设计（P0安全增强版）

    V45容器化改造：支持多项目容器管理和启动流程
    """

    def __init__(self):
        print("✅ SimplifiedMCPServer初始化开始（V45容器化版本）...")

        # 🚀 V45容器化改造：初始化项目上下文管理器
        try:
            from four_layer_meeting_system.project_context_manager import ProjectContextManager
            ProjectContextManager.initialize(self)
            print("✅ ProjectContextManager初始化成功")
        except Exception as e:
            print(f"⚠️ ProjectContextManager初始化失败: {e}")

        # 🚀 V45容器化改造：统一项目管理器
        self.project_manager = ProjectManager()

        # 🔧 IDEA式启动：不创建默认指挥官，等待项目选择
        # 初始化项目状态管理器
        try:
            from four_layer_meeting_server.project_status_manager import ProjectStatusManager
            self.project_status_manager = ProjectStatusManager()
            print("✅ 项目状态管理器已初始化")
        except ImportError as e:
            # 如果ProjectStatusManager导入失败，使用临时占位符
            print(f"⚠️ 项目状态管理器导入失败: {e}")
            self.project_status_manager = None
            print("⚠️ 项目状态管理器未找到，将在Phase 2中创建")
        except Exception as e:
            # 如果ProjectStatusManager初始化失败
            print(f"⚠️ 项目状态管理器初始化失败: {e}")
            self.project_status_manager = None
            print("⚠️ 项目状态管理器初始化失败，将使用临时占位符")

        print("✅ 服务器已就绪，等待项目选择")

        # 直接使用现有Web界面
        if WEB_INTERFACE_AVAILABLE:
            # 🔧 IDEA式启动：通过依赖注入传递项目管理器和服务器实例
            self.web_app = create_web_app(self.project_manager, self)
            self.web_thread = None  # 🔧 修复：保存Web线程引用，用于关闭
            print("✅ Web界面系统已加载（IDEA式启动模式）")

        else:
            self.web_app = None
            self.web_thread = None

        # ✅ V45架构：状态外置，保留StateManager但不维护状态
        self.state_manager = ServerStateManager()

        # 🔧 按照V4.5简化容错机制设计文档实现
        # 核心状态：项目根目录 → 客户端映射
        self.project_clients = {}  # {project_root: client_id} - 统一命名规范

        # ✅ V45架构：删除task_queue - 使用同步等待机制，不需要状态管理
        # 按照设计文档第546行要求：消除任务队列复杂性

        # 连接管理
        self.client_connections = {}  # {client_id: websocket}

        # ✅ V45架构：同步等待机制（设计文档第424行要求）

        # ✅ V45架构：线程安全的事件循环通信机制
        self._main_event_loop = None  # 主事件循环引用（用于线程安全通信）

        # P0修复：连接锁机制 - 防止并发连接竞态
        self.connection_locks = {}    # {project_root: asyncio.Lock}

        # 重启控制标志 - V4.5架构修复：重置为False，确保按设计文档工作
        self._restart_requested = False

        # V4.5架构修复：清理任何残留的重启标志文件，确保干净启动
        # 🚀 V45容器化改造：通过配置管理系统获取重启标志文件路径
        restart_flag_file = self._get_restart_flag_file_path()
        if os.path.exists(restart_flag_file):
            try:
                os.remove(restart_flag_file)
                print("🧹 V4.5架构修复：清理残留重启标志文件")
            except Exception as e:
                print(f"⚠️ 清理重启标志文件失败: {e}")

        # 内存中的调试日志存储（必须在_load_task_queue之前初始化）
        self.debug_logs = []
        self.max_debug_logs = 500  # 最多保存500条调试日志

        # ✅ V45架构：简化状态管理器初始化
        self.state_manager.load_persistent_state()

        # 🎯 V45统一文件操作抽象层集成 - 一行代码完成初始化
        try:
            from four_layer_meeting_system.remote_file_operator import RemoteClientFactory
            RemoteClientFactory.initialize(self)
            print("✅ V45远程文件操作抽象层已初始化")
            self.debug_log("V45远程文件操作抽象层已初始化", "REMOTE_FILE_OPERATOR", "INFO")
        except ImportError as e:
            print(f"⚠️ V45远程文件操作抽象层导入失败: {e}")
            self.debug_log(f"V45远程文件操作抽象层导入失败: {e}", "REMOTE_FILE_OPERATOR", "WARNING")

        print("✅ SimplifiedMCPServer初始化完成（含P0安全修复）")
        self.debug_log("SimplifiedMCPServer初始化完成（含P0安全修复）", "SYSTEM", "INFO")

    def _get_restart_flag_file_path(self) -> str:
        """获取重启标志文件路径 - V45容器化改造：配置驱动"""
        try:
            # 通过配置管理系统获取路径
            from unified_config_manager import UnifiedConfigManager

            # 尝试从runtime_config获取
            restart_flag_file = UnifiedConfigManager.get_config("temporary.restart_flag_file")
            if restart_flag_file:
                # 确保目录存在
                restart_flag_dir = UnifiedConfigManager.get_config("temporary.restart_flag_directory", "data/runtime")
                os.makedirs(restart_flag_dir, exist_ok=True)
                return restart_flag_file

        except Exception as e:
            print(f"⚠️ 无法从配置获取重启标志文件路径: {e}")

        # 回退到相对路径（避免硬编码绝对路径）
        fallback_dir = "data/runtime"
        os.makedirs(fallback_dir, exist_ok=True)
        return os.path.join(fallback_dir, ".restart_requested")

    def debug_log(self, message: str, source: str = "SYSTEM", level: str = "INFO"):
        """专业级调试日志系统 - 用于关键调试信息和难点分析

        Args:
            message: 调试消息内容
            source: 消息来源（如WEBSOCKET、STATE_MGMT、API等）
            level: 日志级别（DEBUG、INFO、WARNING、ERROR、CRITICAL）

        使用场景：
        - 连接/断开事件
        - 关键错误和异常
        - 状态变更和重要操作
        - 调试难点和问题定位

        存储：内存中存储，重启后自动清空
        """

        # 创建日志条目
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "level": level,
            "source": source,
            "message": str(message)
        }

        # 存储到内存中
        self.debug_logs.append(log_entry)

        # 保持日志数量限制（避免内存溢出）
        if len(self.debug_logs) > self.max_debug_logs:
            self.debug_logs = self.debug_logs[-self.max_debug_logs:]

        # 控制台输出（带特殊标记）
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[DEBUG_LOG] {timestamp} [{level}] {source}: {message}")

        # SocketIO实时推送到Web界面
        if self.web_app and hasattr(self.web_app, 'socketio'):
            try:
                self.web_app.socketio.emit('debug_log_update', log_entry)
            except Exception as e:
                print(f"[DEBUG_LOG] SocketIO推送失败: {e}")

        # CRITICAL级别强制显示
        if level == "CRITICAL":
            print(f"🚨🚨🚨 [CRITICAL] {source}: {message}")

    def get_debug_logs(self):
        """获取内存中的调试日志"""
        return self.debug_logs



    def _call_commander_service(self, service_name: str, project_name: str = None, *args, **kwargs) -> dict:
        """调用指挥官的服务（透明化远程执行）- V45边界清晰化改进"""
        # 🔧 IDEA式启动：必须指定项目名称，不再使用默认项目
        if not project_name:
            return {"status": "error", "message": "IDEA式启动模式下必须指定项目名称"}

        commander = self.project_manager.get_project_commander(project_name)

        if not commander:
            return {"status": "error", "message": f"项目 {project_name} 的指挥官系统不可用，请先启动该项目"}

        try:
            if hasattr(commander, service_name):
                service_method = getattr(commander, service_name)
                # 指挥官不知道这是本地还是远程执行
                # Web服务器负责处理远程执行的复杂性
                result = service_method(*args, **kwargs)
                return {"status": "success", "result": result, "execution_method": "commander_direct_call", "project": project_name}
            else:
                return {"status": "error", "message": f"指挥官服务不存在: {service_name}"}
        except Exception as e:
            return {"status": "error", "message": f"指挥官服务调用失败: {e}"}

    def _normalize_project_root(self, project_root):
        """标准化项目路径，确保同一物理项目只有一个连接"""
        import os

        # 如果已经是项目名（不包含路径分隔符），直接返回
        if os.sep not in project_root and '/' not in project_root:
            return project_root

        # 如果是绝对路径，提取项目名
        if os.path.isabs(project_root):
            return os.path.basename(project_root)

        # 如果是相对路径，也提取最后一部分作为项目名
        return os.path.basename(project_root.rstrip(os.sep + '/'))

    async def handle_client_connection(self, websocket, path=None):
        """简化的客户端连接处理 - 基于V4.5简化容错机制设计"""
        client_id = None
        project_root = None

        try:
            # 等待客户端身份信息
            first_message = await websocket.recv()
            client_info = json.loads(first_message)

            project_root = client_info['project_root']
            client_id = client_info['client_id']
            # 🔧 V45修复：检查是否是ace mcp替换调用
            is_ace_mcp_replace = client_info.get('ace_mcp_replace', False)

            # 🔧 修复：标准化项目路径，确保同一物理项目只有一个连接
            normalized_project_root = self._normalize_project_root(project_root)

            print(f"🔍 客户端连接请求: {client_id}, 项目: {project_root}, ace_mcp: {is_ace_mcp_replace}")
            self.debug_log(f"🔍 客户端连接请求: {client_id}, 项目: {project_root}, ace_mcp: {is_ace_mcp_replace}", "WEBSOCKET", "INFO")

            # 🔧 修复：显示标准化后的项目路径
            if normalized_project_root != project_root:
                print(f"🔄 项目路径标准化: {project_root} → {normalized_project_root}")
                self.debug_log(f"🔄 项目路径标准化: {project_root} → {normalized_project_root}", "WEBSOCKET", "INFO")

            # P0修复：获取项目连接锁，防止并发竞态（使用标准化路径）
            if normalized_project_root not in self.connection_locks:
                self.connection_locks[normalized_project_root] = asyncio.Lock()

            async with self.connection_locks[normalized_project_root]:
                print(f"🔒 获取项目连接锁: {normalized_project_root}")
                self.debug_log(f"🔒 获取项目连接锁: {normalized_project_root}", "WEBSOCKET", "DEBUG")

                # 🔧 V45修复：检查是否已有客户端（使用标准化路径）
                if normalized_project_root in self.project_clients:
                    existing_client = self.project_clients[normalized_project_root]

                    # 🔧 V45核心逻辑：只有ace mcp调用才能替换现有客户端
                    if is_ace_mcp_replace:
                        print(f"👤 ace mcp调用，立即替换客户端: {existing_client} → {client_id}")
                        self.debug_log(f"👤 ace mcp调用，立即替换客户端: {existing_client} → {client_id}", "WEBSOCKET", "INFO")

                        # ✅ V45架构：服务器不管理任务状态，直接断开旧客户端
                        # 旧客户端检测到断线后会自动执行回退操作

                        # 🔧 V45修复：安全断开旧客户端
                        try:
                            await self._disconnect_client(existing_client)
                        except Exception as e:
                            print(f"⚠️ 断开旧客户端时出现异常: {e}")
                            self.debug_log(f"⚠️ 断开旧客户端时出现异常: {e}", "WEBSOCKET", "WARNING")

                        self.project_clients[normalized_project_root] = client_id
                    else:
                        # 🔧 V45核心逻辑：拒绝普通客户端连接，保持第一个连接
                        print(f"❌ 拒绝客户端连接: {client_id}，项目 {normalized_project_root} 已有客户端 {existing_client}")
                        self.debug_log(f"❌ 拒绝客户端连接: {client_id}，项目 {normalized_project_root} 已有客户端 {existing_client}", "WEBSOCKET", "INFO")

                        # 发送拒绝消息给客户端
                        rejection_message = {
                            "type": "connection_rejected",
                            "reason": "project_already_connected",
                            "existing_client": existing_client,
                            "message": f"项目 {normalized_project_root} 已有客户端连接，请使用 'ace mcp' 命令替换"
                        }
                        await websocket.send(json.dumps(rejection_message))
                        await websocket.close()
                        return  # 直接返回，不进入消息循环
                else:
                    # 接受新客户端（项目没有现有连接）
                    self.project_clients[normalized_project_root] = client_id
                    print(f"✅ 接受客户端连接: {client_id}")
                    self.debug_log(f"✅ 接受客户端连接: {client_id}", "WEBSOCKET", "INFO")

                # 注册连接
                self.client_connections[client_id] = websocket

                # ✅ V45架构：不需要重新分派任务，使用同步等待机制

                print(f"🔓 释放项目连接锁: {normalized_project_root}")
                self.debug_log(f"🔓 释放项目连接锁: {normalized_project_root}", "WEBSOCKET", "DEBUG")

            # 🔍 添加详细的状态诊断输出
            print(f"✅ 客户端连接成功: {client_id}")
            print(f"🔍 [状态诊断] project_clients: {dict(self.project_clients)}")
            print(f"🔍 [状态诊断] client_connections keys: {list(self.client_connections.keys())}")
            print(f"🔍 [状态诊断] 总连接数: {len(self.project_clients)}")

            self.debug_log(f"✅ 客户端连接成功: {client_id}", "WEBSOCKET", "INFO")
            self.debug_log(f"🔍 [状态诊断] project_clients: {dict(self.project_clients)}", "WEBSOCKET", "DEBUG")
            self.debug_log(f"🔍 [状态诊断] client_connections keys: {list(self.client_connections.keys())}", "WEBSOCKET", "DEBUG")

            # V4.5修复：发送连接确认，包含状态恢复信息
            confirmation = {
                "type": "connection_confirmed",
                "client_id": client_id,
                "project_root": project_root,
                "state_restored": True,  # 标记状态已恢复
                "pending_tasks": 0  # V45架构：不再维护任务队列状态
            }
            await websocket.send(json.dumps(confirmation))

            # ✅ V45架构：状态外置，不需要持久化状态

            # ✅ V45架构：不需要消息循环，连接保持到断开为止
            # 同步等待机制：只在发送任务时等待响应，不需要持续监听
            try:
                await websocket.wait_closed()
            except Exception as e:
                print(f"🔌 等待连接关闭时出错: {e}")
                self.debug_log(f"等待连接关闭时出错: {e}", "WEBSOCKET", "DEBUG")

        except Exception as e:
            print(f"❌ 客户端连接处理失败: {client_id if client_id else 'unknown'} - {e}")
            self.debug_log(f"❌ 客户端连接处理失败: {client_id if client_id else 'unknown'} - {e}", "WEBSOCKET", "ERROR")
        finally:

            # 清理连接
            if client_id:
                await self._cleanup_client_connection(client_id)

    # ✅ V45架构：移除消息循环，使用同步等待机制



    # ✅ V45架构：移除心跳机制，通过任务执行检测连接状态

    async def _cleanup_client_connection(self, client_id):
        """清理客户端连接 - 架构修复：确保完整清理"""
        print(f"🧹 清理客户端连接: {client_id}")
        self.debug_log(f"🧹 清理客户端连接: {client_id}", "WEBSOCKET", "INFO")

        # 🔧 架构修复：如果连接还存在，先优雅关闭
        if client_id in self.client_connections:
            websocket = self.client_connections[client_id]
            try:
                if not websocket.closed:
                    await websocket.close()
                    await websocket.wait_closed()
            except Exception as e:
                print(f"⚠️ 清理时WebSocket关闭出错: {client_id} - {e}")
            finally:
                del self.client_connections[client_id]

        # ✅ V45架构：客户端断开时不需要重置任务状态
        # 客户端检测到断线后会自动执行回退操作

        # 清理项目映射
        for project_root, mapped_client in list(self.project_clients.items()):
            if mapped_client == client_id:
                print(f"🔄 [调试] 从project_clients删除: {project_root} -> {client_id}")
                self.debug_log(f"🔄 [调试] 从project_clients删除: {project_root} -> {client_id}", "WEBSOCKET", "DEBUG")
                del self.project_clients[project_root]
                print(f"🔄 项目 {project_root} 客户端已断开，等待重连")
                self.debug_log(f"🔄 项目 {project_root} 客户端已断开，等待重连", "WEBSOCKET", "INFO")
                print(f"🔍 [调试] 删除后project_clients: {dict(self.project_clients)}")
                self.debug_log(f"🔍 [调试] 删除后project_clients: {dict(self.project_clients)}", "WEBSOCKET", "DEBUG")
                break

    def _cleanup_disconnected_client(self, client_id: str):
        """完全清理断开的客户端 - V4.5修复多客户端连接问题"""
        print(f"🧹 清理断开的客户端: {client_id}")
        self.debug_log(f"🧹 开始清理客户端: {client_id}", "WEBSOCKET", "INFO")

        # 🔧 修复：正确清理客户端连接状态
        # 从client_connections中删除
        if client_id in self.client_connections:
            del self.client_connections[client_id]
            print(f"   ✅ 从client_connections中删除: {client_id}")
            self.debug_log(f"   ✅ 从client_connections中删除: {client_id}", "WEBSOCKET", "DEBUG")

        # 从project_clients中删除（需要查找对应的项目路径）
        project_to_remove = None
        for project_root, mapped_client_id in self.project_clients.items():
            if mapped_client_id == client_id:
                project_to_remove = project_root
                break

        if project_to_remove:
            del self.project_clients[project_to_remove]
            print(f"   ✅ 从project_clients中删除项目: {project_to_remove}")
            self.debug_log(f"   ✅ 从project_clients中删除项目: {project_to_remove}", "WEBSOCKET", "DEBUG")

        print(f"🧹 客户端清理完成: {client_id}")
        self.debug_log(f"🧹 客户端清理完成: {client_id} - 剩余连接数: {len(self.project_clients)}", "WEBSOCKET", "INFO")

    # 🔧 历史遗留方法已删除：_load_task_queue()
    # 现在使用 state_manager.load_persistent_state() 替代

    def _save_tokens(self):
        """
        V45架构：状态外置，不保存状态
        """
        # ✅ V45架构：状态外置，不维护任何状态
        pass



    # ✅ V45架构：删除旧的_send_task_to_client方法
    # 已被新的send_task_to_client同步等待版本替代

    def _get_active_tasks(self, client_id):
        """
        V45架构：简化活跃任务获取
        删除复杂的任务状态管理
        """
        # ✅ V45简化：不再维护任务状态，返回空列表
        return []

    async def _disconnect_client(self, client_id):
        """断开客户端连接 - 架构修复：确保WebSocket正确关闭"""
        if client_id in self.client_connections:
            websocket = self.client_connections[client_id]
            try:
                # 🔧 架构修复：优雅关闭WebSocket连接
                if not websocket.closed:
                    await websocket.close()
                    # 等待关闭完成，确保发送关闭帧
                    await websocket.wait_closed()
                print(f"✅ WebSocket连接已优雅关闭: {client_id}")
            except Exception as e:
                print(f"⚠️ WebSocket关闭时出错: {client_id} - {e}")
                # 即使关闭失败也要清理引用
            finally:
                del self.client_connections[client_id]

        # 清理项目映射
        for project_root, mapped_client in list(self.project_clients.items()):
            if mapped_client == client_id:
                del self.project_clients[project_root]
                print(f"🔄 项目 {os.path.basename(project_root)} 客户端已断开，等待重连")
                self.debug_log(f"🔄 项目 {os.path.basename(project_root)} 客户端已断开，等待重连", "WEBSOCKET", "INFO")
                break

    def cleanup_all_disconnected_clients(self):
        """简化的启动清理 - 基于V4.5简化容错机制设计"""
        print("🧹 启动时清理连接状态...")

        # 简化的清理：清空所有连接状态，让客户端重新连接
        self.project_clients.clear()
        self.client_connections.clear()

        print("🧹 连接状态已清理，等待客户端重新连接")

    async def process_client_message(self, client_id: str, message: str):
        """处理MCP客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type")

            if message_type == "task_result":
                # ✅ V45架构：不需要处理task_result消息
                # 同步等待机制已在send_task_to_client中处理
                print(f"📥 收到task_result消息（V45架构中已通过同步等待处理）")
            else:
                print(f"📥 收到MCP客户端消息: {client_id} -> {message_type}")

        except Exception as e:
            print(f"❌ 处理MCP客户端消息失败: {e}")
            self.debug_log(f"处理MCP客户端消息失败: {e}", "WEBSOCKET", "ERROR")

    async def send_task_to_client(self, client_id: str, task_type: str, command: dict, metadata: dict = None):
        """
        向MCP客户端发送任务指令 - V45事务边界架构同步等待版本

        按照设计文档第315-337行要求：
        1. 同步等待客户端响应
        2. 根据客户端执行结果决定成功/失败
        3. 失败时抛出OperationFailedException
        4. 简化任务命令，无复杂ID生成
        5. 确保单线程执行（文件操作串行化）
        """
        # 导入异常类
        try:
            from four_layer_meeting_server.exceptions import OperationFailedException
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            from exceptions import OperationFailedException

        if client_id not in self.client_connections:
            raise OperationFailedException(f"MCP客户端 {client_id} 未连接",
                                         operation_type=task_type,
                                         client_id=client_id)

        # ✅ V45架构：简化任务命令，无复杂ID生成（设计文档第409行、第533行要求）
        # 🔧 关键修复：避免同一秒内重复ID，使用计数器确保唯一性
        if not hasattr(self, '_task_counter'):
            self._task_counter = 0
        self._task_counter += 1
        task_id = f"task_{int(time.time())}_{self._task_counter}"
        task_command = {
            "type": "task_command",
            "task_id": task_id,
            "task_type": task_type,
            "command": command,
            "timestamp": datetime.now().isoformat()
        }

        try:
            websocket = self.client_connections[client_id]

            # 1. 发送任务命令
            await websocket.send(json.dumps(task_command))
            print(f"📤 任务已发送: {task_type} → {client_id}")
            self.debug_log(f"任务已发送: {task_type} → {client_id}", "TASK_MGMT", "INFO")

            # 2. ✅ V45架构：同步等待客户端响应（设计文档第424行要求）
            print(f"🔍 开始同步等待响应: {task_id}")
            self.debug_log(f"开始同步等待响应: {task_id}", "TASK_MGMT", "DEBUG")
            response = await asyncio.wait_for(websocket.recv(), timeout=30.0)
            result = json.loads(response)
            print(f"🔍 同步响应接收成功: {task_id}")
            self.debug_log(f"同步响应接收成功: {task_id}", "TASK_MGMT", "DEBUG")

            # 3. 🔧 关键：根据客户端执行结果决定成功/失败
            if result.get("status") == "error":
                # 服务器报告操作失败（客户端已自动回退）
                error_info = result.get("result", {})
                error_msg = error_info.get("error_message", "未知错误")
                print(f"❌ 客户端执行失败: {task_type} - {error_msg}")
                self.debug_log(f"客户端执行失败: {task_type} - {error_msg}", "TASK_MGMT", "ERROR")
                raise OperationFailedException(f"操作失败: {error_msg}",
                                             operation_type=task_type,
                                             client_id=client_id)

            print(f"✅ 任务执行成功: {task_type}")
            self.debug_log(f"任务执行成功: {task_type}", "TASK_MGMT", "INFO")

            # 🔥 P0修复：正确提取工具输出，避免返回undefined
            # 调试：打印完整响应结构
            print(f"🔍 [DEBUG] 完整响应结构: {result}")
            self.debug_log(f"完整响应结构: {result}", "TASK_MGMT", "DEBUG")

            # ✅ V45架构：服务器作为抽象层，返回标准的Commander格式
            # 客户端响应格式：{"result": {"status": "success", "result": {...}, "operation": "..."}}
            result_data = result.get("result", {})

            print(f"🔍 [DEBUG] result_data: {result_data}")

            # 🔧 抽象层职责：返回RemoteXXX类期望的标准格式 {"status": "success", "result": {...}}
            # 这样RemoteDirectory/RemoteFile的转换逻辑可以正常工作
            if isinstance(result_data, dict) and "status" in result_data:
                # 客户端返回的已经是标准格式，直接返回
                print(f"🔍 [DEBUG] 返回标准Commander格式: {result_data}")
                return result_data
            else:
                # 兜底：构造标准格式
                standard_format = {
                    "status": "success",
                    "result": result_data,
                    "operation": result_data.get("operation", "unknown")
                }
                print(f"🔍 [DEBUG] 构造标准格式: {standard_format}")
                return standard_format

        except asyncio.TimeoutError:
            # 超时视为操作失败
            print(f"⏰ 客户端响应超时: {task_type}")
            self.debug_log(f"客户端响应超时: {task_type}", "TASK_MGMT", "ERROR")
            raise OperationFailedException(f"客户端 {client_id} 响应超时",
                                         operation_type=task_type,
                                         client_id=client_id)
        except Exception as e:
            # 其他异常也视为操作失败
            print(f"❌ 任务执行异常: {task_type} - {e}")
            self.debug_log(f"任务执行异常: {task_type} - {e}", "TASK_MGMT", "ERROR")
            raise OperationFailedException(f"操作失败: {str(e)}",
                                         operation_type=task_type,
                                         client_id=client_id,
                                         original_exception=e)

    # ✅ V45架构：删除handle_task_completion和_handle_task_result方法
    # V45使用同步等待机制，不需要事件处理

    # ✅ V45架构：删除不再需要的异步任务处理方法
    # 使用同步等待机制，不需要 _send_confirmation, _auto_release_suspend_for_debug, _notify_web_interface

    def start_web_interface(self, host: str = "localhost", port: int = 25526) -> dict:
        """启动Web界面（扩展版）- 独立端口避免与WebSocket冲突"""
        if not self.web_app:
            return {"status": "error", "message": "Web界面系统不可用"}

        try:
            # 在后台启动扩展的Web界面
            def run_web():
                self.web_app.run(host=host, port=port, debug=False)

            # 🔧 修复：保存Web线程引用，用于关闭
            self.web_thread = threading.Thread(target=run_web, daemon=True)
            self.web_thread.start()

            # 添加HTTP API路由到Web应用
            self._add_http_api_routes()

            # 添加MCP客户端任务发送API
            self._add_mcp_task_api_routes()

            self.debug_log(f"Web界面已启动，监听端口: {port}", "WEB_SERVER", "INFO")

            return {
                "status": "success",
                "message": "四重会议Web界面已启动",
                "web_url": f"http://{host}:{port}",
                "debug_url": f"http://{host}:{port}/debug",
                "modules_url": f"http://{host}:{port}/modules",
                "nine_grid_url": f"http://{host}:{port}/nine-grid",
                "api_endpoints": [
                    f"http://{host}:{port}/api/execute_task",
                    f"http://{host}:{port}/api/execute_auto",
                    f"http://{host}:{port}/api/validate_continue",
                    f"http://{host}:{port}/api/start_web",
                    f"http://{host}:{port}/api/execute_meeting",
                    f"http://{host}:{port}/api/system_status",
                    f"http://{host}:{port}/api/send_mcp_task",
                    f"http://{host}:{port}/api/mcp_clients"
                ],
                "features": [
                    "九宫格主界面",
                    "Meeting管理和执行",
                    "指挥官控制台",
                    "系统状态监控",
                    "调试中心",
                    "模块管理",
                    "完整HTTP API接口",
                    "MCP客户端任务发送"
                ],
                "note": "Web界面集成了所有指挥官功能，可通过浏览器完整操作，支持向挂起的MCP客户端发送任务"
            }

        except Exception as e:
            return {"status": "error", "message": f"Web界面启动失败: {e}"}

    def _add_http_api_routes(self):
        """添加HTTP API路由到Web应用"""
        if not self.web_app or not hasattr(self.web_app, 'app'):
            return

        from flask import request, jsonify

        @self.web_app.app.route('/api/execute_task', methods=['POST'])
        def api_execute_task():
            """执行修改任务（手动批处理模式）"""
            try:
                data = request.json
                # 调用原有MCP工具函数
                from four_layer_meeting_system.mcp_server.simple_ascii_launcher import execute_checkresult_v4_modification_task
                result = execute_checkresult_v4_modification_task(**data)
                print(f"📋 API执行任务结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API执行任务错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/execute_auto', methods=['POST'])
        def api_execute_auto():
            """执行自动修改任务"""
            try:
                data = request.json
                from four_layer_meeting_system.mcp_server.simple_ascii_launcher import execute_checkresult_v4_modification_task_auto
                result = execute_checkresult_v4_modification_task_auto(**data)
                print(f"🤖 API自动执行结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API自动执行错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/validate_continue', methods=['POST'])
        def api_validate_continue():
            """验证并继续修改"""
            try:
                data = request.json
                from four_layer_meeting_system.mcp_server.simple_ascii_launcher import validate_and_continue_modifications
                result = validate_and_continue_modifications(**data)
                print(f"✅ API验证继续结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API验证继续错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/start_web', methods=['POST'])
        def api_start_web():
            """启动Web界面（递归调用处理）"""
            try:
                data = request.json
                # 这个API在服务端环境下返回当前Web界面信息
                result = {
                    "status": "success",
                    "message": "Web界面已在运行",
                    "web_url": "http://localhost:25526",
                    "note": "当前请求就是通过Web界面发起的"
                }
                print(f"🌐 API启动Web结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API启动Web错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/execute_meeting', methods=['POST'])
        def api_execute_meeting():
            """执行四重会议"""
            try:
                data = request.json
                from four_layer_meeting_system.mcp_server.simple_ascii_launcher import execute_four_layer_meeting
                result = execute_four_layer_meeting(**data)
                print(f"🎉 API执行会议结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API执行会议错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/system_status', methods=['GET'])
        def api_system_status():
            """获取系统状态 - IDEA式启动模式"""
            try:
                # 🔧 IDEA式启动：获取服务器级别状态，不依赖默认项目
                server_status = {}
                if self.project_status_manager:
                    server_status = self.project_status_manager.get_server_status()

                result = {
                    "status": "success",
                    "web_server_info": {
                        "connected_mcp_clients": len(self.project_clients),
                        "task_queue_size": 0,  # V45架构：不再维护任务队列
                        "web_interface_status": "active" if self.web_app else "inactive"
                    },
                    "server_status": server_status,
                    "active_projects": list(self.project_manager.project_commanders.keys()) if hasattr(self.project_manager, 'project_commanders') else [],
                    "execution_method": "idea_startup_mode",
                    "timestamp": datetime.now().isoformat()
                }
                print(f"📊 API系统状态结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ API系统状态错误: {error_result}")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/restart_server', methods=['POST'])
        def api_restart_server():
            """安全重启服务器API - 调试专用，无需确认"""
            try:
                print("🚨 收到重启请求，开始安全重启流程...")
                self.debug_log("收到重启请求，开始安全重启流程", "RESTART", "CRITICAL")
                result = self.safe_shutdown_and_restart()
                print(f"🔄 重启结果: {result}")
                return jsonify(result)
            except Exception as e:
                error_result = {"status": "error", "message": str(e)}
                print(f"❌ 重启错误: {error_result}")
                self.debug_log(f"重启流程失败: {e}", "RESTART", "CRITICAL")
                return jsonify(error_result), 500

        @self.web_app.app.route('/api/test_v45_abstraction', methods=['POST'])
        def api_test_v45_abstraction():
            """一键测试V45抽象层 - 三重验证所有27个API"""
            try:
                print("🧪 收到V45抽象层测试请求...")
                self.debug_log("开始V45抽象层一键测试", "V45_TEST", "INFO")

                data = request.json or {}
                client_id = data.get('client_id')  # 可选指定客户端ID

                # 导入测试验证器
                try:
                    from four_layer_meeting_system.remote_file_operator.v45_test_validator import run_v45_test
                except ImportError as e:
                    return jsonify({
                        "status": "error",
                        "message": f"V45测试验证器导入失败: {e}"
                    }), 500

                # ✅ V45架构：线程安全的事件循环通信
                import asyncio
                if hasattr(self, '_main_event_loop') and self._main_event_loop:
                    # 使用主事件循环执行测试（线程安全）
                    future = asyncio.run_coroutine_threadsafe(
                        run_v45_test(client_id),
                        self._main_event_loop
                    )
                    result = future.result()  # 等待测试完成
                    print(f"✅ [V45架构] V45测试使用主事件循环执行")
                else:
                    # 回退到新事件循环（会有事件循环冲突）
                    result = asyncio.run(run_v45_test(client_id))
                    print(f"⚠️ [V45警告] V45测试使用新事件循环，可能有冲突")

                print(f"✅ V45抽象层测试完成: {result.get('test_summary', {}).get('success_rate', 0):.1f}%")
                self.debug_log(f"V45抽象层测试完成，成功率: {result.get('test_summary', {}).get('success_rate', 0):.1f}%", "V45_TEST", "INFO")

                return jsonify(result)

            except Exception as e:
                error_result = {"status": "error", "message": f"V45抽象层测试失败: {str(e)}"}
                print(f"❌ V45抽象层测试错误: {error_result}")
                self.debug_log(f"V45抽象层测试错误: {str(e)}", "V45_TEST", "ERROR")
                return jsonify(error_result), 500

        print("✅ HTTP API路由已添加")

    def _add_mcp_task_api_routes(self):
        """为Web界面添加任务下发API - 按照V4.5设计文档要求"""
        if not self.web_app or not hasattr(self.web_app, 'app'):
            return

        from flask import request, jsonify
        import asyncio

        @self.web_app.app.route('/api/send_task', methods=['POST'])
        def api_send_task():
            """通用任务下发接口 - V45事务边界架构异常处理版本"""
            try:
                data = request.json
                client_id = data.get('client_id')
                task_type = data.get('task_type', 'mcp_tool_execution')
                command = data.get('command', {})
                metadata = data.get('metadata', {})

                # 🔧 修复：如果没有指定client_id，自动选择第一个可用的客户端
                if not client_id:
                    if self.project_clients:
                        client_id = list(self.project_clients.values())[0]  # 获取第一个客户端ID
                        print(f"🔧 [自动选择] 未指定客户端，自动选择: {client_id}")
                    else:
                        return jsonify({
                            "status": "error",
                            "message": "没有可用的MCP客户端连接"
                        }), 400

                # ✅ V45架构：线程安全的事件循环通信
                if hasattr(self, '_main_event_loop') and self._main_event_loop:
                    # 使用主事件循环执行任务（线程安全）
                    future = asyncio.run_coroutine_threadsafe(
                        self.send_task_to_client(client_id, task_type, command, metadata),
                        self._main_event_loop
                    )
                    result = future.result()  # V45架构：同步等待结果
                    print(f"✅ [V45架构] 任务使用主事件循环执行")
                else:
                    # 回退到新事件循环（会有事件循环冲突）
                    result = asyncio.run(self.send_task_to_client(client_id, task_type, command, metadata))
                    print(f"⚠️ [V45警告] 任务使用新事件循环，可能有冲突")

                print(f"🌐 Web界面请求任务: {task_type}")
                print(f"📊 任务结果: {result}")
                return jsonify({
                    "status": "success",
                    "result": result,
                    "task_id": result  # 🔥 P0修复：JavaScript期望task_id字段
                })

            except Exception as e:
                # ✅ V45架构要求：API异常统一返回JSON格式
                error_message = str(e)
                print(f"❌ API任务执行失败: {task_type} - {error_message}")
                self.debug_log(f"API任务执行失败: {task_type} - {error_message}", "MCP_API", "ERROR")
                return jsonify({
                    "status": "error",
                    "message": error_message,
                    "task_type": task_type,
                    "client_id": client_id if 'client_id' in locals() else None
                }), 500

        @self.web_app.app.route('/api/task_status/<task_id>', methods=['GET'])
        def api_task_status(task_id):
            """查询任务执行状态 - V45架构：使用同步等待，不维护任务状态"""
            # V45架构：不再维护任务队列状态
            return jsonify({
                "status": "success",
                "message": "V45架构使用同步等待机制，任务立即执行完成",
                "task_info": {
                    "task_id": task_id,
                    "status": "completed_immediately",
                    "architecture": "V45_sync_wait"
                }
            })

        @self.web_app.app.route('/api/connected_clients', methods=['GET'])
        def api_connected_clients():
            """获取已连接的MCP客户端列表"""
            clients = []
            for project_root, client_id in self.project_clients.items():
                if client_id in self.client_connections:
                    clients.append({
                        "client_id": client_id,
                        "project_root": project_root,
                        "status": "connected"
                    })

            return jsonify({
                "status": "success",
                "connected_clients": clients,
                "total_count": len(clients)
            })

        # 便捷的MCP工具执行接口
        @self.web_app.app.route('/api/execute_mcp_tool', methods=['POST'])
        def api_execute_mcp_tool():
            """便捷的MCP工具执行接口"""
            data = request.json
            tool_name = data.get('tool_name')
            arguments = data.get('arguments', {})
            client_id = data.get('client_id')

            # 🔧 修复：如果没有指定client_id，自动选择第一个可用的客户端
            if not client_id:
                if self.project_clients:
                    client_id = list(self.project_clients.keys())[0]
                    print(f"🔧 [自动选择] 未指定客户端，自动选择: {client_id}")
                else:
                    return jsonify({
                        "status": "error",
                        "message": "没有可用的MCP客户端连接"
                    }), 400

            command = {
                "tool_name": tool_name,
                "arguments": arguments
            }

            # ✅ V45架构：线程安全的事件循环通信
            if hasattr(self, '_main_event_loop') and self._main_event_loop:
                # 使用主事件循环执行MCP工具（线程安全）
                future = asyncio.run_coroutine_threadsafe(
                    self.send_task_to_client(
                        client_id,
                        "mcp_tool_execution",
                        command,
                        {"priority": "normal", "timeout": 300}
                    ),
                    self._main_event_loop
                )
                result = future.result()  # V45架构：同步等待结果
            else:
                # 回退到新事件循环（会有事件循环冲突）
                result = asyncio.run(self.send_task_to_client(
                    client_id,
                    "mcp_tool_execution",
                    command,
                    {"priority": "normal", "timeout": 300}
                ))

            return jsonify(result)

        # V4.5 状态管理API
        @self.web_app.app.route('/api/client_states', methods=['GET'])
        def api_client_states():
            """获取客户端状态信息"""
            # 真正的服务器端debug_log调用
            self.debug_log("📊 查询客户端状态信息", "STATE_MGMT", "INFO")

            try:
                # 🔍 添加详细的状态诊断输出
                print(f"🔍 [状态诊断] 查询时 project_clients: {dict(self.project_clients)}")
                print(f"🔍 [状态诊断] 查询时 client_connections keys: {list(self.client_connections.keys())}")

                self.debug_log(f"🔍 [状态诊断] 查询时 project_clients: {dict(self.project_clients)}", "STATE_MGMT", "DEBUG")
                self.debug_log(f"🔍 [状态诊断] 查询时 client_connections keys: {list(self.client_connections.keys())}", "STATE_MGMT", "DEBUG")

                # 简化的状态查询 - 基于新的状态管理
                states = {}
                for project_root, client_id in self.project_clients.items():
                    print(f"🔍 [状态诊断] 检查客户端: {client_id}, 项目: {project_root}")
                    print(f"🔍 [状态诊断] 客户端在连接列表中: {client_id in self.client_connections}")

                    if client_id in self.client_connections:
                        states[client_id] = {
                            "status": "connected",
                            "project_root": project_root,
                            "connection_active": True
                        }
                        print(f"🔍 [状态诊断] 添加到状态列表: {client_id}")
                    else:
                        print(f"🔍 [状态诊断] 客户端不在连接列表中，跳过: {client_id}")

                # 真正的服务器端成功日志
                self.debug_log(f"✅ 客户端状态查询成功，共{len(states)}个客户端", "STATE_MGMT", "SUCCESS")

                # 详细的客户端信息
                for client_id, state in states.items():
                    self.debug_log(f"📱 {client_id}: {state['status']} (项目: {state['project_root']})", "STATE_MGMT", "INFO")

                return jsonify({
                    "status": "success",
                    "client_states": states,
                    "total_clients": len(states)
                })
            except Exception as e:
                self.debug_log(f"❌ 客户端状态查询失败: {e}", "STATE_MGMT", "ERROR")
                return jsonify({"status": "error", "message": str(e)}), 500

        @self.web_app.app.route('/api/debug_logs', methods=['GET'])
        def api_debug_logs():
            """获取内存中的调试日志"""
            try:
                return jsonify({
                    "status": "success",
                    "debug_logs": self.debug_logs,
                    "total_logs": len(self.debug_logs)
                })
            except Exception as e:
                return jsonify({"status": "error", "message": str(e)}), 500

        @self.web_app.app.route('/api/reconnection_history', methods=['GET'])
        def api_reconnection_history():
            """获取重连历史记录"""
            try:
                history = {}
                for client_id, record in self.reconnection_history.items():
                    history[client_id] = {
                        "first_connected": record["first_connected"].isoformat(),
                        "reconnection_count": record["count"],
                        "last_reconnect": record["last_reconnect"].isoformat()
                    }

                return jsonify({
                    "status": "success",
                    "reconnection_history": history,
                    "total_records": len(history)
                })
            except Exception as e:
                return jsonify({"status": "error", "message": str(e)}), 500

        print("✅ MCP任务下发API路由已添加")
        print("✅ V4.5 状态管理API路由已添加")

    def safe_shutdown_and_restart(self):
        """安全关闭并重启服务器 - 100%可靠的生产级重启"""
        print("🚨 开始安全关闭和重启流程...")

        try:
            # 第一步：数据持久化和资源清理（恢复正常架构）
            self._perform_safe_resource_cleanup()

            # 第二步：启动独立重启器
            self._launch_restart_process()

            # 第三步：标记重启请求，让主循环处理退出
            self._restart_requested = True

            # 🔧 修复：创建重启标志文件，确保即使server对象为None也能检测到重启
            # 🚀 V45容器化改造：通过配置管理系统获取重启标志文件路径
            restart_flag_file = self._get_restart_flag_file_path()
            try:
                os.makedirs(os.path.dirname(restart_flag_file), exist_ok=True)
                with open(restart_flag_file, 'w') as f:
                    f.write(f"restart_requested_at_{datetime.now().isoformat()}")
                print(f"✅ 重启标志文件已创建: {restart_flag_file}")
            except Exception as e:
                print(f"⚠️ 创建重启标志文件失败: {e}")

            print("✅ 安全重启流程已启动")
            return {"status": "success", "message": "服务器正在安全重启..."}

        except Exception as e:
            print(f"❌ 安全重启失败: {e}")
            return {"status": "error", "message": f"重启失败: {e}"}

    def _perform_safe_resource_cleanup(self):
        """执行安全资源清理 - 恢复正常架构，但修正WebSocket处理"""
        print("🧹 开始资源清理...")

        try:
            # 🔧 修复：第一步 - 停止Web服务器（按正确关闭顺序）
            if self.web_thread and self.web_thread.is_alive():
                print("🌐 停止Web服务器...")
                # 注意：daemon线程会随主进程退出，这里只是标记
                # 实际的Web服务器会在进程退出时自动停止
                print("✅ Web服务器停止标记已设置")

            # 🔧 修正：第二步 - 强制清理所有客户端连接状态（不保留）
            print(f"🧹 重启时强制清理客户端连接，当前连接数: {len(self.project_clients)}")
            self.debug_log(f"🧹 重启时强制清理客户端连接，当前连接数: {len(self.project_clients)}", "WEBSOCKET", "INFO")

            # 强制断开所有WebSocket连接
            for client_id, websocket in list(self.client_connections.items()):
                try:
                    print(f"🔌 强制断开客户端: {client_id}")
                    # 不使用await，直接关闭连接
                    if hasattr(websocket, 'close'):
                        websocket.close()
                except Exception as e:
                    print(f"⚠️ 断开客户端失败 {client_id}: {e}")

            # 清理所有客户端状态
            self.client_connections.clear()
            self.project_clients.clear()
            print("✅ 所有客户端连接已强制清理")

            # 🔧 边界清晰化：清理所有项目的指挥官系统资源
            for project_name, commander in self.project_manager.project_commanders.items():
                if commander and hasattr(commander, 'cleanup_phase3_resources'):
                    try:
                        commander.cleanup_phase3_resources()
                        print(f"✅ 项目 {project_name} 指挥官资源已清理")
                    except Exception as e:
                        print(f"⚠️ 项目 {project_name} 指挥官资源清理失败: {e}")

            # 清理全局资源
            try:
                from python_host.common.resource_manager import cleanup_all_resources
                cleanup_all_resources()
            except ImportError:
                pass

            # 清理线程池（恢复老代码逻辑）
            try:
                from python_host.common.thread_pool_manager import get_thread_pool_manager
                get_thread_pool_manager().shutdown_all(wait=True)
            except ImportError:
                pass

            # 清理数据库连接池
            try:
                from python_host.panoramic.sqlite_connection_pool import _connection_pools
                for db_path, pool in _connection_pools.items():
                    pool.close_all()
            except ImportError:
                pass

            print("✅ 资源清理完成")

        except Exception as e:
            print(f"⚠️ 资源清理错误: {e}")
            # 即使清理失败也继续重启流程


    def _launch_restart_process(self):
        """启动独立重启进程"""
        try:
            current_dir = os.path.dirname(__file__)
            restart_script = os.path.join(current_dir, "restart.py")

            if not os.path.exists(restart_script):
                raise FileNotFoundError(f"重启脚本不存在: {restart_script}")

            # Windows 脱钩标志
            DETACHED_PROCESS = 0x00000008

            subprocess.Popen(
                [sys.executable, restart_script],
                cwd=current_dir,
                close_fds=True,
                creationflags=DETACHED_PROCESS if os.name == 'nt' else 0
            )

            print("✅ 重启进程已启动")

        except Exception as e:
            print(f"❌ 启动重启进程失败: {e}")
            raise

    def is_restart_requested(self):
        """检查是否请求重启"""
        return self._restart_requested

async def start_server():
    """启动四重会议Web服务器"""
    if not WEBSOCKETS_AVAILABLE:
        print("❌ websockets模块不可用，无法启动WebSocket服务器")
        print("💡 请安装websockets: pip install websockets")
        return

    server = SimplifiedMCPServer()

    print("🚀 四重会议Web服务器启动中...")
    print("📊 直接调用指挥官: Python指挥官V4.5完整系统")
    server.debug_log("四重会议Web服务器启动中...", "STARTUP", "INFO")

    # V4.5修复：按照设计文档要求，不在启动时清理连接状态
    # 状态将从持久化存储中恢复，客户端重连时自动恢复连接状态

    # 同时启动WebSocket服务器和Web界面
    print("🌐 启动Web界面服务器...")
    web_result = server.start_web_interface(host='localhost', port=25526)
    print(f"✅ Web界面启动结果: {web_result.get('message', 'Unknown')}")

    # 启动WebSocket服务器
    print("🔌 启动WebSocket通信服务器...")
    server.debug_log("WebSocket通信服务器启动中，监听端口: 25527", "WEBSOCKET", "INFO")

    print("✅ 四重会议Web服务器完全启动:")
    print(f"   📡 WebSocket通信: ws://localhost:25527")
    server.debug_log("四重会议Web服务器完全启动", "STARTUP", "INFO")
    print(f"   🌐 Web界面访问: http://localhost:25526")
    print(f"   🔧 调试中心: http://localhost:25526/debug")
    print(f"   📋 九宫格界面: http://localhost:25526/nine-grid")

    # ✅ V45架构：保存主事件循环引用，用于线程安全通信
    server._main_event_loop = asyncio.get_running_loop()
    print(f"✅ [V45架构] 主事件循环已保存: {id(server._main_event_loop)}")
    server.debug_log(f"主事件循环已保存: {id(server._main_event_loop)}", "STARTUP", "DEBUG")

    # 保持服务器运行
    try:
        # 启动WebSocket服务器并等待
        websocket_server = await websockets.serve(
            server.handle_client_connection,
            "localhost",
            25527,
            # 🔧 修复：添加错误处理，减少握手失败的日志噪音
            logger=None  # 禁用websockets库的默认日志
        )
        print("🔄 服务器运行中，按Ctrl+C停止...")
        server.debug_log("WebSocket服务器已启动，等待MCP客户端连接", "WEBSOCKET", "INFO")

        # 运行循环，定期检查重启请求
        while True:
            try:
                # 检查是否请求重启
                if server.is_restart_requested():
                    print("🔄 检测到重启请求，开始优雅退出...")
                    server.debug_log("🔄 检测到重启请求，开始关闭WebSocket服务器", "RESTART", "CRITICAL")
                    server.debug_log(f"🔄 重启时活跃连接数: {len(server.project_clients)}", "RESTART", "INFO")
                    raise asyncio.CancelledError("重启请求")

                # 短暂等待，避免CPU占用过高
                await asyncio.sleep(1)

            except asyncio.CancelledError:
                # 重启请求或其他取消信号
                raise

    except asyncio.CancelledError:
        # 🔧 修复：显式关闭WebSocket服务器，确保资源完全释放
        try:
            if 'websocket_server' in locals() and websocket_server:
                print("🔌 正在关闭WebSocket服务器...")
                websocket_server.close()
                await websocket_server.wait_closed()
                print("✅ WebSocket服务器已完全关闭")
        except Exception as e:
            print(f"⚠️ WebSocket服务器关闭时出错: {e}")
            # 继续执行，不阻塞重启流程

        # 重新抛出CancelledError，让外层main函数处理
        if server.is_restart_requested():
            print("🔄 服务器收到重启信号，准备重启...")
            server.debug_log("🔄 WebSocket服务器已关闭，准备重启", "RESTART", "CRITICAL")
        else:
            print("⚠️ 服务器收到取消信号，准备重启...")
            server.debug_log("⚠️ WebSocket服务器收到取消信号", "RESTART", "WARNING")
        raise
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        raise
    except Exception as e:
        print(f"❌ 服务器错误: {e}")
        raise
    finally:
        # 🔧 修复：确保WebSocket服务器总是被关闭，防止端口占用
        try:
            if 'websocket_server' in locals() and websocket_server:
                print("🔌 [Finally] 强制关闭WebSocket服务器...")
                websocket_server.close()
                await websocket_server.wait_closed()
                print("✅ [Finally] WebSocket服务器已强制关闭")
        except Exception as e:
            print(f"⚠️ [Finally] WebSocket服务器关闭时出错: {e}")

    return server

async def main():
    """主函数 - 屏蔽生命周期管理的中断"""
    print("🚀 启动Web服务器（屏蔽生命周期管理中断）...")

    server = None
    while True:
        try:
            server = await start_server()
        except asyncio.CancelledError:
            # 🔧 关键修复：检查是否存在重启标志文件（独立重启器创建的）
            # 🚀 V45容器化改造：通过配置管理系统获取重启标志文件路径
            restart_flag_file = _get_restart_flag_file_path_static()

            # 🔧 修复：统一重启检查，避免重复逻辑
            is_restart = False
            if server and server.is_restart_requested():
                print("🔄 重启请求确认，服务器退出...")
                is_restart = True
            elif os.path.exists(restart_flag_file):
                print("🔄 检测到重启标志文件，服务器退出...")
                try:
                    os.remove(restart_flag_file)  # 清理标志文件
                except:
                    pass
                is_restart = True

            if is_restart:
                break  # 退出循环，让进程结束
            else:
                # 其他取消请求，继续运行
                print("⚠️ 检测到生命周期管理中断，忽略并继续运行...")
                continue
        except KeyboardInterrupt:
            # 真正手动 Ctrl+C 时才退出
            print("\n🛑 收到手动停止信号，服务器退出")
            break
        except Exception as e:
            print(f"❌ 服务器异常: {e}")
            print("🔄 5秒后重启服务器...")
            await asyncio.sleep(5)
            continue

    # ==================== V45容器化改造方法 ====================
    # 🔧 边界清晰化：项目管理方法统一委托给ProjectManager

    def create_new_project(self, project_name: str, meeting_path: str = None):
        """创建新项目 - 委托给ProjectManager"""
        return self.project_manager.create_project(project_name, meeting_path)

    def get_project_container(self, project_name: str):
        """获取项目容器 - 委托给ProjectManager"""
        return self.project_manager.get_project(project_name)

    def list_projects(self):
        """列出所有项目 - 委托给ProjectManager"""
        return self.project_manager.list_projects()

if __name__ == "__main__":
    asyncio.run(main())
