#!/usr/bin/env node

/**
 * 批量添加权威性元数据工具
 * 为缺少权威性元数据的文件快速添加元数据
 */

const fs = require('fs');
const path = require('path');

// 文件映射规则
const fileMappings = [
    {
        file: 'docs/ai-memory/L2-context/task-types/documentation-tasks.json',
        source: 'docs/common/best-practices/document-management/document-creation-automation.md'
    },
    {
        file: 'docs/ai-memory/L2-context/task-types/feature-document-creation.json',
        source: 'docs/common/templates/feature-readme-template.md'
    },
    {
        file: 'docs/ai-memory/L2-context/task-types/testing-tasks.json',
        source: 'docs/common/best-practices/testing/ai-testing-strategy-guide.md'
    },
    {
        file: 'docs/ai-memory/L2-context/tech-stack/postgresql-stack.json',
        source: 'docs/common/middleware/postgresql/postgresql-index.json'
    },
    {
        file: 'docs/ai-memory/L2-context/project-groups/xkc-core+uid.json',
        source: 'docs/common/architecture/patterns/project-integration-patterns.md'
    },
    {
        file: 'docs/ai-memory/L2-context/automation/document-creation-automation.json',
        source: 'docs/common/best-practices/document-management/document-creation-automation.md'
    }
];

function addAuthorityMetadata(filePath, sourceFile) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠ 文件不存在: ${filePath}`);
            return false;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const jsonContent = JSON.parse(content);

        // 检查是否已有权威性元数据
        if (jsonContent.authority_metadata) {
            console.log(`✓ ${filePath} 已有权威性元数据`);
            return true;
        }

        // 添加权威性元数据
        jsonContent.authority_metadata = {
            source_file: sourceFile,
            last_sync_date: new Date().toISOString(),
            sync_status: "up_to_date",
            authority_validation: "confirmed"
        };

        // 写回文件
        fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2));
        console.log(`✅ 已为 ${filePath} 添加权威性元数据`);
        return true;

    } catch (error) {
        console.error(`❌ 处理 ${filePath} 时出错: ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🚀 开始批量添加权威性元数据...\n');
    
    let successCount = 0;
    let totalCount = fileMappings.length;

    fileMappings.forEach(mapping => {
        if (addAuthorityMetadata(mapping.file, mapping.source)) {
            successCount++;
        }
    });

    console.log('\n📊 处理结果:');
    console.log(`  总文件数: ${totalCount}`);
    console.log(`  成功处理: ${successCount}`);
    console.log(`  失败数量: ${totalCount - successCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 所有文件处理完成！');
    } else {
        console.log('\n⚠ 部分文件处理失败，请检查错误信息');
    }
}

if (require.main === module) {
    main();
}
