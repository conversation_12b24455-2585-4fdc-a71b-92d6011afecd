# 项目关系矩阵

## 概述

本文档定义XKongCloud项目各子项目间的关系矩阵，包括依赖关系、集成模式和通信方式。覆盖XKC-CORE、XKC-UID、XKC-CENTER三个核心项目的完整关系网络，为AI系统提供项目间依赖分析、架构决策支持和集成模式指导。

本文档记录了XKongCloud各子项目之间的关系，帮助理解项目之间的依赖和交互方式。

## 核心项目关系矩阵

| 项目 | xkongcloud-business-internal-core | xkongcloud-commons-uid | xkongcloud-service-center |
|------|-----------------------------------|------------------------|--------------------------|
| **xkongcloud-business-internal-core** | - | 依赖<br>使用UID生成器门面<br>集成持久化ID管理 | 依赖<br>使用KV参数服务<br>读取配置参数 |
| **xkongcloud-commons-uid** | 被依赖<br>提供UID生成功能<br>提供门面API | - | 无直接依赖<br>间接使用KV参数 |
| **xkongcloud-service-center** | 被依赖<br>提供KV参数服务<br>配置管理 | 无直接依赖 | - |

## xkongcloud-business-internal-core与xkongcloud-commons-uid的关系

xkongcloud-business-internal-core项目依赖xkongcloud-commons-uid公共库，使用其提供的分布式唯一ID生成功能。主要交互包括：

1. **门面模式集成**：
   - 使用UidGeneratorFacade门面类简化配置
   - 通过门面API获取UID，不直接使用内部组件

2. **配置方式**：
   - xkongcloud-business-internal-core项目创建UidGeneratorConfig配置类
   - 从KVParamService获取UID生成器所需的配置参数
   - 通过UidGeneratorFacadeBuilder构建UidGeneratorFacade实例

3. **实体类ID生成**：
   - 为JPA实体类提供唯一ID
   - 在保存实体前通过UID生成器获取ID

4. **数据库交互**：
   - xkongcloud-commons-uid使用xkongcloud-business-internal-core提供的数据源
   - 自动管理infra_uid.worker_node表和infra_uid.instance_registry表

## xkongcloud-business-internal-core与xkongcloud-service-center的关系

xkongcloud-business-internal-core项目依赖xkongcloud-service-center，使用其提供的配置管理功能。主要交互包括：

1. **KV参数获取**：
   - 通过KVParamService获取PostgreSQL数据库连接参数
   - 通过KVParamService获取UID生成器配置参数
   - 通过KVParamService获取其他系统配置参数

2. **依赖启动顺序**：
   - xkongcloud-service-center必须先于xkongcloud-business-internal-core启动
   - PostgreSQLConfig和UidGeneratorConfig类使用@DependsOn("kvParamService")确保依赖顺序

3. **配置变更通知**：
   - 支持配置参数热更新
   - 敏感配置参数(如数据库连接)变更需重启应用

4. **集群信息共享**：
   - 通过xkongcloud-service-center获取集群信息
   - 使用集群ID作为应用标识

## 演进架构中的项目关系

在演进架构设计中，各项目关系将逐步调整：

1. **模块化阶段**：
   - 保持当前依赖关系
   - 引入服务接口抽象层和配置驱动架构

2. **混合架构阶段**：
   - 部分服务可能从本地调用切换到远程调用
   - 引入服务发现和负载均衡能力

3. **微服务架构阶段**：
   - 项目间依赖转变为服务间依赖
   - 使用API网关和服务注册中心协调服务

## 注意事项

1. **依赖管理**：严格遵循依赖方向，避免循环依赖
2. **集成模式**：必须使用指定的集成模式（如门面模式），不允许直接访问内部组件
3. **启动顺序**：必须按照指定的启动顺序启动服务，确保依赖服务先于被依赖服务启动
4. **演进兼容性**：架构演进时必须保持向后兼容性，渐进式演进而非激进重构
5. **配置依赖**：配置参数变更可能影响多个项目，需要协调一致的变更管理