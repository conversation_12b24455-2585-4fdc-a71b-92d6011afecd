<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task ID 修复测试</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1e1e1e;
            color: #ffffff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background-color: #2a2a2a;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #106ebe;
        }
        input[type="text"] {
            width: 400px;
            padding: 8px;
            margin: 5px;
            background-color: #333;
            color: white;
            border: 1px solid #555;
            border-radius: 3px;
        }
        .log {
            background-color: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #00ff00; }
        .error { color: #ff0000; }
        .info { color: #00ffff; }
    </style>
</head>
<body>
    <h1>🔧 Task ID 修复测试</h1>
    
    <div class="test-section">
        <h3>1. WebSocket 连接测试</h3>
        <button onclick="connectWebSocket()">连接 WebSocket</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <div id="connection-status">未连接</div>
    </div>

    <div class="test-section">
        <h3>2. 目录设置测试</h3>
        <input type="text" id="directory-path" placeholder="输入目录路径，例如：C:\ExchangeWorks\xkong\project-manager" value="C:\ExchangeWorks\xkong\project-manager">
        <br>
        <button onclick="testDirectorySetup()">测试目录设置</button>
        <div id="directory-result"></div>
    </div>

    <div class="test-section">
        <h3>3. 心跳测试</h3>
        <button onclick="sendHeartbeat()">发送心跳</button>
        <button onclick="startAutoHeartbeat()">开始自动心跳</button>
        <button onclick="stopAutoHeartbeat()">停止自动心跳</button>
        <div id="heartbeat-result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="test-log" class="log"></div>
    </div>

    <script>
        let wsClient = null;
        let heartbeatInterval = null;
        let currentTaskId = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('test-log');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function connectWebSocket() {
            if (wsClient) {
                log('WebSocket 已连接', 'info');
                return;
            }

            log('正在连接 WebSocket...', 'info');
            
            wsClient = io('/ws/pm-v2', {
                auth: {
                    Authorization: 'Bearer test-token'
                },
                transports: ['websocket'],
                upgrade: false
            });

            wsClient.on('connect', () => {
                log('✅ WebSocket 连接成功', 'success');
                document.getElementById('connection-status').textContent = '已连接';
                document.getElementById('connection-status').style.color = '#00ff00';
            });

            wsClient.on('disconnect', () => {
                log('❌ WebSocket 连接断开', 'error');
                document.getElementById('connection-status').textContent = '未连接';
                document.getElementById('connection-status').style.color = '#ff0000';
            });

            wsClient.on('connection_established', (message) => {
                log('🎯 连接建立确认: ' + JSON.stringify(message, null, 2), 'success');
            });

            wsClient.on('workspace_created', (message) => {
                log('🏗️ 工作区创建: ' + JSON.stringify(message, null, 2), 'success');
                if (message.data && message.data.task_id) {
                    currentTaskId = message.data.task_id;
                    log('✅ 任务ID已保存: ' + currentTaskId, 'success');
                }
            });

            wsClient.on('pong', (message) => {
                log('💓 心跳响应: ' + JSON.stringify(message, null, 2), 'info');
                const taskId = message.task_id;
                if (taskId) {
                    log('✅ 心跳响应包含 task_id: ' + taskId, 'success');
                    document.getElementById('heartbeat-result').innerHTML = `<span class="success">✅ task_id: ${taskId}</span>`;
                } else {
                    log('❌ 心跳响应 task_id 为 null', 'error');
                    document.getElementById('heartbeat-result').innerHTML = `<span class="error">❌ task_id: null</span>`;
                }
            });

            wsClient.on('error', (message) => {
                log('❌ WebSocket 错误: ' + JSON.stringify(message, null, 2), 'error');
            });
        }

        function disconnectWebSocket() {
            if (wsClient) {
                wsClient.disconnect();
                wsClient = null;
                log('WebSocket 已断开', 'info');
                document.getElementById('connection-status').textContent = '未连接';
                document.getElementById('connection-status').style.color = '#ff0000';
            }
        }

        async function testDirectorySetup() {
            const path = document.getElementById('directory-path').value.trim();
            if (!path) {
                log('❌ 请输入目录路径', 'error');
                return;
            }

            try {
                log('🚀 开始测试目录设置: ' + path, 'info');
                
                // 步骤1：调用 API 创建 manager
                log('步骤1: 调用 API 创建 manager...', 'info');
                const response = await fetch('/api/pm_v2/get_and_create', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({design_doc_path: path})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ API 调用成功: ' + JSON.stringify(result, null, 2), 'success');
                    document.getElementById('directory-result').innerHTML = `<span class="success">✅ Manager ID: ${result.manager_id}</span>`;
                    
                    // 步骤2：通过 WebSocket 初始化工作区
                    if (wsClient && wsClient.connected) {
                        log('步骤2: 通过 WebSocket 初始化工作区...', 'info');
                        wsClient.emit('initialize_workspace', {
                            project_path: path
                        });
                    } else {
                        log('❌ WebSocket 未连接，无法初始化工作区', 'error');
                    }
                } else {
                    log('❌ API 调用失败: ' + result.error, 'error');
                    document.getElementById('directory-result').innerHTML = `<span class="error">❌ ${result.error}</span>`;
                }
            } catch (error) {
                log('❌ 测试失败: ' + error.message, 'error');
                document.getElementById('directory-result').innerHTML = `<span class="error">❌ ${error.message}</span>`;
            }
        }

        function sendHeartbeat() {
            if (wsClient && wsClient.connected) {
                log('💓 发送心跳 ping...', 'info');
                wsClient.emit('ping');
            } else {
                log('❌ WebSocket 未连接', 'error');
            }
        }

        function startAutoHeartbeat() {
            if (heartbeatInterval) {
                log('自动心跳已在运行', 'info');
                return;
            }
            
            heartbeatInterval = setInterval(() => {
                if (wsClient && wsClient.connected) {
                    sendHeartbeat();
                }
            }, 5000); // 5秒间隔，便于测试
            
            log('🔄 自动心跳已启动 (5秒间隔)', 'success');
        }

        function stopAutoHeartbeat() {
            if (heartbeatInterval) {
                clearInterval(heartbeatInterval);
                heartbeatInterval = null;
                log('⏹️ 自动心跳已停止', 'info');
            }
        }

        // 页面加载时自动连接
        window.addEventListener('load', () => {
            log('页面加载完成，准备测试...', 'info');
        });
    </script>
</body>
</html>
