# -*- coding: utf-8 -*-
"""
状态数据模型 - 为项目经理工作区提供标准的、类型安全的数据结构
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import json
import os

class ProjectState(Enum):
    """项目状态枚举"""
    INITIALIZED = "initialized"
    IDLE = "idle"
    REVIEWING = "reviewing"
    COMPLETED = "completed"
    FAILED = "failed"
    ERROR = "error"

class ReviewStageStatus(Enum):
    """审查阶段状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class ReviewStageInfo:
    """审查阶段信息"""
    name: str
    description: str
    order: int
    status: ReviewStageStatus = ReviewStageStatus.PENDING
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    is_blocking: bool = True

@dataclass
class ReviewProgress:
    """审查进度信息"""
    current_stage: Optional[str] = None
    completed_stages: int = 0
    total_stages: int = 4
    stages: List[ReviewStageInfo] = field(default_factory=list)
    overall_status: ReviewStageStatus = ReviewStageStatus.PENDING
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    error_message: Optional[str] = None

@dataclass
class ProjectStatus:
    """项目状态信息"""
    id: str
    state: ProjectState = ProjectState.INITIALIZED
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())
    review_progress: Optional[ReviewProgress] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        if self.review_progress is None:
            self.review_progress = ReviewProgress()
            # ✅ 移除硬编码的四个审查阶段，改为空列表，由项目经理动态配置
            self.review_progress.stages = []
            self.review_progress.total_stages = 0
    
    def configure_review_stages(self, stages_config: List[Dict[str, Any]]):
        """
        配置审查阶段 - 由项目经理调用
        
        Args:
            stages_config: 阶段配置列表，每个配置包含name, description, order, is_blocking等字段
        """
        if not self.review_progress:
            self.review_progress = ReviewProgress()
        
        stages = []
        for config in stages_config:
            stage = ReviewStageInfo(
                name=config["name"],
                description=config["description"],
                order=config["order"],
                status=ReviewStageStatus.PENDING,
                is_blocking=config.get("is_blocking", True)
            )
            stages.append(stage)
        
        self.review_progress.stages = stages
        self.review_progress.total_stages = len(stages)
        self.review_progress.completed_stages = 0
        self.review_progress.current_stage = None
    
    def update_state(self, new_state: ProjectState):
        """更新项目状态"""
        self.state = new_state
        self.last_updated = datetime.now().isoformat()
    
    def start_review(self):
        """开始审查"""
        self.update_state(ProjectState.REVIEWING)
        if self.review_progress:
            self.review_progress.overall_status = ReviewStageStatus.RUNNING
            self.review_progress.start_time = datetime.now().isoformat()
    
    def complete_review(self, success: bool = True, error_message: Optional[str] = None):
        """完成审查"""
        if success:
            self.update_state(ProjectState.COMPLETED)
            if self.review_progress:
                self.review_progress.overall_status = ReviewStageStatus.COMPLETED
        else:
            self.update_state(ProjectState.FAILED)
            if self.review_progress:
                self.review_progress.overall_status = ReviewStageStatus.FAILED
                self.review_progress.error_message = error_message
        
        if self.review_progress:
            self.review_progress.end_time = datetime.now().isoformat()
    
    def update_stage_progress(self, stage_name: str, status: ReviewStageStatus, 
                            message: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        """更新阶段进度"""
        if not self.review_progress:
            return
        
        for stage in self.review_progress.stages:
            if stage.name == stage_name:
                stage.status = status
                stage.message = message
                stage.details = details
                
                if status == ReviewStageStatus.RUNNING and not stage.start_time:
                    stage.start_time = datetime.now().isoformat()
                elif status in [ReviewStageStatus.COMPLETED, ReviewStageStatus.FAILED, ReviewStageStatus.SKIPPED]:
                    stage.end_time = datetime.now().isoformat()
                
                # 更新当前阶段
                if status == ReviewStageStatus.RUNNING:
                    self.review_progress.current_stage = stage_name
                
                # 更新完成阶段数
                completed = sum(1 for s in self.review_progress.stages 
                              if s.status in [ReviewStageStatus.COMPLETED, ReviewStageStatus.SKIPPED])
                self.review_progress.completed_stages = completed
                
                break
        
        self.last_updated = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        review_progress_dict = None
        if self.review_progress:
            review_progress_dict = {
                "current_stage": self.review_progress.current_stage,
                "completed_stages": self.review_progress.completed_stages,
                "total_stages": self.review_progress.total_stages,
                "overall_status": self.review_progress.overall_status.value,
                "start_time": self.review_progress.start_time,
                "end_time": self.review_progress.end_time,
                "error_message": self.review_progress.error_message,
                "stages": [
                    {
                        "name": stage.name,
                        "description": stage.description,
                        "order": stage.order,
                        "status": stage.status.value,
                        "start_time": stage.start_time,
                        "end_time": stage.end_time,
                        "message": stage.message,
                        "details": stage.details,
                        "is_blocking": stage.is_blocking
                    }
                    for stage in self.review_progress.stages
                ]
            }
        
        return {
            "id": self.id,
            "state": self.state.value,
            "created_at": self.created_at,
            "last_updated": self.last_updated,
            "review_progress": review_progress_dict,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProjectStatus':
        """从字典创建实例"""
        # 处理枚举类型
        state = ProjectState(data.get("state", "initialized"))
        
        # 处理review_progress
        review_progress_data = data.get("review_progress")
        review_progress = None
        if review_progress_data:
            review_progress = ReviewProgress()
            review_progress.current_stage = review_progress_data.get("current_stage")
            review_progress.completed_stages = review_progress_data.get("completed_stages", 0)
            review_progress.total_stages = review_progress_data.get("total_stages", 4)
            review_progress.overall_status = ReviewStageStatus(review_progress_data.get("overall_status", "pending"))
            review_progress.start_time = review_progress_data.get("start_time")
            review_progress.end_time = review_progress_data.get("end_time")
            review_progress.error_message = review_progress_data.get("error_message")
            
            # 处理stages
            stages_data = review_progress_data.get("stages", [])
            stages = []
            for stage_data in stages_data:
                stage = ReviewStageInfo(
                    name=stage_data.get("name"),
                    description=stage_data.get("description"),
                    order=stage_data.get("order", 0),
                    status=ReviewStageStatus(stage_data.get("status", "pending")),
                    start_time=stage_data.get("start_time"),
                    end_time=stage_data.get("end_time"),
                    message=stage_data.get("message"),
                    details=stage_data.get("details"),
                    is_blocking=stage_data.get("is_blocking", True)
                )
                stages.append(stage)
            review_progress.stages = stages
        
        return cls(
            id=data.get("id"),
            state=state,
            created_at=data.get("created_at"),
            last_updated=data.get("last_updated"),
            review_progress=review_progress,
            metadata=data.get("metadata", {})
        )
    
    def is_valid(self) -> bool:
        """验证数据有效性"""
        if not self.id:
            return False
        return True
    
    def save_to_file(self, file_path: str):
        """保存到文件"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
        except Exception as e:
            raise Exception(f"保存状态文件失败: {str(e)}")
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'ProjectStatus':
        """从文件加载"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"状态文件不存在: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return cls.from_dict(data)
        except Exception as e:
            raise Exception(f"加载状态文件失败: {str(e)}")

class StatusManager:
    """状态管理器 - 提供状态文件的读写和管理功能"""
    
    def __init__(self, workspace_path: str):
        self.workspace_path = workspace_path
        self.status_file_path = os.path.join(workspace_path, "status.json")
    
    def create_initial_status(self, project_id: str, design_doc_path: str) -> ProjectStatus:
        """创建初始状态"""
        status = ProjectStatus(
            id=project_id,
            design_doc_path=design_doc_path,
            workspace_path=self.workspace_path
        )
        status.save_to_file(self.status_file_path)
        return status
    
    def load_status(self) -> Optional[ProjectStatus]:
        """加载状态"""
        try:
            return ProjectStatus.load_from_file(self.status_file_path)
        except Exception as e:
            print(f"加载状态失败: {e}")
            return None
    
    def save_status(self, status: ProjectStatus):
        """保存状态"""
        status.save_to_file(self.status_file_path)
    
    def update_status(self, **kwargs):
        """更新状态"""
        status = self.load_status()
        if status:
            for key, value in kwargs.items():
                if hasattr(status, key):
                    setattr(status, key, value)
            status.last_updated = datetime.now().isoformat()
            self.save_status(status)
    
    def exists(self) -> bool:
        """检查状态文件是否存在"""
        return os.path.exists(self.status_file_path) 