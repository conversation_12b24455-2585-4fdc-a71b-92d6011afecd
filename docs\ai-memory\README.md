# XKongCloud AI记忆系统

## 概述

XKongCloud AI记忆系统是一个为AI助手设计的分层知识库，旨在提高AI在XKongCloud项目开发过程中的理解能力、一致性和效率。该系统通过结构化存储项目知识、上下文信息和索引，使AI能够快速检索相关信息，理解项目架构和约束，并提供高质量的开发支持。

## 系统结构

AI记忆系统采用三层架构设计：

### L1-核心层（Core Layer）

核心层包含项目的基础知识和全局约束，是AI理解项目的基础。包括：

- **全局约束（global-constraints.md）**：记录项目中必须严格遵循的硬约束
- **项目矩阵（project-matrix.md）**：描述XKongCloud各子项目之间的关系
- **关键模式（key-patterns.md）**：记录项目中使用的关键设计模式和架构模式
- **注意力命令（attention-commands.md）**：AI应关注的特定指令和检查项

### L2-上下文层（Context Layer）

上下文层提供特定场景和任务的详细上下文信息，帮助AI理解具体任务的背景和要求。包括：

- **任务类型（task-types/）**：不同类型任务的上下文信息
  - 数据库任务（database-tasks.md）
  - 测试任务（testing-tasks.md）
  - 其他任务类型...
  
- **项目组合（project-groups/）**：多个项目协同工作的上下文
  - XKC-CORE与XKC-UID集成（xkc-core+uid.md）
  - 其他项目组合...
  
- **项目特定（projects/）**：单个项目的特定上下文
  - 未来扩展...
  
- **技术栈（tech-stack/）**：技术栈相关的上下文
  - PostgreSQL技术栈（postgresql-stack.md）
  - 其他技术栈...

### L3-索引层（Index Layer）

索引层提供快速查找和关联信息的入口，帮助AI高效检索相关知识。包括：

- **功能索引（feature-index/）**：按项目组织的功能特性索引
  - 按项目分类（by-project/）
    - XKC-CORE项目功能（xkc-core/）
      - PostgreSQL迁移（F003-PostgreSQL迁移.md）
    - 其他项目...
  
- **关键词索引（keyword-index/）**：关键技术和概念的索引
  - PostgreSQL（PostgreSQL.md）
  - 其他关键词...
  
- **依赖关系索引（dependency-index/）**：项目间依赖关系的索引
  - XKC-CORE与XKC-UID依赖（XKC-CORE-UID.md）
  - 其他依赖关系...

## 索引文件

系统使用memory-index.json作为总索引文件，记录所有内存文件的元数据和关联关系。该文件包含：

- 版本信息
- 创建和更新日期
- 层级结构描述
- 内存文件列表，每个文件包含：
  - 路径
  - 类型
  - 标题
  - 标签

## 使用方法

### 对AI助手的指导

AI助手在处理XKongCloud项目相关任务时，应遵循以下步骤：

1. **初始化理解**：
   - 访问核心层文档，理解项目的全局约束和关键模式
   - 了解项目矩阵中的依赖关系

2. **上下文分析**：
   - 根据任务类型，查阅相关的上下文层文档
   - 理解特定技术栈的上下文信息

3. **相关知识检索**：
   - 使用索引层快速查找任务相关的功能、关键词和依赖关系
   - 综合多个相关文档的信息

4. **记忆引用**：
   - 在回答中引用相关的记忆文档
   - 确保建议和解决方案符合记录的约束和模式

### 对开发人员的指导

开发人员可以通过以下方式使用和维护AI记忆系统：

1. **查阅知识**：
   - 浏览memory-index.json了解系统结构
   - 查看特定文档获取详细信息

2. **更新记忆**：
   - 修改或添加记忆文档
   - 更新memory-index.json以包含新文档

3. **引导AI**：
   - 在与AI交互时引用特定记忆文档
   - 使用注意力命令引导AI关注特定约束

## 维护指南

### 功能文档同步规则

AI记忆系统与功能文档（`docs/features/`）之间建立了同步机制，确保信息的时效性和一致性：

#### 1. 功能状态分类

- **active_development**：正在积极开发，文档频繁变更
  - **文档优先级**：优先读取原始功能文档（`docs/features/`）
  - **同步频率**：每次重要更新后
  - **AI行为**：必须优先查看原始文档，避免依赖记忆索引

- **stable_maintenance**：已完成主要开发，仅维护性更新
  - **文档优先级**：记忆索引可用，但需验证同步状态
  - **同步频率**：每周检查一次
  - **AI行为**：可使用记忆索引，但需检查同步日期

- **completed_archived**：已完成并归档，文档基本不变
  - **文档优先级**：记忆索引作为主要参考
  - **同步频率**：每月验证一次
  - **AI行为**：可安全依赖记忆索引

#### 2. 同步状态标记

每个功能索引文档必须包含"同步状态"部分：

```markdown
## 同步状态
- **索引创建日期**: YYYY-MM-DD
- **最后同步日期**: YYYY-MM-DD
- **同步来源**: docs/features/[功能目录]/
- **功能状态**: active_development|stable_maintenance|completed_archived
- **同步状态**: up_to_date|requires_update|sync_in_progress|sync_failed
- **同步检查频率**: 根据功能状态确定的检查频率
- **优先级**: high|medium|low
```

#### 3. 同步命令

开发团队和AI可以使用以下命令管理同步：

- `@sync:feature:F003` - 手动触发指定功能的记忆索引同步
- `@status:feature:F003` - 查询指定功能的当前状态和同步信息
- `@check:sync` - 检查所有功能的同步状态

#### 4. 功能状态注册表

系统使用 `docs/feature-status.json` 作为中央化的功能开发状态标记：

- 记录所有功能的状态、文档路径和同步信息
- 提供功能状态定义和同步规则
- 支持项目映射和命令语法说明

#### 5. 同步工具

使用 `docs/ai-memory/tools/verify-memory-system.js` 验证同步状态：

```bash
# 验证所有功能
node docs/ai-memory/tools/verify-memory-system.js

# 验证特定功能
node docs/ai-memory/tools/verify-memory-system.js --feature F003

# 检查同步状态
node docs/ai-memory/tools/verify-memory-system.js --check-sync

# 生成完整报告
node docs/ai-memory/tools/verify-memory-system.js --full-report
```

### 文档更新规则

1. **版本控制**：
   - 记忆文档应包含版本号和最后更新日期
   - 重要变更应记录在文档底部的变更历史中

2. **格式一致性**：
   - 所有记忆文档应使用Markdown格式
   - 遵循统一的文档结构和格式

3. **索引更新**：
   - 添加新文档后，应更新memory-index.json
   - 确保添加适当的标签以便有效检索

4. **同步状态维护**：
   - 创建新功能索引时，必须使用模板 `docs/ai-memory/templates/feature-index-template.md`
   - 功能状态变更时，必须更新 `docs/feature-status.json`
   - 定期运行验证工具检查同步状态

### 记忆索引更新指南

#### 如何正确更新记忆索引

1. **创建新功能索引**：
   ```bash
   # 1. 复制模板
   cp docs/ai-memory/templates/feature-index-template.md \
      docs/ai-memory/L3-index/feature-index/by-project/[项目]/feature-[功能ID].md
   
   # 2. 替换模板中的变量
   # 编辑新文件，将所有 {变量} 替换为实际内容
   
   # 3. 更新功能状态注册表
   # 在 docs/feature-status.json 中添加新功能条目
   ```

2. **更新现有功能索引**：
   ```bash
   # 1. 检查同步状态
   node docs/ai-memory/tools/verify-memory-system.js --feature F003
   
   # 2. 手动更新索引文档内容
   # 同步原始功能文档的最新信息
   
   # 3. 更新同步状态信息
   # 修改"同步状态"部分的日期和状态
   
   # 4. 更新功能状态注册表
   # 在 docs/feature-status.json 中更新对应条目
   ```

3. **功能状态变更流程**：
   ```bash
   # 当功能从 active_development 变为 stable_maintenance 时：
   # 1. 强制同步记忆索引
   # 2. 更新 docs/feature-status.json 中的状态
   # 3. 验证同步状态
   node docs/ai-memory/tools/verify-memory-system.js --feature F003
   ```

#### feature-status.json 维护方法

1. **添加新功能**：
   ```json
   {
     "F005": {
       "name": "新功能名称",
       "status": "active_development",
       "original_doc_path": "docs/features/F005-功能名称-20250115/",
       "memory_index_path": "docs/ai-memory/L3-index/feature-index/by-project/项目/feature-F005.md",
       "last_sync_date": "2025-01-15",
       "project": "项目代号",
       "sync_status": "up_to_date",
       "current_phase": "当前开发阶段",
       "priority": "high|medium|low",
       "dependencies": ["依赖的功能ID"],
       "related_projects": ["相关项目列表"],
       "key_technologies": ["关键技术列表"]
     }
   }
   ```

2. **更新功能状态**：
   - 修改 `status` 字段（active_development → stable_maintenance → completed_archived）
   - 更新 `last_sync_date`
   - 修改 `sync_status`
   - 更新 `current_phase`

3. **维护项目映射**：
   - 在 `project_mapping` 部分添加新项目
   - 更新现有项目的功能列表

#### 同步最佳实践

1. **定期检查**：
   ```bash
   # 每周运行一次完整检查
   node docs/ai-memory/tools/verify-memory-system.js --full-report
   
   # 在重要功能更新后检查特定功能
   node docs/ai-memory/tools/verify-memory-system.js --feature F003
   ```

2. **同步触发时机**：
   - 功能文档有重要更新时
   - 功能状态发生变更时
   - 定期维护检查时
   - 发现记忆索引信息过时时

3. **错误处理**：
   - 同步失败时，检查原始文档是否存在
   - 验证 feature-status.json 格式正确性
   - 确保记忆索引文档路径正确

### 扩展建议

AI记忆系统可以通过以下方式扩展：

1. **新增文档类型**：
   - 添加新的上下文类型
   - 创建更多专业领域的索引

2. **交叉引用增强**：
   - 在文档之间添加更多交叉引用
   - 构建知识图谱增强关联关系

3. **版本控制增强**：
   - 添加记忆文档的版本控制机制
   - 实现不同项目版本的记忆分支

4. **自动化同步**：
   - 开发基于Git提交的自动同步机制
   - 实现CI/CD集成的同步检查
   - 添加同步状态的可视化仪表板

## 记忆文件模板

### 核心层文档模板

```markdown
# [文档标题]

## 概述
[概述内容]

## [主要部分1]
[内容]

## [主要部分2]
[内容]

## 注意事项
[注意事项]

## 变更历史
| 版本 | 日期 | 变更内容 | 变更人 |
|-----|-----|---------|-------|
| 1.0 | [日期] | 初始版本 | [作者] |
```

### 上下文层文档模板

```markdown
# [上下文标题]

## 背景
[背景信息]

## [上下文部分1]
[内容]

## [上下文部分2]
[内容]

## 常见问题和解决方案
[问题和解决方案]
```

### 索引层文档模板

```markdown
# [索引类型]：[索引名称]

## 基本信息
[基本信息]

## [主要内容1]
[内容]

## [主要内容2]
[内容]

## 相关资源和文档
[相关资源列表]
``` 