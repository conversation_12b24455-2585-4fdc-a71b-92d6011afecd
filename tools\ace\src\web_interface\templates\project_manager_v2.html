<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2项目经理工作台 - 虚拟项目经理交互版</title>
    
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>

    <!-- 复用nine_grid的基础样式和脚本 -->
    <link rel="stylesheet" href="/static/css/nine_grid_base.css">
    <link rel="stylesheet" href="/static/css/pm_v2_styles.css">
    <script src="/static/js/nine_grid_base.js"></script>
</head>
<body>
    <!-- 左侧菜单感应区域 -->
    <div class="left-menu-trigger" id="leftMenuTrigger"></div>
    
    <!-- 自动隐藏左侧菜单 -->
    <nav class="left-menu" id="leftMenu">
        <div class="menu-title">🏗️ V2项目经理工作台</div>
        <a href="/" class="menu-item" data-target="home">🏠 主页</a>
        <a href="/nine-grid" class="menu-item" data-target="nine-grid">🔧 九宫格系统</a>
        <a href="/debug" class="menu-item" data-target="debug">🐛 调试中心</a>
        <a href="/api/status" class="menu-item" data-target="status" target="_blank">📊 系统状态</a>
        <a href="/api/health" class="menu-item" data-target="health" target="_blank">❤️ 健康检查</a>
        <div class="menu-item" onclick="openConfigCenter()">⚙️ 配置中心</div>
    </nav>

    <!-- 九宫格主容器 -->
    <div class="nine-grid-container">
        <!-- 区域1-2：项目进度监控 (由组件渲染) -->
        <div id="progress-area" class="grid-area grid-area-1-2 vscode-scrollbar"></div>

        <!-- 区域3：项目风险评估 (由组件渲染) -->
        <div id="risk-area" class="grid-area grid-area-3 vscode-scrollbar"></div>

        <!-- 区域4：项目经理状态 (由组件渲染) -->
        <div id="manager-area" class="grid-area grid-area-4 vscode-scrollbar"></div>

        <!-- 区域5：Python主持人算法思维 (由组件渲染) -->
        <div id="algorithm-area" class="grid-area grid-area-5 vscode-scrollbar"></div>

        <!-- 区域6：项目约束审查 (由组件渲染) -->
        <div id="constraint-area" class="grid-area grid-area-6 vscode-scrollbar"></div>

        <!-- 区域7：项目知识库 (由组件渲染) -->
        <div id="knowledge-area" class="grid-area grid-area-7 vscode-scrollbar"></div>

        <!-- 区域8：人类输入控制区 (由组件渲染) -->
        <div id="control-area" class="grid-area grid-area-8"></div>

        <!-- 区域9：项目交付结果 (由组件渲染) -->
        <div id="deliverables-area" class="grid-area grid-area-9 vscode-scrollbar"></div>
    </div>

    <!-- Unified Architecture Core -->
    <script src="/static/js/unified/http-client.js"></script>
    <script src="/static/js/unified/base-component.js"></script>
    <script src="/static/js/unified/data-manager.js"></script>
    <script src="/static/js/unified/app-manager.js"></script>

    <!-- PM_V2 Data Configuration -->
    <script src="/static/js/pm_v2_data_config.js"></script>

    <!-- PM_V2 Components -->
    <script src="/static/js/components/ProjectProgressComponent.js"></script>
    <script src="/static/js/components/RiskAssessmentComponent.js"></script>
    <script src="/static/js/components/ManagerStatusComponent.js"></script>
    <script src="/static/js/components/AlgorithmMindsetComponent.js"></script>
    <script src="/static/js/components/ConstraintReviewComponent.js"></script>
    <script src="/static/js/components/KnowledgeBaseComponent.js"></script>
    <script src="/static/js/components/HumanInputComponent.js"></script>
    <script src="/static/js/components/ProjectOutputComponent.js"></script>

    <!-- Left Menu JavaScript -->
    <script>
        // 配置中心点击事件
        window.openConfigCenter = function() {
            alert('配置中心功能开发中...');
        };

        // 左侧菜单交互逻辑 - 使用更可靠的事件绑定
        function initLeftMenu() {
            const leftMenuTrigger = document.getElementById('leftMenuTrigger');
            const leftMenu = document.getElementById('leftMenu');

            if (!leftMenuTrigger || !leftMenu) {
                console.log('Left menu elements not found, retrying...');
                setTimeout(initLeftMenu, 100);
                return;
            }

            let hideTimeout;
            console.log('Initializing left menu with elements:', leftMenuTrigger, leftMenu);

            // 使用 onmouseenter/onmouseleave 属性方式，更可靠
            leftMenuTrigger.onmouseenter = function() {
                console.log('Mouse entered trigger area!');
                clearTimeout(hideTimeout);
                leftMenu.classList.add('show');
            };

            leftMenu.onmouseenter = function() {
                console.log('Mouse entered menu!');
                clearTimeout(hideTimeout);
                leftMenu.classList.add('show');
            };

            leftMenuTrigger.onmouseleave = function() {
                console.log('Mouse left trigger area!');
                hideTimeout = setTimeout(() => {
                    leftMenu.classList.remove('show');
                }, 300);
            };

            leftMenu.onmouseleave = function() {
                console.log('Mouse left menu!');
                hideTimeout = setTimeout(() => {
                    leftMenu.classList.remove('show');
                }, 300);
            };

            console.log('Left menu initialized successfully');
        }

        // 在DOM加载完成后和页面加载完成后都尝试初始化
        document.addEventListener('DOMContentLoaded', initLeftMenu);
        window.addEventListener('load', initLeftMenu);
    </script>

    <!-- App Initialization -->
    <script src="/static/js/pm_v2_unified_init.js"></script>
</body>
</html>
