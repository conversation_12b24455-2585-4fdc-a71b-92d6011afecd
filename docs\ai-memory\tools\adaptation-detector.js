/**
 * Adaptation Detector for docs/common → docs/ai-memory Synchronization
 * 
 * This tool monitors docs/common for changes and triggers automatic adaptation
 * of docs/ai-memory content to maintain consistency with authoritative sources.
 */

const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');

class AdaptationDetector {
    constructor() {
        this.authorityRules = this.loadAuthorityRules();
        this.watchedPaths = [
            'docs/common/templates/',
            'docs/common/best-practices/',
            'docs/common/architecture/',
            'docs/common/middleware/',
            'docs/common/protocols/',
            'docs/common/security/'
        ];
        this.changeQueue = [];
        this.isProcessing = false;
    }

    loadAuthorityRules() {
        try {
            const rulesPath = path.join(__dirname, 'authority-rules.json');
            return JSON.parse(fs.readFileSync(rulesPath, 'utf8'));
        } catch (error) {
            console.error('Failed to load authority rules:', error);
            return null;
        }
    }

    startMonitoring() {
        console.log('Starting docs/common monitoring for automatic adaptation...');
        
        const watcher = chokidar.watch(this.watchedPaths, {
            ignored: /(^|[\/\\])\../, // ignore dotfiles
            persistent: true,
            ignoreInitial: true
        });

        watcher
            .on('change', (filePath) => this.handleFileChange(filePath, 'change'))
            .on('add', (filePath) => this.handleFileChange(filePath, 'add'))
            .on('unlink', (filePath) => this.handleFileChange(filePath, 'delete'))
            .on('error', (error) => console.error('Watcher error:', error));

        console.log('Monitoring active for paths:', this.watchedPaths);
        return watcher;
    }

    handleFileChange(filePath, changeType) {
        console.log(`Detected ${changeType} in: ${filePath}`);
        
        const changeEvent = {
            filePath: filePath,
            changeType: changeType,
            timestamp: new Date().toISOString(),
            processed: false
        };

        this.changeQueue.push(changeEvent);
        this.processChangeQueue();
    }

    async processChangeQueue() {
        if (this.isProcessing || this.changeQueue.length === 0) {
            return;
        }

        this.isProcessing = true;
        
        try {
            while (this.changeQueue.length > 0) {
                const change = this.changeQueue.shift();
                await this.processChange(change);
            }
        } catch (error) {
            console.error('Error processing change queue:', error);
        } finally {
            this.isProcessing = false;
        }
    }

    async processChange(change) {
        console.log(`Processing change: ${change.filePath} (${change.changeType})`);
        
        // Determine affected AI memory files based on semantic mapping
        const affectedFiles = this.determineAffectedFiles(change.filePath);
        
        if (affectedFiles.length === 0) {
            console.log('No AI memory files affected by this change');
            return;
        }

        // Extract semantic changes from the source file
        const semanticChanges = await this.extractSemanticChanges(change);
        
        // Propagate changes to affected AI memory files
        for (const targetFile of affectedFiles) {
            await this.propagateChanges(change.filePath, targetFile, semanticChanges);
        }

        // Verify consistency after propagation
        await this.verifyConsistency(change.filePath, affectedFiles);
        
        change.processed = true;
        console.log(`Successfully processed change for: ${change.filePath}`);
    }

    determineAffectedFiles(sourceFilePath) {
        const affectedFiles = [];
        const mappingRules = this.authorityRules?.automatic_adaptation_rules?.semantic_mapping_rules;
        
        if (!mappingRules) {
            return affectedFiles;
        }

        for (const [ruleKey, rule] of Object.entries(mappingRules)) {
            if (this.matchesPattern(sourceFilePath, rule.source_pattern)) {
                affectedFiles.push(...rule.affects);
            }
        }

        return [...new Set(affectedFiles)]; // Remove duplicates
    }

    matchesPattern(filePath, pattern) {
        // Convert glob pattern to regex for matching
        const regexPattern = pattern
            .replace(/\*\*/g, '.*')
            .replace(/\*/g, '[^/]*')
            .replace(/\./g, '\\.');
        
        const regex = new RegExp(regexPattern);
        return regex.test(filePath);
    }

    async extractSemanticChanges(change) {
        // This is a simplified implementation
        // In a full implementation, this would parse the file content
        // and extract semantic changes relevant to AI memory updates
        
        return {
            changeType: change.changeType,
            filePath: change.filePath,
            timestamp: change.timestamp,
            extractedContent: 'Semantic analysis would be performed here'
        };
    }

    async propagateChanges(sourceFile, targetFile, semanticChanges) {
        console.log(`Propagating changes from ${sourceFile} to ${targetFile}`);
        
        // This would implement the actual propagation logic
        // For now, we log the intended action
        console.log(`Would update ${targetFile} based on changes in ${sourceFile}`);
        
        // Record the propagation in update history
        this.recordPropagation(sourceFile, targetFile, semanticChanges);
    }

    recordPropagation(sourceFile, targetFile, changes) {
        const propagationRecord = {
            source: sourceFile,
            target: targetFile,
            timestamp: new Date().toISOString(),
            changes: changes,
            status: 'completed'
        };

        // In a full implementation, this would be stored in a persistent log
        console.log('Propagation recorded:', propagationRecord);
    }

    async verifyConsistency(sourceFile, affectedFiles) {
        console.log(`Verifying consistency for ${sourceFile} and affected files`);
        
        // This would implement consistency verification logic
        // For now, we assume consistency is maintained
        return true;
    }

    getStatus() {
        return {
            isMonitoring: true,
            queueLength: this.changeQueue.length,
            isProcessing: this.isProcessing,
            watchedPaths: this.watchedPaths
        };
    }
}

// Export for use as a module
module.exports = AdaptationDetector;

// CLI usage
if (require.main === module) {
    const detector = new AdaptationDetector();
    const watcher = detector.startMonitoring();
    
    // Graceful shutdown
    process.on('SIGINT', () => {
        console.log('Shutting down adaptation detector...');
        watcher.close();
        process.exit(0);
    });
}
