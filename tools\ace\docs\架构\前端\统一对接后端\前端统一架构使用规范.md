# 前端统一架构使用规范

## 文档概述

本文档定义了基于统一架构的前端开发规范，确保所有开发者遵循一致的模式，实现高效的组件复用和后端对接。

---

## 1. 架构概览

### 核心组件
```
AppManager (应用管理器)
├── DataManager (数据管理器)
├── HttpClient (HTTP客户端)
├── BaseComponent (组件基类)
└── Components (具体组件)
    ├── ProgressComponent
    ├── ConstraintComponent
    └── ...
```

### 数据流向
```
Backend API → DataManager → Components → UI
Backend WebSocket → DataManager → Components → UI
```

---

## 2. 开发规范

### 2.1 新组件开发规范

#### 必须继承BaseComponent
```javascript
class NewComponent extends BaseComponent {
    constructor(containerId, dataManager, config = {}) {
        super(containerId, dataManager, config);
        // 组件特定的初始化
    }
    
    // 必须实现的方法
    getDataTypes() {
        return ['data_type1', 'data_type2'];
    }
    
    render() {
        // 渲染逻辑
    }
    
    // 可选重写的方法
    onDataUpdate(dataType, data, oldData) {
        // 数据更新处理
    }
    
    bindEvents() {
        // 事件绑定
    }
}
```

#### 组件命名规范
- **文件名**: `kebab-case` (如: `progress-component.js`)
- **类名**: `PascalCase` (如: `ProgressComponent`)
- **容器ID**: `kebab-case` (如: `progress-area`)
- **组件类型**: `snake_case` (如: `progress_monitoring`)

#### 组件文件结构
```javascript
/**
 * 组件描述 - 简要说明组件功能
 */
class ComponentName extends BaseComponent {
    // 1. 构造函数
    constructor(containerId, dataManager, config = {}) {}
    
    // 2. 必须实现的抽象方法
    getDataTypes() {}
    render() {}
    
    // 3. 生命周期方法重写
    onDataUpdate() {}
    bindEvents() {}
    onInitialized() {}
    onDestroyed() {}
    
    // 4. 组件特定的公共方法
    publicMethod1() {}
    publicMethod2() {}
    
    // 5. 组件特定的私有方法
    _privateMethod1() {}
    _privateMethod2() {}
}
```

### 2.2 数据管理规范

#### DataManager使用规范
```javascript
// ✅ 正确：通过DataManager获取数据
const data = this.dataManager.getData('progress');

// ✅ 正确：订阅数据更新
const unsubscribe = this.dataManager.subscribe('progress', (data) => {
    this.updateUI(data);
});

// ✅ 正确：获取新数据
await this.dataManager.fetchData('progress', { refresh: true });

// ❌ 错误：直接发起HTTP请求
fetch('/api/progress').then(response => response.json());
```

#### 数据类型命名规范
```javascript
// ✅ 正确的数据类型命名
'progress'              // 进度数据
'risk_assessment'       // 风险评估
'constraints'           // 约束列表
'constraint_detail'     // 约束详情
'knowledge_graph'       // 知识图谱
'manager_status'        // 管理器状态
'algorithm_logs'        // 算法日志
'deliverables'          // 交付结果

// ❌ 错误的命名
'progressData'          // 不使用驼峰命名
'Progress'              // 不使用大写开头
'progress-data'         // 不使用连字符
```

#### 缓存策略规范
```javascript
// 不同数据类型的缓存时间
const CACHE_TTL = {
    'progress': 30000,        // 30秒 - 频繁更新
    'risk_assessment': 60000, // 1分钟 - 中等频率
    'constraints': 120000,    // 2分钟 - 较少更新
    'knowledge_graph': 120000,// 2分钟 - 较少更新
    'manager_status': 10000,  // 10秒 - 高频更新
    'algorithm_logs': 5000,   // 5秒 - 实时数据
    'deliverables': 60000     // 1分钟 - 中等频率
};
```

### 2.3 错误处理规范

#### 组件级错误处理
```javascript
class MyComponent extends BaseComponent {
    async loadData() {
        try {
            await super.loadData();
        } catch (error) {
            // 记录错误
            console.error(`Failed to load data for ${this.containerId}:`, error);
            
            // 显示用户友好的错误信息
            this.renderError(error);
            
            // 可选：上报错误
            this.reportError(error);
        }
    }
    
    onError(error) {
        // 组件特定的错误处理
        if (error.code === 'NETWORK_ERROR') {
            this.showRetryButton();
        }
    }
}
```

#### 全局错误处理
```javascript
// 在应用初始化时设置
const appManager = new AppManager(projectId, {
    errorHandler: (error) => {
        // 全局错误处理逻辑
        console.error('Global error:', error);
        
        // 发送错误报告
        sendErrorReport(error);
        
        // 显示全局错误提示
        showGlobalErrorNotification(error);
    }
});
```

### 2.4 性能优化规范

#### 防抖和节流
```javascript
class MyComponent extends BaseComponent {
    constructor(containerId, dataManager, config) {
        super(containerId, dataManager, config);
        
        // 防抖更新
        this.debouncedUpdate = this.debounce(this.updateUI.bind(this), 300);
        
        // 节流滚动
        this.throttledScroll = this.throttle(this.onScroll.bind(this), 100);
    }
    
    onDataUpdate(dataType, data) {
        // 使用防抖避免频繁更新
        this.debouncedUpdate(data);
    }
}
```

#### 内存管理
```javascript
class MyComponent extends BaseComponent {
    onDestroyed() {
        // 清理定时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        
        // 清理事件监听器
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
            this.resizeHandler = null;
        }
        
        // 清理DOM引用
        this.cachedElements = null;
    }
}
```

---

## 3. 应用集成规范

### 3.1 应用初始化标准流程

#### HTML页面集成
```html
<!DOCTYPE html>
<html>
<head>
    <title>Project Manager V2</title>
    <link rel="stylesheet" href="static/css/nine_grid_base.css">
</head>
<body>
    <!-- 九宫格布局 -->
    <div class="nine-grid-container">
        <div id="progress-area" class="grid-area grid-area-1"></div>
        <div id="risk-area" class="grid-area grid-area-3"></div>
        <!-- 其他区域... -->
    </div>
    
    <!-- 统一架构脚本 -->
    <script src="static/js/unified/http-client.js"></script>
    <script src="static/js/unified/data-manager.js"></script>
    <script src="static/js/unified/base-component.js"></script>
    <script src="static/js/unified/app-manager.js"></script>
    
    <!-- 组件脚本 -->
    <script src="static/js/unified/components/progress-component.js"></script>
    <script src="static/js/unified/components/risk-component.js"></script>
    <!-- 其他组件... -->
    
    <!-- 应用初始化 -->
    <script src="static/js/app-init.js"></script>
</body>
</html>
```

#### 应用初始化脚本 (app-init.js)
```javascript
// 获取项目ID的标准方法
function getCurrentProjectId() {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('project_id');
    
    if (projectId) {
        return projectId;
    }
    
    // 从localStorage获取
    const storedProjectId = localStorage.getItem('current_project_id');
    if (storedProjectId) {
        return storedProjectId;
    }
    
    // 默认项目ID
    return 'default_project';
}

// 标准初始化流程
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // 1. 获取项目ID
        const projectId = getCurrentProjectId();
        console.log(`Initializing app for project: ${projectId}`);
        
        // 2. 创建应用管理器
        const appManager = await initializeApp(projectId, {
            errorHandler: (error) => {
                console.error('Application error:', error);
                // 可以添加错误上报逻辑
            }
        });
        
        // 3. 注册所有组件
        appManager.registerComponents([
            { type: 'progress', containerId: 'progress-area', ComponentClass: ProgressComponent },
            { type: 'risk', containerId: 'risk-area', ComponentClass: RiskComponent },
            { type: 'constraint', containerId: 'constraint-area', ComponentClass: ConstraintComponent },
            { type: 'knowledge', containerId: 'knowledge-area', ComponentClass: KnowledgeComponent },
            { type: 'manager', containerId: 'manager-area', ComponentClass: ManagerComponent },
            { type: 'algorithm', containerId: 'algorithm-area', ComponentClass: AlgorithmComponent },
            { type: 'control', containerId: 'control-area', ComponentClass: ControlComponent },
            { type: 'deliverables', containerId: 'deliverables-area', ComponentClass: DeliverablesComponent }
        ]);
        
        // 4. 显示初始化成功消息
        console.log('✅ Application initialized successfully');
        
        // 5. 可选：显示用户提示
        showInitializationSuccess();
        
    } catch (error) {
        console.error('❌ Failed to initialize application:', error);
        showInitializationError(error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    const appManager = getAppManager();
    if (appManager) {
        appManager.destroy();
    }
});

// 初始化成功提示
function showInitializationSuccess() {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 1rem;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 0.9rem;
    `;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <span>✅</span>
            <span>应用初始化成功</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 初始化错误提示
function showInitializationError(error) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #F44336;
        color: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
        z-index: 10000;
        text-align: center;
        max-width: 400px;
    `;
    
    errorDiv.innerHTML = `
        <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>
        <div style="font-weight: bold; margin-bottom: 1rem;">应用初始化失败</div>
        <div style="font-size: 0.9rem; margin-bottom: 1.5rem; opacity: 0.9;">
            ${error.message || '未知错误'}
        </div>
        <button onclick="location.reload()" style="
            background: white;
            color: #F44336;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        ">重新加载页面</button>
    `;
    
    document.body.appendChild(errorDiv);
}
```

### 3.2 组件注册规范

#### 标准组件注册
```javascript
// 组件配置对象
const COMPONENT_CONFIGS = [
    {
        type: 'progress',
        containerId: 'progress-area',
        ComponentClass: ProgressComponent,
        config: {
            animationDuration: 800,
            updateDebounceDelay: 300
        }
    },
    {
        type: 'risk',
        containerId: 'risk-area',
        ComponentClass: RiskComponent,
        config: {
            refreshInterval: 30000,
            showPreventionDetails: true
        }
    }
    // 其他组件...
];

// 批量注册
appManager.registerComponents(COMPONENT_CONFIGS);
```

#### 动态组件注册
```javascript
// 根据用户权限动态注册组件
function registerComponentsByPermission(appManager, userPermissions) {
    const availableComponents = [];
    
    if (userPermissions.includes('view_progress')) {
        availableComponents.push({
            type: 'progress',
            containerId: 'progress-area',
            ComponentClass: ProgressComponent
        });
    }
    
    if (userPermissions.includes('view_constraints')) {
        availableComponents.push({
            type: 'constraint',
            containerId: 'constraint-area',
            ComponentClass: ConstraintComponent
        });
    }
    
    appManager.registerComponents(availableComponents);
}
```

---

## 4. 调试和测试规范

### 4.1 调试工具

#### 浏览器控制台调试
```javascript
// 全局调试对象
window.debug = {
    // 获取应用状态
    getAppStatus() {
        const appManager = getAppManager();
        return appManager ? appManager.getStatus() : null;
    },
    
    // 获取组件实例
    getComponent(type) {
        const appManager = getAppManager();
        return appManager ? appManager.getComponent(type) : null;
    },
    
    // 获取数据管理器
    getDataManager() {
        const appManager = getAppManager();
        return appManager ? appManager.dataManager : null;
    },
    
    // 刷新所有组件
    async refreshAll() {
        const appManager = getAppManager();
        if (appManager) {
            await appManager.refreshAll();
        }
    },
    
    // 重试失败的组件
    async retryComponent(type) {
        const appManager = getAppManager();
        if (appManager) {
            await appManager.retryComponent(type);
        }
    }
};

// 使用示例：
// debug.getAppStatus()
// debug.getComponent('progress')
// debug.refreshAll()
```

#### 日志级别控制
```javascript
// 设置日志级别
window.LOG_LEVEL = 'DEBUG'; // DEBUG, INFO, WARN, ERROR

function log(level, message, ...args) {
    const levels = ['DEBUG', 'INFO', 'WARN', 'ERROR'];
    const currentLevelIndex = levels.indexOf(window.LOG_LEVEL || 'INFO');
    const messageLevelIndex = levels.indexOf(level);
    
    if (messageLevelIndex >= currentLevelIndex) {
        console[level.toLowerCase()](message, ...args);
    }
}

// 在组件中使用
log('DEBUG', 'Component initialized:', this.containerId);
log('INFO', 'Data updated:', dataType, data);
log('WARN', 'Deprecated method used:', methodName);
log('ERROR', 'Component error:', error);
```

### 4.2 单元测试规范

#### 组件测试模板
```javascript
// test/components/progress-component.test.js
describe('ProgressComponent', () => {
    let component;
    let mockDataManager;
    let container;
    
    beforeEach(() => {
        // 创建测试容器
        container = document.createElement('div');
        container.id = 'test-progress-area';
        document.body.appendChild(container);
        
        // 创建模拟数据管理器
        mockDataManager = {
            getData: jest.fn(),
            subscribe: jest.fn(() => () => {}),
            fetchData: jest.fn()
        };
        
        // 创建组件实例
        component = new ProgressComponent('test-progress-area', mockDataManager);
    });
    
    afterEach(() => {
        // 清理
        if (component) {
            component.destroy();
        }
        if (container && container.parentElement) {
            container.remove();
        }
    });
    
    test('should initialize correctly', async () => {
        await component.init();
        expect(component.isInitialized).toBe(true);
    });
    
    test('should render progress data', () => {
        const mockData = {
            current_stage: { stage_number: 0, status: 'in_progress' },
            stage_zero_metrics: { pre_validation_pass_rate: 100 }
        };
        
        mockDataManager.getData.mockReturnValue(mockData);
        component.render();
        
        expect(container.innerHTML).toContain('阶段零');
    });
    
    test('should handle data updates', () => {
        const mockData = { current_stage: { stage_number: 1 } };
        const spy = jest.spyOn(component, 'updateDisplay');
        
        component.onDataUpdate('progress', mockData);
        
        expect(spy).toHaveBeenCalledWith(mockData);
    });
});
```

### 4.3 集成测试规范

#### 端到端测试
```javascript
// test/integration/app-integration.test.js
describe('App Integration', () => {
    let appManager;
    
    beforeEach(async () => {
        // 设置测试环境
        document.body.innerHTML = `
            <div id="progress-area"></div>
            <div id="risk-area"></div>
        `;
        
        // 初始化应用
        appManager = await initializeApp('test-project');
        appManager.registerComponents([
            { type: 'progress', containerId: 'progress-area', ComponentClass: ProgressComponent },
            { type: 'risk', containerId: 'risk-area', ComponentClass: RiskComponent }
        ]);
    });
    
    afterEach(() => {
        if (appManager) {
            appManager.destroy();
        }
    });
    
    test('should initialize all components', () => {
        expect(appManager.getComponent('progress')).toBeDefined();
        expect(appManager.getComponent('risk')).toBeDefined();
    });
    
    test('should handle WebSocket messages', (done) => {
        const progressComponent = appManager.getComponent('progress');
        const spy = jest.spyOn(progressComponent, 'onDataUpdate');
        
        // 模拟WebSocket消息
        appManager.dataManager.handleWebSocketMessage({
            type: 'stage_progress_update',
            data: { stage_number: 1 }
        });
        
        setTimeout(() => {
            expect(spy).toHaveBeenCalled();
            done();
        }, 100);
    });
});
```

---

## 5. 部署和维护规范

### 5.1 构建配置

#### Webpack配置示例
```javascript
// webpack.config.js
const path = require('path');

module.exports = {
    entry: {
        'unified-core': [
            './static/js/unified/http-client.js',
            './static/js/unified/data-manager.js',
            './static/js/unified/base-component.js',
            './static/js/unified/app-manager.js'
        ],
        'components': [
            './static/js/unified/components/progress-component.js',
            './static/js/unified/components/risk-component.js',
            // 其他组件...
        ]
    },
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: '[name].[contenthash].js'
    },
    optimization: {
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                unified: {
                    test: /[\\/]unified[\\/]/,
                    name: 'unified-core',
                    chunks: 'all'
                }
            }
        }
    }
};
```

### 5.2 版本管理

#### 组件版本控制
```javascript
// 在每个组件中添加版本信息
class ProgressComponent extends BaseComponent {
    static get VERSION() {
        return '1.2.0';
    }
    
    static get COMPATIBLE_VERSIONS() {
        return {
            dataManager: '^2.0.0',
            baseComponent: '^1.0.0'
        };
    }
}
```

#### 兼容性检查
```javascript
// 在应用初始化时检查版本兼容性
function checkCompatibility() {
    const components = [ProgressComponent, RiskComponent];
    
    components.forEach(ComponentClass => {
        if (ComponentClass.COMPATIBLE_VERSIONS) {
            // 检查版本兼容性
            console.log(`${ComponentClass.name} v${ComponentClass.VERSION} compatibility checked`);
        }
    });
}
```

### 5.3 性能监控

#### 性能指标收集
```javascript
// 在AppManager中添加性能监控
class AppManager {
    constructor(projectId, config) {
        super(projectId, config);
        this.performanceMetrics = {
            initTime: 0,
            componentLoadTimes: new Map(),
            dataFetchTimes: new Map()
        };
    }
    
    async _doInit() {
        const startTime = performance.now();
        
        await super._doInit();
        
        this.performanceMetrics.initTime = performance.now() - startTime;
        console.log(`App initialization took ${this.performanceMetrics.initTime}ms`);
    }
}
```

---

## 6. 故障排除指南

### 6.1 常见问题

#### 组件初始化失败
```javascript
// 问题：Container element not found
// 解决：检查HTML中是否存在对应的容器元素
const container = document.getElementById(containerId);
if (!container) {
    console.error(`Container ${containerId} not found. Available containers:`, 
        Array.from(document.querySelectorAll('[id]')).map(el => el.id));
}
```

#### WebSocket连接失败
```javascript
// 问题：WebSocket connection failed
// 解决：检查服务器状态和网络连接
if (this.wsClient.readyState === WebSocket.CLOSED) {
    console.error('WebSocket connection failed. Check server status and network connectivity.');
    // 尝试重连
    setTimeout(() => this.setupWebSocket(), 5000);
}
```

#### 数据更新不及时
```javascript
// 问题：组件没有响应数据更新
// 解决：检查数据订阅是否正确设置
const subscriptions = this.subscriptions.length;
const dataTypes = this.getDataTypes();
console.log(`Component has ${subscriptions} subscriptions for ${dataTypes.length} data types`);
```

### 6.2 调试检查清单

#### 应用级检查
- [ ] 项目ID是否正确
- [ ] 所有必需的脚本文件是否已加载
- [ ] WebSocket连接是否建立
- [ ] 全局错误处理是否设置

#### 组件级检查
- [ ] 容器元素是否存在
- [ ] 组件是否正确继承BaseComponent
- [ ] getDataTypes()方法是否正确实现
- [ ] render()方法是否正确实现
- [ ] 数据订阅是否正确设置

#### 数据级检查
- [ ] API端点是否正确
- [ ] 数据格式是否符合预期
- [ ] 缓存策略是否合理
- [ ] 错误处理是否完善

这套规范确保了统一架构的正确使用和高效维护！
