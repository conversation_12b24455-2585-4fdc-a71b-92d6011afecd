# PostgreSQL技术栈上下文

## 背景

PostgreSQL作为XKongCloud项目的核心数据库技术栈，承担着从Cassandra迁移的重要任务。PostgreSQL的选择基于其强大的ACID事务支持、丰富的数据类型、优秀的查询优化器以及良好的扩展性。

### 技术选型理由
- **ACID事务支持**：确保数据一致性和完整性
- **多Schema组织**：支持复杂的业务领域划分
- **丰富的数据类型**：支持JSON、数组等复杂数据结构
- **优秀的性能**：查询优化器和索引机制
- **成熟的生态**：与Spring Boot、JPA的良好集成

### 迁移策略
- 渐进式迁移，降低风险
- 保持数据一致性
- 优化查询性能
- 简化运维管理

本文档提供了在XKongCloud项目中使用PostgreSQL数据库的技术栈上下文信息，包括版本、配置、最佳实践和集成方式。

## 基础配置

### PostgreSQL版本与功能

XKongCloud项目使用PostgreSQL 17.4版本，选择该版本的主要原因包括：

1. **高性能**：支持高效的并行查询、复杂索引和优化的查询计划
2. **强大的事务支持**：完全符合ACID属性，支持复杂的事务操作
3. **丰富的数据类型**：支持JSON、数组、地理信息等高级数据类型
4. **强大的扩展能力**：支持自定义函数、存储过程和触发器

### 连接池配置

项目使用HikariCP作为数据库连接池，主要配置参数包括：

| 参数名 | 说明 | 推荐值 | 环境调整 |
|-------|------|-------|--------|
| `maximumPoolSize` | 连接池最大连接数 | 10 | 生产环境可根据负载调整到20-50 |
| `minimumIdle` | 连接池最小空闲连接数 | 5 | 生产环境可根据负载调整到10-20 |
| `connectionTimeout` | 连接超时时间(毫秒) | 30000 | 保持一致 |
| `idleTimeout` | 空闲连接超时时间(毫秒) | 600000 | 保持一致 |
| `maxLifetime` | 连接最大生存时间(毫秒) | 1800000 | 保持一致 |

连接池配置示例：

```java
@Bean
@Primary
public DataSource dataSource() {
    HikariConfig config = new HikariConfig();
    config.setJdbcUrl(getRequiredParam("postgresql.url"));
    config.setUsername(getRequiredParam("postgresql.username"));
    config.setPassword(getRequiredParam("postgresql.password"));
    config.setMaximumPoolSize(getIntParam("postgresql.pool.max-size"));
    config.setMinimumIdle(getIntParam("postgresql.pool.min-idle"));
    config.setConnectionTimeout(getLongParam("postgresql.pool.connection-timeout"));
    config.setIdleTimeout(getLongParam("postgresql.pool.idle-timeout"));
    config.setMaxLifetime(getLongParam("postgresql.pool.max-lifetime"));
    
    return new HikariDataSource(config);
}
```

### JPA和Hibernate配置

项目使用Spring Data JPA和Hibernate ORM框架，主要配置参数包括：

| 参数名 | 说明 | 开发环境值 | 生产环境值 |
|-------|------|-----------|-----------|
| `hibernate.ddl-auto` | DDL自动生成策略 | create | validate或none |
| `hibernate.dialect` | 数据库方言 | PostgreSQLDialect | PostgreSQLDialect |
| `hibernate.show_sql` | 是否显示SQL | true | false |
| `hibernate.format_sql` | 是否格式化SQL | true | false |
| `hibernate.jdbc.batch_size` | 批处理大小 | 30 | 30-50 |
| `hibernate.jdbc.fetch_size` | 查询结果集获取大小 | 100 | 100-500 |

JPA配置示例：

```java
@Bean
public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(dataSource());
    em.setPackagesToScan("com.xkongcloud.core.entity");
    
    JpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    em.setJpaVendorAdapter(vendorAdapter);
    
    Properties properties = new Properties();
    properties.setProperty("hibernate.hbm2ddl.auto", getRequiredParam("postgresql.ddl-auto"));
    properties.setProperty("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
    properties.setProperty("hibernate.show_sql", getParam("postgresql.show-sql", "false"));
    properties.setProperty("hibernate.format_sql", getParam("postgresql.format-sql", "false"));
    properties.setProperty("hibernate.jdbc.batch_size", getParam("postgresql.batch-size", "30"));
    properties.setProperty("hibernate.jdbc.fetch_size", getParam("postgresql.fetch-size", "100"));
    properties.setProperty("hibernate.physical_naming_strategy", "com.xkongcloud.core.config.PostgreSQLNamingStrategy");
    
    em.setJpaProperties(properties);
    
    return em;
}
```

## Schema设计与命名规范

### Schema组织方式

项目采用多Schema组织方式，按照业务领域和功能类型划分Schema，主要包括：

1. **业务Schema**：按照`<业务领域>_<可选子域>`格式命名
   - 例如：`user_management`、`identity_core`、`payment_processing`

2. **基础设施Schema**：按照`infra_<组件类型>`格式命名
   - 例如：`infra_uid`、`infra_audit`、`infra_cache`

3. **通用功能Schema**：按照`common_<功能类型>`格式命名
   - 例如：`common_config`、`common_logging`、`common_security`

### 命名策略

项目使用自定义的命名策略，实现Java驼峰命名到PostgreSQL下划线命名的转换：

```java
public class PostgreSQLNamingStrategy implements PhysicalNamingStrategy {
    @Override
    public Identifier toPhysicalTableName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return convertToSnakeCase(name);
    }

    @Override
    public Identifier toPhysicalColumnName(Identifier name, JdbcEnvironment jdbcEnvironment) {
        return convertToSnakeCase(name);
    }

    private Identifier convertToSnakeCase(Identifier identifier) {
        if (identifier == null) {
            return null;
        }
        
        String name = identifier.getText();
        String snakeCase = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);
        return Identifier.toIdentifier(snakeCase, identifier.isQuoted());
    }

    // 其他必需的方法实现...
}
```

## 数据访问模式

### Repository模式

项目使用Spring Data JPA的Repository模式进行数据访问，主要包括：

1. **CrudRepository**：提供基本的CRUD操作
2. **JpaRepository**：扩展CrudRepository，提供分页和排序功能
3. **自定义Repository**：通过@Query注解或方法名约定实现自定义查询

示例：

```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    // 通过方法名约定查询
    Optional<User> findByUsername(String username);
    
    // 通过@Query注解自定义查询
    @Query("SELECT u FROM User u WHERE u.email = :email AND u.status = :status")
    List<User> findByEmailAndStatus(@Param("email") String email, @Param("status") UserStatus status);
    
    // 原生SQL查询
    @Query(value = "SELECT * FROM user_management.user WHERE regist_time > :date", nativeQuery = true)
    List<User> findUsersRegisteredAfter(@Param("date") LocalDateTime date);
}
```

### 事务管理

项目使用Spring的声明式事务管理，通过@Transactional注解控制事务：

```java
@Service
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    @Transactional
    public User createUser(User user) {
        // 业务逻辑
        return userRepository.save(user);
    }
    
    @Transactional(readOnly = true)
    public User getUserById(Long id) {
        return userRepository.findById(id).orElseThrow(() -> new EntityNotFoundException("User not found"));
    }
    
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public void transferCredits(Long fromUserId, Long toUserId, int amount) {
        // 高隔离级别的事务操作
    }
}
```

### 批量操作

项目使用JPA和JdbcTemplate的批量操作功能提高性能：

```java
@Service
public class BulkOperationService {
    @Autowired
    private EntityManager entityManager;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Transactional
    public void bulkInsertUsers(List<User> users) {
        for (int i = 0; i < users.size(); i++) {
            entityManager.persist(users.get(i));
            
            // 每30条数据清理一次缓存
            if (i % 30 == 0) {
                entityManager.flush();
                entityManager.clear();
            }
        }
    }
    
    @Transactional
    public void bulkUpdateStatus(List<Long> userIds, UserStatus status) {
        jdbcTemplate.batchUpdate(
            "UPDATE user_management.user SET status = ? WHERE user_id = ?",
            new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    ps.setString(1, status.name());
                    ps.setLong(2, userIds.get(i));
                }
                
                @Override
                public int getBatchSize() {
                    return userIds.size();
                }
            });
    }
}
```

## 实体类设计模式

### 基础实体类结构

项目中的实体类遵循以下基本结构：

```java
@Entity
@Table(name = "user", schema = "user_management")
public class User {
    @Id
    private Long userId;
    
    @Column(name = "username", nullable = false, length = 64)
    private String username;
    
    @Column(name = "email", nullable = false, length = 128)
    private String email;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status;
    
    @Column(name = "regist_time", nullable = false)
    private LocalDateTime registTime;
    
    // Getter和Setter方法...
}
```

### 关系映射模式

项目中常用的关系映射模式包括：

1. **一对多关系**：
   ```java
   // 一方
   @Entity
   @Table(name = "department", schema = "user_management")
   public class Department {
       @Id
       private Long departmentId;
       
       @Column(name = "name", nullable = false)
       private String name;
       
       @OneToMany(mappedBy = "department", cascade = CascadeType.ALL, orphanRemoval = true)
       private List<Employee> employees = new ArrayList<>();
       
       // 其他字段和方法...
   }
   
   // 多方
   @Entity
   @Table(name = "employee", schema = "user_management")
   public class Employee {
       @Id
       private Long employeeId;
       
       @Column(name = "name", nullable = false)
       private String name;
       
       @ManyToOne(fetch = FetchType.LAZY)
       @JoinColumn(name = "department_id")
       private Department department;
       
       // 其他字段和方法...
   }
   ```

2. **多对多关系**：
   ```java
   // 一方
   @Entity
   @Table(name = "user", schema = "user_management")
   public class User {
       @Id
       private Long userId;
       
       // 其他字段...
       
       @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
       @JoinTable(
           name = "user_role",
           schema = "user_management",
           joinColumns = @JoinColumn(name = "user_id"),
           inverseJoinColumns = @JoinColumn(name = "role_id")
       )
       private Set<Role> roles = new HashSet<>();
       
       // 方法...
   }
   
   // 另一方
   @Entity
   @Table(name = "role", schema = "user_management")
   public class Role {
       @Id
       private Long roleId;
       
       @Column(name = "name", nullable = false)
       private String name;
       
       @ManyToMany(mappedBy = "roles")
       private Set<User> users = new HashSet<>();
       
       // 方法...
   }
   ```

### 实体类验证模式

项目使用Bean Validation (JSR-380)进行实体类验证：

```java
@Entity
@Table(name = "user", schema = "user_management")
public class User {
    @Id
    private Long userId;
    
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 64, message = "用户名长度必须在3到64个字符之间")
    @Column(name = "username", nullable = false, length = 64)
    private String username;
    
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Column(name = "email", nullable = false, length = 128)
    private String email;
    
    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private UserStatus status;
    
    // 其他字段和方法...
}
```

## 索引和性能优化

### 索引策略

项目使用以下索引策略提高查询性能：

1. **单列索引**：针对常用查询条件的单列创建索引
   ```java
   @Entity
   @Table(
       name = "user", 
       schema = "user_management",
       indexes = {
           @Index(name = "idx_user_username", columnList = "username"),
           @Index(name = "idx_user_email", columnList = "email"),
           @Index(name = "idx_user_status", columnList = "status")
       }
   )
   public class User {
       // 字段和方法...
   }
   ```

2. **复合索引**：针对常用的组合查询条件创建复合索引
   ```java
   @Entity
   @Table(
       name = "order", 
       schema = "order_management",
       indexes = {
           @Index(name = "idx_order_user_status", columnList = "user_id, status"),
           @Index(name = "idx_order_created_status", columnList = "created_time, status")
       }
   )
   public class Order {
       // 字段和方法...
   }
   ```

### 查询优化

项目采用以下查询优化技术：

1. **预编译语句**：使用预编译语句减少SQL解析开销
2. **分页查询**：使用Pageable接口进行分页查询，避免大结果集返回
3. **投影查询**：只查询需要的字段，减少数据传输量
4. **批量操作**：使用批量插入和更新，减少数据库往返次数

示例：

```java
// 分页查询
Page<User> findByStatus(UserStatus status, Pageable pageable);

// 投影查询
@Query("SELECT new com.xkongcloud.core.dto.UserSummary(u.userId, u.username, u.email) FROM User u WHERE u.status = :status")
List<UserSummary> findUserSummariesByStatus(@Param("status") UserStatus status);
```

### 缓存策略

项目使用多级缓存提高性能：

1. **JPA二级缓存**：使用EhCache作为JPA二级缓存
   ```java
   @Entity
   @Table(name = "product", schema = "product_management")
   @Cacheable
   @org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
   public class Product {
       // 字段和方法...
   }
   ```

2. **查询结果缓存**：缓存常用查询结果
   ```java
   @QueryHints({@QueryHint(name = "org.hibernate.cacheable", value = "true")})
   List<Product> findByCategory(String category);
   ```

3. **应用级缓存**：使用Spring Cache抽象

## 最佳实践与注意事项

### 强制约束

1. **实体类Schema指定**：所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
2. **Schema命名规范**：严格遵循Schema命名规范
3. **参数验证**：所有从KV参数服务获取的参数必须进行验证
4. **错误处理**：所有数据库操作必须有适当的错误处理机制

### 性能注意事项

1. **N+1查询问题**：通过适当的关联抓取策略避免N+1查询问题
2. **大事务**：避免长时间运行的大事务，可能导致锁竞争
3. **连接池设置**：根据实际负载调整连接池参数
4. **批量操作**：大量数据操作时使用批量插入、更新或删除

### 安全注意事项

1. **参数化查询**：使用参数化查询避免SQL注入
2. **最小权限原则**：数据库用户仅分配必要的权限
3. **敏感数据加密**：敏感数据应进行加密存储
4. **审计日志**：重要操作应记录审计日志

### 迁移注意事项

1. **Schema兼容性**：确保Schema变更不破坏向后兼容性
2. **数据迁移**：大规模数据迁移应分批进行
3. **回滚机制**：提供回滚机制，在迁移失败时可以恢复
4. **验证机制**：数据迁移后进行数据完整性和一致性验证

## 常见问题和解决方案

### 连接问题
1. **连接超时**
   - 问题：数据库连接超时
   - 解决方案：检查网络连接，调整连接超时参数，验证数据库服务状态

2. **连接池耗尽**
   - 问题：连接池中没有可用连接
   - 解决方案：增加连接池大小，检查连接泄漏，优化长时间运行的查询

### 性能问题
1. **查询慢**
   - 问题：查询执行时间过长
   - 解决方案：添加适当索引，优化查询语句，使用EXPLAIN分析执行计划

2. **N+1查询**
   - 问题：关联查询产生大量SQL语句
   - 解决方案：使用@EntityGraph或JOIN FETCH，调整抓取策略

### 配置问题
1. **Schema不存在**
   - 问题：应用启动时报告Schema不存在
   - 解决方案：手动创建Schema，检查数据库权限，验证Schema名称拼写

2. **DDL策略错误**
   - 问题：生产环境使用了create或update策略
   - 解决方案：设置为validate或none，通过数据库迁移工具管理Schema变更