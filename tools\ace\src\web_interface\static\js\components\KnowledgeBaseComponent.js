/**
 * 项目知识库 (KnowledgeBaseComponent)
 * 
 * 功能：
 * - 可视化项目中的原子约束及其关系。
 * - 支持点击约束节点以查看详情。
 * - 提供帮助弹窗解释图例。
 * 
 * 数据依赖：
 * - knowledge_graph: 包含知识图谱的节点和连接数据。
 */
class KnowledgeBaseComponent extends BaseComponent {
    getDataTypes() {
        return ['knowledge_graph'];
    }

    render() {
        const graphData = this.getData('knowledge_graph') || { nodes: [], connections: [] };

        this.container.innerHTML = `
            <div class="area-title">
                项目知识库 (Knowledge Base)
                <span class="help-icon" title="点击查看图例说明">
                    <span class="help-symbol">?</span>
                </span>
            </div>
            <div class="area-content">
                <div class="knowledge-graph" id="knowledge-graph">
                    ${graphData.nodes.map(node => this.renderNode(node)).join('')}
                    ${graphData.connections.map(conn => this.renderConnection(conn, graphData.nodes)).join('')}
                    <div class="node-tooltip" id="node-tooltip"></div>
                </div>
                ${this.renderLegend()}
            </div>
        `;

        // 将弹窗渲染到body层级，避免跟随区域滚动
        this.renderHelpPopupToBody();
    }

    renderHelpPopupToBody() {
        // 检查是否已存在弹窗，避免重复创建
        let existingPopup = document.getElementById('knowledge-help-popup');
        if (existingPopup) {
            existingPopup.remove();
        }

        // 创建弹窗并添加到body
        const popupHTML = this.renderHelpPopup();
        document.body.insertAdjacentHTML('beforeend', popupHTML);
    }
    
    renderNode(node) {
        const categoryClass = (node.category || 'constraint').toLowerCase().replace(/_/g, '-');
        const forkedClass = node.is_forked ? 'forked' : '';
        return `
            <div class="constraint-node ${categoryClass} ${forkedClass}" 
                 style="top: ${node.y}px; left: ${node.x}px;"
                 data-id="${node.id}"
                 data-category="${node.category}"
                 title="${node.id}: ${node.description}">
                ${node.id}
                <div class="node-label">${node.label || node.category}</div>
                ${node.is_forked ? `<div class="fork-indicator">↗ ${node.forked_from}</div>` : ''}
            </div>
        `;
    }

    renderConnection(conn, nodes) {
        const fromNode = nodes.find(n => n.id === conn.from);
        const toNode = nodes.find(n => n.id === conn.to);
        if (!fromNode || !toNode) return '';

        const x1 = fromNode.x + 25;
        const y1 = fromNode.y + 25;
        const x2 = toNode.x + 25;
        const y2 = toNode.y + 25;

        const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
        const connClass = conn.type === 'fork' ? 'fork' : 'reference';

        return `<div class="constraint-connection ${connClass}" style="top: ${y1}px; left: ${x1}px; width: ${length}px; transform: rotate(${angle}deg);"></div>`;
    }
    
    renderLegend() {
        return `
            <div style="margin-top: 0.8rem; font-size: 0.7rem;">
                <div style="font-weight: bold; margin-bottom: 0.5rem; color: #0078D4;">AtomicConstraint类型:</div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #FF9800; margin-right: 0.3rem;">●</span>
                    <span>boundary_condition</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #4CAF50; margin-right: 0.3rem;">●</span>
                    <span>constraint</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #F44336; margin-right: 0.3rem;">●</span>
                    <span>guardrail</span>
                </div>
                <div style="margin-bottom: 0.2rem; display: flex; align-items: center;">
                    <span style="color: #9C27B0; margin-right: 0.3rem;">●</span>
                    <span>state_machine</span>
                </div>
            </div>
        `;
    }

    renderHelpPopup() {
        return `
            <div class="knowledge-help-popup" id="knowledge-help-popup">
                <button class="help-close-btn">×</button>
                <div class="help-popup-title"><span>📚</span><span>知识库图例</span></div>

                <div class="help-popup-section">
                    <div class="help-section-title">约束节点类型</div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol" style="color: #4CAF50;">●</span>
                        <span class="help-legend-text">constraint - 基础约束</span>
                    </div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol" style="color: #FF9800;">●</span>
                        <span class="help-legend-text">boundary_condition - 边界条件</span>
                    </div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol" style="color: #F44336;">●</span>
                        <span class="help-legend-text">guardrail - 护栏约束</span>
                    </div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol" style="color: #9C27B0;">●</span>
                        <span class="help-legend-text">state_machine - 状态机</span>
                    </div>
                </div>

                <div class="help-popup-section">
                    <div class="help-section-title">操作说明</div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol">🖱️</span>
                        <span class="help-legend-text">点击节点查看详细约束信息</span>
                    </div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol">↗</span>
                        <span class="help-legend-text">箭头表示约束派生关系</span>
                    </div>
                    <div class="help-legend-item">
                        <span class="help-legend-symbol">📋</span>
                        <span class="help-legend-text">详情将显示在约束审查区域</span>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        const graph = this.container.querySelector('#knowledge-graph');
        const helpIcon = this.container.querySelector('.help-icon');

        if (graph) {
            graph.addEventListener('click', (e) => {
                const node = e.target.closest('.constraint-node');
                if (node) {
                    const constraintId = node.dataset.id;
                    this.showConstraintDetail(constraintId);
                }
            });
        }

        if (helpIcon) {
            helpIcon.addEventListener('click', (event) => {
                event.stopPropagation();
                const helpPopup = document.getElementById('knowledge-help-popup');
                this.showKnowledgeBaseHelp(event, helpPopup);
            });
        }

        // 绑定关闭按钮事件（弹窗在body层级）
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('help-close-btn')) {
                const helpPopup = document.getElementById('knowledge-help-popup');
                if (helpPopup) {
                    helpPopup.classList.remove('show');
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            }
        });
    }

    showKnowledgeBaseHelp(event, popup) {
        const helpIcon = event.currentTarget;

        if (!popup || !helpIcon) return;

        // 获取弹窗实际高度
        popup.style.visibility = 'hidden';
        popup.style.display = 'block';
        const popupHeight = popup.offsetHeight;
        popup.style.visibility = '';
        popup.style.display = '';

        // 计算弹窗位置 - 使用页面坐标，弹窗在body层级
        const iconRect = helpIcon.getBoundingClientRect();
        
        // 设置弹窗位置（相对于页面定位）
        popup.style.position = 'fixed';

        // 计算理想的top位置（在按钮上方5px间距）
        const idealTop = iconRect.top - popupHeight - 5;

        // 智能边界约束：优先显示在问号上方，空间不足时显示在下方
        let finalTop;
        if (idealTop >= 10) {
            // 空间足够，显示在问号上方
            finalTop = idealTop;
        } else {
            // 空间不足，显示在问号下方
            finalTop = iconRect.bottom + 5;
        }

        // 计算水平位置，确保弹窗居中对齐问号按钮
        const popupWidth = 320; // 弹窗的固定宽度
        const idealLeft = iconRect.left - (popupWidth / 2) + (iconRect.width / 2);
        const finalLeft = Math.max(10, Math.min(idealLeft, window.innerWidth - popupWidth - 10));

        popup.style.top = `${finalTop}px`;
        popup.style.left = `${finalLeft}px`;
        popup.style.right = 'auto';
        popup.style.bottom = 'auto';

        // 显示弹窗
        popup.classList.add('show');

        // 添加全局点击监听器来关闭弹窗
        this.handleOutsideClick = (e) => {
            if (!popup.contains(e.target) && !helpIcon.contains(e.target)) {
                popup.classList.remove('show');
                document.removeEventListener('click', this.handleOutsideClick);
            }
        };

        setTimeout(() => {
            document.addEventListener('click', this.handleOutsideClick);
        }, 100);

    }

    showConstraintDetail(constraintId) {
        const constraintReviewComponent = this.appManager.getComponent('constraint-area');
        if (constraintReviewComponent) {
            const graphData = this.getData('knowledge_graph') || { nodes: [] };
            const nodeData = graphData.nodes.find(n => n.id === constraintId);
            if (nodeData) {
                // 假设节点数据本身就包含足够的详情
                constraintReviewComponent.displayConstraint(nodeData);
            }
        }
    }
}