# 数据库操作相关任务上下文

## 背景

XKongCloud项目正在进行从Cassandra到PostgreSQL的数据库迁移，这是一个重大的技术架构升级。迁移的目标是提高数据一致性、查询灵活性和开发效率，同时为未来的微服务架构演进做准备。

主要技术挑战包括：数据模型从NoSQL到关系型数据库的转换、实体类映射和关系设计的重新构建、UID生成器从内存模式到持久化模式的升级、多Schema架构的规划和实施、配置驱动架构的实现。这个迁移确保业务连续性的同时实现技术架构的平滑升级，为后续功能开发提供更强大的数据支持。

本文档提供了在XKongCloud项目中执行数据库操作相关任务的上下文信息，特别是PostgreSQL迁移和配置相关的任务。

## PostgreSQL配置任务

### 环境准备
- PostgreSQL 17.4已安装
- 数据库xkong_main_db已创建
- pgAdmin 4管理工具已配置
- 用户xkong_user已创建并配置了适当权限
- 必要的Schema已创建：user_management、common_config、infra_uid

### 配置类创建
在xkongcloud-business-internal-core项目中，需要创建PostgreSQLConfig类，负责配置数据源、实体管理器工厂和事务管理器。主要步骤包括：

1. **数据源配置**
   - 从KVParamService获取PostgreSQL连接参数
   - 使用HikariCP作为连接池实现
   - 配置连接池参数，如最大连接数、最小空闲连接数、连接超时时间等

2. **实体管理器工厂配置**
   - 配置JPA实体类的扫描路径
   - 配置Hibernate方言和其他JPA参数
   - 配置命名策略，确保表名和列名符合PostgreSQL的命名约定

3. **事务管理器配置**
   - 配置JpaTransactionManager
   - 配置TransactionTemplate，支持编程式事务管理

4. **JdbcTemplate配置**
   - 配置JdbcTemplate，支持原生SQL查询
   - 用于xkongcloud-commons-uid公共库的表管理

5. **命名策略配置**
   - 创建PostgreSQLNamingStrategy类，实现PhysicalNamingStrategy接口
   - 将Java驼峰命名转换为PostgreSQL下划线命名

6. **其他辅助Bean配置**
   - 配置PersistenceExceptionTranslationPostProcessor，支持异常转换
   - 获取和验证ddl-auto参数

### Schema管理任务

Schema管理是PostgreSQL迁移的重要部分，需要按照规范创建和管理Schema。主要任务包括：

1. **Schema创建**
   - 创建业务Schema，如user_management
   - 创建基础设施Schema，如infra_uid
   - 创建通用功能Schema，如common_config

2. **Schema权限配置**
   - 为xkong_user用户授予Schema的所有权
   - 配置适当的访问权限

3. **Schema验证**
   - 在应用启动时验证Schema是否存在
   - 如果验证失败，应用应无法启动

4. **Schema演进管理**
   - 使用PostgreSQLSchemaEvolutionManager管理Schema版本
   - 支持Schema结构的升级

### 实体类映射任务

实体类映射是PostgreSQL迁移的核心任务，需要将原有的Cassandra实体类重新映射为JPA实体类。主要任务包括：

1. **实体类设计**
   - 使用@Entity、@Table、@Column等JPA注解标注实体类
   - 明确指定Schema：`@Table(name = "表名", schema = "schema名")`
   - 使用适当的ID生成策略

2. **关系映射**
   - 使用@OneToMany、@ManyToOne、@ManyToMany等注解映射实体之间的关系
   - 配置级联操作和懒加载策略

3. **字段类型映射**
   - 将Cassandra数据类型映射为PostgreSQL数据类型
   - 使用适当的长度、精度等约束

4. **索引和约束**
   - 使用@Index配置索引
   - 使用@UniqueConstraint配置唯一约束
   - 配置外键约束（如适用）

## UID生成器配置任务

UID生成器配置是PostgreSQL迁移中的重要部分，需要使用门面模式集成xkongcloud-commons-uid公共库。主要任务包括：

1. **UidGeneratorConfig类创建**
   - 使用UidGeneratorFacade门面模式配置UID生成器
   - 从KVParamService获取UID生成器所需的配置参数
   - 通过UidGeneratorFacadeBuilder构建UidGeneratorFacade实例

2. **UidGenerator适配器创建**
   - 创建UidGenerator适配器Bean，兼容现有代码
   - 适配门面类到百度UidGenerator接口

3. **资源关闭配置**
   - 配置关闭顺序，确保UID库在数据库连接池之前关闭
   - 使用DisposableBean和@PreDestroy注解确保资源正确关闭

4. **参数验证**
   - 验证必需的UID生成器参数是否存在
   - 验证参数值是否有效

## 常见问题和解决方案

在执行数据库操作相关任务时，可能遇到以下常见问题：

1. **数据库连接问题**
   - 检查PostgreSQL服务是否运行
   - 检查连接参数是否正确
   - 检查网络连接是否正常
   - 检查防火墙设置是否允许连接

2. **Schema验证失败**
   - 检查Schema是否存在
   - 检查用户是否有权限访问Schema
   - 手动创建缺失的Schema

3. **实体类映射问题**
   - 检查实体类是否明确指定Schema
   - 检查字段类型映射是否正确
   - 检查关系映射是否正确配置

4. **UID生成器问题**
   - 检查UID生成器参数是否正确配置
   - 检查数据源是否正确传递给UidGeneratorFacade
   - 检查资源关闭顺序是否正确

5. **性能问题**
   - 调整连接池参数
   - 优化JPA配置
   - 添加适当的索引
   - 使用批处理操作 