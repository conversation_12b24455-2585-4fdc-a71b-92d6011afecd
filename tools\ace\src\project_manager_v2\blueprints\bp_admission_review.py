# -*- coding: utf-8 -*-
"""
项目准入审查蓝图 - 实现动态配置的阻塞式验证策略
"""

import os
import json
from typing import Tuple, Dict, Any, Optional, List, Callable
from datetime import datetime
from dataclasses import dataclass
from abc import ABC, abstractmethod

@dataclass
class ReviewStage:
    """审查阶段定义"""
    name: str
    description: str
    order: int
    is_blocking: bool = True
    validator_class: Optional[str] = None  # 验证器类名，用于动态加载

@dataclass
class ReviewResult:
    """审查结果"""
    stage_name: str
    is_blocking: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now().isoformat()

class StageValidator(ABC):
    """审查阶段验证器基类"""
    
    @abstractmethod
    def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
        """执行验证逻辑"""
        pass

class AssetInventoryValidator(StageValidator):
    """阶段1：基础资产盘点验证器"""
    
    def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
        # 占位实现
        return ReviewResult(
            stage_name="Asset Inventory",
            is_blocking=False,
            message="基础资产盘点验证通过",
            details={"validated_path": design_doc_path}
        )

class SyntaxValidationValidator(StageValidator):
    """阶段2：核心模块语法验证器"""
    
    def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
        # 占位实现
        return ReviewResult(
            stage_name="Module Syntax Validation",
            is_blocking=False,
            message="核心模块语法验证通过",
            details={"validated_path": design_doc_path}
        )

class ArchitectureAssessmentValidator(StageValidator):
    """阶段3：宏观架构健康度评估验证器"""
    
    def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
        # 占位实现
        return ReviewResult(
            stage_name="Holistic Architecture Assessment",
            is_blocking=False,
            message="宏观架构健康度评估通过",
            details={"validated_path": design_doc_path}
        )

class CompletenessAuditValidator(StageValidator):
    """阶段4：设计完备性与一致性审计验证器"""
    
    def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
        # 占位实现
        return ReviewResult(
            stage_name="Design Completeness & Consistency Audit",
            is_blocking=False,
            message="设计完备性与一致性审计通过",
            details={"validated_path": design_doc_path}
        )

class AdmissionReviewBlueprint:
    """
    项目准入审查蓝图 - 实现动态配置的阻塞式验证策略
    
    核心原则：
    1. 支持动态配置审查阶段，而非硬编码
    2. 所有验证器仅负责检测问题并返回布尔对 (is_blocking: bool, message: str)
    3. 调度器根据布尔值统一决定流程是否终止
    4. 实时流式推送日志（每个验证器输出立即推送，无需缓存）
    """
    
    def __init__(self, stages_config: Optional[List[ReviewStage]] = None):
        """
        初始化审查蓝图
        
        Args:
            stages_config: 自定义的审查阶段配置，如果为None则使用默认配置
        """
        self.stages = stages_config or self._get_default_stages()
        self.current_stage = None
        self.results = []
        self._validators = self._initialize_validators()
        
    def _get_default_stages(self) -> List[ReviewStage]:
        """获取默认的审查阶段配置"""
        return [
            ReviewStage(
                name="Asset Inventory",
                description="基础资产盘点 - 确认项目具备执行审查所需的最基础物理资产",
                order=1,
                validator_class="AssetInventoryValidator"
            ),
            ReviewStage(
                name="Module Syntax Validation", 
                description="核心模块语法验证 - 确保核心设计模块的语法正确",
                order=2,
                validator_class="SyntaxValidationValidator"
            ),
            ReviewStage(
                name="Holistic Architecture Assessment",
                description="宏观架构健康度评估 - 由AI扮演首席架构师评估整体健全性",
                order=3,
                validator_class="ArchitectureAssessmentValidator"
            ),
            ReviewStage(
                name="Design Completeness & Consistency Audit",
                description="设计完备性与一致性审计 - 审计所有微观设计细节",
                order=4,
                validator_class="CompletenessAuditValidator"
            )
        ]
    
    def _initialize_validators(self) -> Dict[str, StageValidator]:
        """初始化验证器映射"""
        validator_map = {
            "AssetInventoryValidator": AssetInventoryValidator(),
            "SyntaxValidationValidator": SyntaxValidationValidator(),
            "ArchitectureAssessmentValidator": ArchitectureAssessmentValidator(),
            "CompletenessAuditValidator": CompletenessAuditValidator(),
        }
        
        validators = {}
        for stage in self.stages:
            if stage.validator_class and stage.validator_class in validator_map:
                validators[stage.name] = validator_map[stage.validator_class]
            else:
                # 如果没有指定验证器类，使用默认验证器
                validators[stage.name] = self._create_default_validator(stage)
        
        return validators
    
    def _create_default_validator(self, stage: ReviewStage) -> StageValidator:
        """创建默认验证器"""
        class DefaultValidator(StageValidator):
            def __init__(self, stage_info: ReviewStage):
                self.stage_info = stage_info
            
            def validate(self, design_doc_path: str, callback: Optional[Callable] = None) -> ReviewResult:
                return ReviewResult(
                    stage_name=self.stage_info.name,
                    is_blocking=False,
                    message=f"{self.stage_info.name} 验证通过（默认实现）",
                    details={"stage": self.stage_info.__dict__}
                )
        
        return DefaultValidator(stage)
    
    def add_stage(self, stage: ReviewStage, validator: Optional[StageValidator] = None):
        """动态添加审查阶段"""
        self.stages.append(stage)
        if validator:
            self._validators[stage.name] = validator
        else:
            self._validators[stage.name] = self._create_default_validator(stage)
        
        # 重新排序
        self.stages.sort(key=lambda x: x.order)
    
    def remove_stage(self, stage_name: str):
        """移除审查阶段"""
        self.stages = [s for s in self.stages if s.name != stage_name]
        if stage_name in self._validators:
            del self._validators[stage_name]
    
    def get_stages(self) -> List[ReviewStage]:
        """获取所有审查阶段"""
        return self.stages
    
    def get_stage_config(self) -> List[Dict[str, Any]]:
        """获取阶段配置，用于状态初始化"""
        return [
            {
                "name": stage.name,
                "description": stage.description,
                "order": stage.order,
                "is_blocking": stage.is_blocking,
                "validator_class": stage.validator_class
            }
            for stage in self.stages
        ]
    
    def run_review(self, design_doc_path: str, callback=None) -> Tuple[bool, List[ReviewResult]]:
        """
        执行完整的审查流程
        
        Args:
            design_doc_path: 设计文档路径
            callback: 进度回调函数，用于实时推送进度
            
        Returns:
            (success: bool, results: List[ReviewResult])
        """
        self.results = []
        self.current_stage = None
        
        try:
            for stage in self.stages:
                self.current_stage = stage
                
                # 通知开始当前阶段
                if callback:
                    callback("stage_started", {
                        "stage_name": stage.name,
                        "stage_order": stage.order,
                        "description": stage.description
                    })
                
                # 执行阶段验证
                validator = self._validators.get(stage.name)
                if validator:
                    result = validator.validate(design_doc_path, callback)
                else:
                    result = ReviewResult(
                        stage_name=stage.name,
                        is_blocking=True,
                        message=f"未找到阶段 {stage.name} 的验证器"
                    )
                
                self.results.append(result)
                
                # 通知阶段完成
                if callback:
                    callback("stage_completed", {
                        "stage_name": stage.name,
                        "result": result.__dict__
                    })
                
                # 检查是否为阻塞性失败
                if result.is_blocking and not self._is_stage_success(result):
                    # 通知审查失败
                    if callback:
                        callback("review_failed", {
                            "failed_stage": stage.name,
                            "message": result.message,
                            "results": [r.__dict__ for r in self.results]
                        })
                    return False, self.results
            
            # 所有阶段通过
            if callback:
                callback("review_completed", {
                    "results": [r.__dict__ for r in self.results]
                })
            return True, self.results
            
        except Exception as e:
            error_result = ReviewResult(
                stage_name=self.current_stage.name if self.current_stage else "Unknown",
                is_blocking=True,
                message=f"审查过程中发生异常: {str(e)}",
                details={"exception": str(e)}
            )
            self.results.append(error_result)
            
            if callback:
                callback("review_failed", {
                    "failed_stage": error_result.stage_name,
                    "message": error_result.message,
                    "results": [r.__dict__ for r in self.results]
                })
            return False, self.results
    
    def _is_stage_success(self, result: ReviewResult) -> bool:
        """判断阶段是否成功"""
        # 实际实现可能需要更复杂的成功判断逻辑
        return not result.is_blocking
    
    def get_current_status(self) -> Dict[str, Any]:
        """获取当前审查状态"""
        return {
            "current_stage": self.current_stage.name if self.current_stage else None,
            "completed_stages": len(self.results),
            "total_stages": len(self.stages),
            "results": [r.__dict__ for r in self.results]
        } 