/**
 * 项目交付结果 (ProjectOutputComponent)
 *
 * 功能：
 * - 显示整体性审计的状态。
 * - 提供最终产出物的下载链接。
 * - 展示项目完成后的各项处理统计。
 * - 支持任务驱动工作流的审查结果显示。
 * - 显示四阶段审查的完成状态和产出物。
 *
 * 数据依赖：
 * - deliverables: 包含审计状态、产出链接和最终统计数据。
 * - admission_review: 包含审查任务的完成状态和结果。
 * - workspace_status: 包含工作区状态信息。
 */
class ProjectOutputComponent extends BaseComponent {
    constructor(containerId, dataManager) {
        super(containerId, dataManager);
        this.workspaceStatus = 'idle';
        this.reviewResults = null;
    }

    getDataTypes() {
        return ['deliverables', 'admission_review', 'workspace_status'];
    }

    render() {
        const deliverables = this.getData('deliverables') || {};
        const admissionReviewData = this.getData('admission_review') || {};
        const workspaceStatusData = this.getData('workspace_status') || {};

        // 更新内部状态
        this.workspaceStatus = workspaceStatusData.status || this.workspaceStatus;
        this.reviewResults = admissionReviewData.results || this.reviewResults;

        // 根据工作区状态确定审计状态
        const auditStatus = this.getAuditStatus(deliverables, admissionReviewData);
        const statusClass = auditStatus.toLowerCase();

        this.container.innerHTML = `
            <div class="area-title">项目交付结果 (Project Output)</div>
            <div class="area-content">
                <!-- 审查状态指示器 -->
                ${this.renderReviewStatus()}

                <!-- 整体性审计状态 -->
                <div class="output-status">
                    <div class="output-badge ${statusClass}" id="audit-status">${this.getAuditStatusText(auditStatus, admissionReviewData)}</div>
                </div>

                <!-- 产出链接 -->
                <div class="output-links" id="output-links" style="${statusClass === 'success' ? 'display: block;' : 'display: none;'}">
                    ${(deliverables.links || []).map(link => `<a href="${link.url}" class="output-link">${link.text}</a>`).join('')}
                </div>

                <!-- 完成统计 -->
                <div style="margin-top: 1rem; font-size: 0.8rem;">
                    <div style="margin-bottom: 0.5rem; font-weight: bold;">处理统计:</div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>文档数量</span>
                            <span style="color: #2196F3; font-weight: bold;" id="processed-docs">${deliverables.stats?.processed_docs || '3/5'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill info" style="width: 60%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>约束数量</span>
                            <span style="color: #4CAF50; font-weight: bold;" id="total-constraints">${deliverables.stats?.total_constraints || '25'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill success" style="width: 83%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>风险数量</span>
                            <span style="color: #FF9800; font-weight: bold;" id="total-risks">${deliverables.stats?.total_risks || '2'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill warning" style="width: 40%;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 0.5rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                            <span>处理时间</span>
                            <span style="color: #9C27B0; font-weight: bold;" id="total-time">${deliverables.stats?.total_time || '150秒'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%; background: linear-gradient(90deg, #9C27B0, #BA68C8);"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 事件委托处理动态生成的链接
        this.container.addEventListener('click', (e) => {
            if (e.target.classList.contains('output-link')) {
                e.preventDefault();
                console.log(`产出链接被点击: ${e.target.href}`);
                // 可以在此触发下载或导航事件
                this.triggerEvent('deliverable-clicked', { url: e.target.href, text: e.target.textContent });
            }
        });

        // 监听审查完成相关事件
        document.addEventListener('pm_v2_data_update', (event) => {
            const { eventType, data } = event.detail;
            switch (eventType) {
                case 'review_completed':
                    this.handleReviewCompleted(data);
                    break;
                case 'review_stage_completed':
                    this.handleStageCompleted(data);
                    break;
            }
        });
    }

    // 审查状态渲染
    renderReviewStatus() {
        if (this.workspaceStatus !== 'completed' && this.workspaceStatus !== 'reviewing') {
            return '';
        }

        const statusColors = {
            'reviewing': '#2196F3',
            'completed': '#4CAF50',
            'failed': '#F44336'
        };

        const statusTexts = {
            'reviewing': '审查进行中',
            'completed': '审查已完成',
            'failed': '审查失败'
        };

        return `
            <div style="margin-bottom: 1rem; padding: 0.8rem; background: #2A2D30; border-radius: 4px; border: 1px solid #3C3F41;">
                <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: ${statusColors[this.workspaceStatus]};"></div>
                    <span style="font-weight: bold;">四阶段审查状态:</span>
                    <span style="color: ${statusColors[this.workspaceStatus]};">${statusTexts[this.workspaceStatus]}</span>
                </div>
                ${this.workspaceStatus === 'completed' ? this.renderCompletedStages() : ''}
            </div>
        `;
    }

    // 已完成阶段渲染
    renderCompletedStages() {
        const stages = [
            '阶段一：全局契约生成',
            '阶段二：引用式契约生成',
            '阶段三：契约履行与审计',
            '阶段四：整体性审计'
        ];

        return `
            <div style="font-size: 0.9rem;">
                <div style="margin-bottom: 0.3rem; color: #888;">已完成阶段:</div>
                ${stages.map(stage => `
                    <div style="display: flex; align-items: center; gap: 0.3rem; margin-bottom: 0.2rem;">
                        <div style="width: 4px; height: 4px; border-radius: 50%; background: #4CAF50;"></div>
                        <span style="color: #BBBBBB; font-size: 0.8rem;">${stage}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    // 获取审计状态
    getAuditStatus(deliverables, admissionReviewData) {
        if (this.workspaceStatus === 'completed') {
            return admissionReviewData.overall_status === 'failed' ? 'failed' : 'completed';
        } else if (this.workspaceStatus === 'reviewing') {
            return 'processing';
        }
        return deliverables.audit_status || 'pending';
    }

    // 获取审计状态文本
    getAuditStatusText(auditStatus, admissionReviewData) {
        switch (auditStatus) {
            case 'completed':
                return '四阶段审查已完成';
            case 'processing':
                return '审查进行中...';
            case 'failed':
                return '审查失败';
            case 'pending':
                return '等待开始审查';
            default:
                return admissionReviewData.status_text || '处理中...';
        }
    }

    // 事件处理方法
    handleReviewCompleted(data) {
        this.workspaceStatus = 'completed';
        this.reviewResults = data.results;
        this.render();
    }

    handleStageCompleted(data) {
        // 阶段完成时更新显示
        this.render();
    }
}