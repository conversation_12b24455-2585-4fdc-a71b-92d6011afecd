# 实施计划：搭建项目准入审查架构框架

- **文档版本**: 2.0
- **更新日期**: 2025-08-03
- **核心目标**: 搭建一个功能完整的架构框架，以支持项目经理工作台的“项目准入审查”功能。此框架将严格遵循V4.2设计文档，实现一个基于WebSocket的、支持状态持久化和断线重连的、采用四阶段阻塞式验证策略的健壮、可复用的服务。

---

## 1. 核心业务流程梳理

本实施计划旨在为以下三个核心用户流程搭建技术框架：

1.  **流程一：初始化工作区 (用户首次交互)**
    *   用户在前端界面输入一个目标设计文档的目录路径。
    *   前端通过WebSocket将此路径作为任务ID发送给后端。
    *   后端为该任务ID创建一个唯一的项目经理实例，并在目标目录下生成一个 `_pm_workspace` 工作区，用于存放状态和产出。

2.  **流程二：恢复工作区 (刷新/断线重连)**
    *   用户刷新页面或关闭浏览器后重新打开。
    *   前端从URL或浏览器本地存储（`localStorage`）中恢复上一次的任务ID。
    *   前端通过WebSocket向后端发送状态恢复请求，并附上任务ID。
    *   后端根据任务ID找到对应的项目经理实例，查询其当前状态（如“审查中”、“已完成”），并将此状态立即推送给前端，前端据此恢复UI显示。

3.  **流程三：执行项目准入审查 (核心功能)**
    *   用户点击“审查”按钮（原“扫描”）。
    *   前端通过WebSocket向后端发送启动审查的指令。
    *   后端项目经理在一个后台线程中，启动一个严格的、可复用的**四阶段阻塞式**验证流程。
    *   在验证的每个关键节点，后端都会通过WebSocket**主动推送**进度和状态更新给前端。
    *   任何一个阻塞性阶段验证失败，流程将**立即中止**，并向前端通知失败状态，但**不会**生成报告文件。

---

## 2. 架构设计原理 (Architectural Rationale)

之所以采用当前的设计，是基于以下几个核心的、专业的软件工程原则，以确保系统的健壮性、可维护性和可扩展性。

1.  **服务复用性 (Reusability)**
    *   “项目准入审查”被设计成一个独立、可复用的核心服务，而非仅仅是UI“扫描”按钮的后台实现。这意味着，未来当V4.2主治理引擎在执行代码生成等核心任务前，可以**直接调用**此审查服务作为其流程的第一步，以确保输入的`01号`文档质量达标。这避免了代码重复，并保证了全系统对文档质量验证的**标准统一**。

2.  **关注点分离 (Separation of Concerns)**
    *   本架构严格遵循关注点分离原则，将不同职责清晰地划分到不同的模块：
        *   **`bp_admission_review.py` (审查蓝图)**: 只负责定义审查**“做什么”**以及**“按什么顺序做”**。它封装了审查的业务逻辑策略。
        *   **`manager/project_manager.py` (项目经理)**: 负责**“何时”**触发审查，以及拿到结果后**“做什么”**（例如，通过WebSocket通知前端）。它是业务流程的调度者。
        *   **`web_interface/app.py` (WebSocket服务)**: 只负责底层的网络通信，处理连接和消息的收发，不包含任何业务逻辑。
    *   这种分离使得每个模块都可以独立测试、修改和演进，极大地提高了代码的可维护性。

3.  **健壮性与用户体验 (Robustness & UX)**
    *   采用**WebSocket实时推送**而非HTTP轮询，是现代Web应用处理长任务的最佳实践，它极大地降低了服务器和网络负载，并提供了真正的实时反馈。
    *   **状态持久化**与**断线重连**机制，确保了即使用户的浏览器崩溃或网络中断，正在运行的长任务也不会丢失，用户重开页面后可以无缝恢复到之前的状态，这是专业开发工具必备的健壮性特征。
    *   **阻塞式分阶段审查**的策略，实现了“快速失败”，确保了系统不会在明显的基础问题上浪费计算资源，并能在第一时间将最关键的错误反馈给用户。

---

## 3. 可复用的项目准入审查服务 (Reusable Project Admission Review Service)

为了确保任何提交给V4.2治理引擎的设计文档都是**正确的、安全的、可信赖的和完备的**，我们设计了这个可被系统复用的“项目准入审查”服务。该服务遵循下述严格的、四阶段阻塞式验证策略。

### **审查阶段详细定义**

| 阶段 | 名称 | 核心目标 | 输入 | 核心处理 | 输出/成功标准 | 失败后果 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **1** | **基础资产盘点 (Asset Inventory)** | 确认项目具备执行审查所需的最基础物理资产。 | 目标目录路径。 | 1. 检查目录及 `01号-*.md` 文件是否存在且可读。<br>2. 检查 `01号` 文档是否包含**必需的结构锚点**：` ```mermaid ` 和 `### CONSTRAINT-`。 | 所有资产均存在且可读。 | **立即中止**。通知：“项目核心资产缺失，无法启动审查。” |
| **2** | **核心模块语法验证 (Module Syntax Validation)** | 确保核心设计模块的语法正确，为逻辑分析提供可靠的输入。 | 通过第一阶段验证的`01号`文档内容。 | 1. **解析核心模块**: 提取Mermaid图和所有YAML代码块。<br>2. **语法校验**: 使用对应的解析器验证其语法。<br>3. **自动修复**: 对可100%确定的格式问题进行修复。 | 所有核心模块均语法正确或可被成功自动修复。 | **立即中止**。通知：“设计文档核心模块存在致命语法错误。” |
| **3** | **宏观架构健康度评估 (Holistic Architecture Assessment)** | 由AI扮演首席架构师，对设计的整体健全性和逻辑自洽性进行评估。 | 通过第二阶段验证的、语法正确的`01号`文档内容。 | 1. **AI上下文构建**: 准备包含完整文档和设计原则的Prompt。<br>2. **生成健康度报告**: AI分析并产出一个结构化的`DocumentHealthReport`对象（与V4.2主流程定义一致）。<br>3. **致命风险检查**: 检查报告中是否存在`CRITICAL`级别的风险、**循环依赖**或**分层架构违规**。 | `DocumentHealthReport`成功生成，且不包含任何已定义的致命风险。 | **立即中止**。通知：“项目在宏观架构设计上存在致命缺陷。” |
| **4** | **设计完备性与一致性审计 (Design Completeness & Consistency Audit)** | 在宏观设计通过后，审计所有微观设计细节是否完备且一致。 | 通过第三阶段验证的`01号`文档内容。 | 1. **微观约束冲突分析**: 检查不同约束间的逻辑矛盾。<br>2. **架构-实现映射审计**: 验证架构图中的核心组件是否都在代码清单中有定义。<br>3. **职责重叠分析**: 此阶段**复用**V4.2主流程的“阶段零：标准化与预验证”能力，但不生成`AtomicConstraint`，只进行冲突检测。 | 所有微观设计细节均通过一致性和完备性检查。 | **立即中止**。通知：“项目设计细节存在不一致或缺失。” |

---

## 4. 分阶段实施计划 (只关注框架)

### **第一阶段：搭建后端核心引擎**

*   **目标**: 构建能够管理项目实例和执行（占位的）四阶段审查流程的后端基础。

*   **需要创建的文件**:
    1.  **`tools/ace/src/project_manager_v2/blueprints/bp_admission_review.py`** (更专业的命名)
        *   **目的**: 实现四阶段阻塞式检查的调度器框架。
        *   **框架核心原则**: 为确保职责清晰并避免过度设计，此框架阶段的契约极为简单：所有验证器仅负责检测问题并返回一个布尔对 `(is_blocking: bool, message: str)`，调度器则根据此布尔值统一决定流程是否终止。
        *   **日志回传方式**: 实时流式推送（每个验证器输出立即通过WebSocket推送，无需缓存或定时）。
        *   **框架核心原则**: 为确保职责清晰并避免过度设计，此框架阶段的契约极为简单：所有验证器仅负责检测问题并返回一个布尔对 `(is_blocking: bool, message: str)`，调度器则根据此布尔值统一决定流程是否终止。
        *   **日志回传方式**: 实时流式推送（每个验证器输出立即通过WebSocket推送，无需缓存或定时）。
    2.  **`tools/ace/src/project_manager_v2/data_models/status_models.py`**
        *   **目的**: 为 `status.json` 提供标准的、类型安全的数据结构。

*   **需要修改的文件**:
    1.  **`tools/ace/src/project_manager_v2/services/project_manager_service.py`**
        *   **修改**: 增强 `get_or_create_manager` 方法，使其在首次创建 `ProjectManager` 时，同步在文件系统中创建 `_pm_workspace` 目录和初始的 `status.json` 文件。
    2.  **`tools/ace/src/project_manager_v2/manager/project_manager.py`**
        *   **修改**: 添加 `status` 属性、`set_socketio()` 方法，并创建 `run_admission_review()` 方法的骨架。

### **第二阶段：实现实时交互架构**

*   **目标**: 搭建完整的前后端WebSocket通信链路，严格按照UI架构标准实现标准化的实时交互机制。

*   **需要修改的文件**:
    1.  **`tools/ace/src/web_interface/app.py`**
        *   **A. WebSocket架构重构**:
            *   修改连接模式: 从 `ws://localhost:25526/ws/project/${projectId}` 改为 `ws://localhost:25526/ws/pm-v2`
            *   实现Bearer Token认证: 在WebSocket连接时验证Authorization头
            *   添加30秒心跳机制: 实现ping/pong心跳保持连接
            *   实现项目路径绑定: 支持 `bind_project_path` 事件动态绑定项目
        *   **B. 标准化WebSocket事件处理器**:
            *   核心工作流事件: `initialize_workspace`, `request_status_reconnect`, `start_review_task`
            *   审查阶段专用事件: `admission_review_started`, `review_stage_progress`, `review_stage_completed`, `review_failed`, `review_completed`
            *   系统事件: `bind_project_path`, `ping`
        *   **C. 标准消息格式实现**:
            *   统一消息结构: 所有WebSocket消息包含 `type`, `data`, `timestamp`, `project_path`, `sequence`
            *   序列号管理: 实现消息序列号自动递增
            *   项目路径追踪: 在会话中维护当前项目路径
        *   **D. HTTP API v2接口实现**:
            *   工作区管理API: `/api/v2/workspace/<task_id>`
    2.  **`tools/ace/src/project_manager_v2/manager/project_manager.py`**
        *   **A. WebSocket回调机制重构**:
            *   修改progress_callback: 生成符合UI标准的完整消息格式
            *   实现消息标准化方法: 添加 `_create_standard_message()` 方法，直接在ProjectManager中处理格式转换
            *   实现状态指示器: 添加 `status_indicator` 字段支持
            *   添加进度百分比: 计算并提供 `progress_percentage`
        *   **B. 事件类型标准化**:
            *   将现有事件映射到UI标准: `stage_started` -> `review_stage_progress`, `stage_completed` -> `review_stage_completed`
        *   **C. 数据结构扩展**:
            *   符合UI标准的数据结构: 包含 `stage_number`, `status`, `progress_percentage`, `status_indicator`, `updated_at` 等字段
    3.  **`tools/ace/src/web_interface/static/js/unified/data-manager.js`**
        *   **A. WebSocket连接重构**:
            *   修改连接URL: 改为 `ws://localhost:25526/ws/pm-v2`
            *   实现认证机制: 添加Authorization头支持
            *   添加心跳机制: 实现30秒ping发送
            *   实现项目路径绑定: 连接后自动发送 `bind_project_path`
        *   **B. 消息处理扩展**:
            *   扩展handleWebSocketMessage: 添加所有UI标准事件处理
            *   添加数据schema: 支持 `workspace_status`, `admission_review`, `stage_progress` 等
            *   实现任务ID管理: 替换固定projectId为动态task_id
    4.  **`tools/ace/src/web_interface/static/js/components/HumanInputComponent.js`**
        *   **A. 用户界面扩展**:
            *   添加项目路径输入: 实现路径选择和验证界面
            *   改造扫描按钮: 发送 `start_review_task` WebSocket消息
            *   添加工作区状态显示: 显示当前工作区状态和进度
        *   **B. WebSocket交互实现**:
            *   实现消息发送: 支持 `initialize_workspace`, `start_review_task` 消息
            *   添加状态监听: 监听 `workspace_created`, `review_progress_update` 等事件
    5.  **`tools/ace/src/web_interface/static/js/pm_v2_unified_init.js`**
        *   **A. 初始化重构**:
            *   移除固定projectId: 改为动态任务管理模式
            *   实现状态恢复: 添加 `restoreTaskId()` 和 `saveCurrentTask()`
            *   添加WebSocket事件监听: 监听所有UI标准事件

*   **需要创建的文件**:
    *   *注：所有功能将在现有文件中实现，无需创建新文件*
    

*   **实施优先级**:
    1.  **阶段2.1**: WebSocket架构重构（app.py连接模式+认证+心跳+消息格式）
    2.  **阶段2.2**: 消息标准化实现（ProjectManager回调重构+内联消息格式转换）
    3.  **阶段2.3**: 前端集成优化（DataManager重构+HumanInputComponent扩展）
    4.  **阶段2.4**: HTTP API补充（现有蓝图扩展+错误处理优化）

*   **验收标准**:
    *   WebSocket连接符合UI标准（通用连接+认证+心跳+项目绑定）
    *   消息格式完全标准化（包含所有必需字段）
    *   事件类型完全匹配UI标准
    *   数据结构完全符合UI组件需求
    *   前后端完整对接，实时状态同步无误
    *   支持多项目动态管理和断线重连机制

### **第三阶段：完善状态持久化与鲁棒性**

*   **目标**: 实现流程二，让系统变得健壮，能够应对刷新和浏览器关闭等情况。

*   **需要修改的文件**:
    1.  **`tools/ace/src/web_interface/static/js/pm_v2_unified_init.js`**
        *   **修改**:
            *   初始化WebSocket客户端连接。
            *   实现 `restoreTaskId()` 和 `saveCurrentTask()` 函数，通过URL和`localStorage`管理任务ID。
            *   在 `socket.on('connect')` 事件中，实现重连后请求状态的逻辑。
            *   重构UI事件，使其通过 `start_review_task` WebSocket消息启动任务。
            *   添加 `status_update_on_reconnect` 和 `review_progress_update` 的消息监听器框架。

---

## 5. 基于UI标准的框架补充要求

为确保框架严格遵循已制定的UI开发标准，需要在上述三个阶段中补充以下标准化实现：

### **5.1 HTTP API接口规范补充**

### **5.2 WebSocket事件规范补充**

*   **参照标准**: `docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/ui/WebSocket事件规范.md`
*   **框架必需的标准化实现**:
    1.  **连接和认证**: WebSocket连接URL `ws://localhost:25526/ws/pm-v2`（通用连接，不依赖预设project_id），Bearer Token验证，30秒心跳机制
    2.  **核心工作流事件**（按照三个流程设计）:
        *   `initialize_workspace` - 初始化工作区（流程一）
        *   `workspace_created` - 工作区创建完成
        *   `request_status_reconnect` - 请求状态恢复（流程二）
        *   `status_update_on_reconnect` - 状态恢复响应
        *   `start_review_task` - 启动审查任务（流程三）
        *   `review_progress_update` - 审查进度更新
    3.  **审查阶段专用事件**: `admission_review_started`, `review_stage_progress`, `review_stage_completed`, `review_failed`, `review_completed`
    4.  **标准消息结构**: 按照UI标准的消息格式实现，包含type、data、timestamp、task_id（而非project_id）、sequence字段

### **5.3 JavaScript组件复用架构补充**

*   **参照标准**: `docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/ui/JavaScript组件复用实现方案.md`
*   **框架搭建要求**:
    1.  **DataManager扩展**: 添加工作区数据管理schema，包含task_id、workspace_status、current_stage、stages、project_path等字段
    2.  **BaseComponent扩展**: 添加工作区状态渲染和审查事件处理的标准方法
    3.  **ComponentFactory配置**: 注册工作区管理和审查相关组件类型
    4.  **WebSocket客户端集成**: 按照UI标准配置WebSocket客户端（主要通信方式）
    5.  **HttpClient集成**: 按照UI标准配置HTTP客户端（辅助功能）

### **5.4 前后端接口对接补充**

*   **参照标准**: `docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/ui/前后端接口对接文档.md`
*   **数据流实现要求**:
    1.  **统一数据流模式**: Backend → DataAdapter → StateManager → UIComponent（主要通过WebSocket实现）
    2.  **工作区数据适配器**: 实现WorkspaceDataAdapter类，处理路径驱动的工作区数据转换
    3.  **审查数据适配器**: 实现AdmissionReviewDataAdapter类，处理四阶段审查数据转换
    4.  **九宫格区域映射**: 将工作区和审查功能映射到对应的九宫格区域（区域1-2显示进度，区域4显示项目经理状态，区域8控制面板）
    5.  **任务ID管理**: 实现基于目录路径的任务ID生成和管理机制

### **5.5 组件实现细节补充**

*   **参照标准**: `docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/ui/组件实现细节文档.md`
*   **框架级配置要求**:
    1.  **WebSocket事件分发器**: 实现AdmissionReviewEventDispatcher类，处理审查相关事件分发
    2.  **组件间通信机制**: 实现AdmissionReviewEventBus，支持审查进度广播
    3.  **状态管理标准化**: 按照UI标准配置状态管理机制

### **5.6 统一前后端对接架构补充**

*   **参照标准**: `docs/features/T001-create-plans-20250612/v4/design/化简/核心/design/v2/ui/统一前后端对接架构方案.md`
*   **核心层实现要求**:
    1.  **UnifiedHTTPClient扩展**: 添加审查相关端点配置和方法
    2.  **UnifiedWebSocketClient配置**: 添加审查事件类型定义
    3.  **ProjectManager消息标准化**: 在ProjectManager中直接实现消息格式转换，无需独立适配器
    4.  **性能优化策略**: 按照UI标准实现缓存和防抖机制

---

## 6. 现有代码改造要求

基于对现有前端代码的验证，需要对以下文件进行具体改造以支持搭建扫描阶段框架：

### **6.1 DataManager改造**

*   **文件**: `tools/ace/src/web_interface/static/js/unified/data-manager.js`
*   **当前问题**: WebSocket连接使用固定的project_id模式 (`ws://localhost:25526/ws/project/${this.projectId}`)
*   **改造要求**:
    1.  **修改WebSocket连接URL**: 改为通用连接 `ws://localhost:25526/ws/pm-v2`
    2.  **添加任务ID管理**: 支持动态task_id而非固定projectId
    3.  **扩展消息处理**: 在`handleWebSocketMessage`中添加工作区和审查事件处理
    4.  **添加数据schema**: 支持workspace_status、admission_review等数据类型

### **6.2 应用初始化改造**

*   **文件**: `tools/ace/src/web_interface/static/js/pm_v2_unified_init.js`
*   **当前问题**: 使用固定的projectId (`'default_project'`)
*   **改造要求**:
    1.  **移除固定projectId**: 改为动态任务管理模式
    2.  **添加状态恢复逻辑**: 实现`restoreTaskId()`和`saveCurrentTask()`函数
    3.  **添加WebSocket事件监听**: 监听工作区创建、状态恢复、审查进度等事件
    4.  **添加路径输入处理**: 支持用户输入目录路径创建工作区

### **6.3 HumanInputComponent改造**

*   **文件**: `tools/ace/src/web_interface/static/js/components/HumanInputComponent.js`
*   **当前问题**: "扫描"按钮功能未实现，缺少路径输入
*   **改造要求**:
    1.  **添加路径输入框**: 在现有UI中添加项目路径输入区域
    2.  **改造扫描按钮**: 将`handleScanningClick()`改为启动审查流程
    3.  **添加工作区状态显示**: 显示当前工作区状态和审查进度
    4.  **添加WebSocket消息发送**: 支持发送`initialize_workspace`和`start_review_task`消息

### **6.4 组件数据类型扩展**

*   **需要扩展的组件**: ProjectProgressComponent, ManagerStatusComponent等
*   **改造要求**:
    1.  **添加审查数据类型**: 在`getDataTypes()`中添加`admission_review`, `workspace_status`
    2.  **扩展渲染逻辑**: 支持显示四阶段审查进度
    3.  **添加状态指示器**: 显示工作区状态（idle, initializing, reviewing, completed, failed）

### **6.5 WebSocket事件处理扩展**

*   **文件**: `tools/ace/src/web_interface/static/js/unified/data-manager.js`
*   **改造要求**: 在`handleWebSocketMessage`方法中添加以下事件处理：
    1.  **工作区事件**: `workspace_created`, `status_update_on_reconnect`
    2.  **审查事件**: `admission_review_started`, `review_stage_progress`, `review_stage_completed`, `review_failed`, `review_completed`
    3.  **错误处理**: 统一的错误消息处理和用户提示
    4.  **消息格式适配**: 前端DataManager已具备消息适配能力，可直接处理ProjectManager发送的标准格式消息

### **6.6 状态持久化实现**

*   **文件**: `tools/ace/src/web_interface/static/js/pm_v2_unified_init.js`
*   **改造要求**:
    1.  **localStorage管理**: 保存和恢复当前任务ID
    2.  **URL参数管理**: 同步任务ID到URL参数
    3.  **页面刷新处理**: 自动恢复工作区状态
    4.  **断线重连逻辑**: WebSocket重连后自动请求状态恢复

---

## 7. 设计决策说明

### **7.1 取消独立消息适配器的理由**

基于架构简洁性和实用性的考虑，本实施计划取消了 `pm_v2_message_adapter.py` 的创建，理由如下：

1.  **过度设计**: 当前场景下，创建独立的适配器类是过度设计
2.  **简单有效**: 直接在ProjectManager中实现消息格式转换更简单直接
3.  **减少复杂性**: 避免引入额外的依赖和抽象层
4.  **前端已有适配**: 前端DataManager已经具备消息适配能力

### **7.2 替代方案**

在ProjectManager中直接实现消息标准化：

```python
def _create_standard_message(self, event_type: str, data: dict) -> dict:
    """创建符合UI标准的消息格式"""
    return {
        "type": self._map_event_type(event_type),
        "data": self._enhance_data_structure(data, event_type),
        "timestamp": datetime.now().isoformat(),
        "project_path": self.design_doc_path,
        "sequence": self._get_next_sequence()
    }

def progress_callback(event_type: str, data: Dict[str, Any]):
    if self.socketio_callback:
        standard_message = self._create_standard_message(event_type, data)
        self.socketio_callback(standard_message)
```

这样既能满足UI架构要求，又避免了不必要的复杂性。
