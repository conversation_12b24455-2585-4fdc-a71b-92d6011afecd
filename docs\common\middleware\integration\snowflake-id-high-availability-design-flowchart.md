graph TD
    A[客户端SDK启动] --> B[加载本地配置文件]
    B --> C[检查Service-Center可用性]
    
    C -->|可用| D[远程模式]
    C -->|不可用| E[本地模式]
    
    D --> F[从中心服务获取ID]
    E --> G[使用本地工作机器ID生成ID]
    
    F --> H[缓存ID(内存+持久化)]
    G --> I[Snowflake算法生成ID]
    
    H --> J[定期尝试重连Service-Center]
    I --> J
    
    J --> K[检测时钟回拨]
    K -->|正常| L[继续生成ID]
    K -->|回拨| M[使用最后已知时间]
    
    L --> N[监控与报警]
    M --> N
    
    N --> O{模式切换/缓存不足?}
    O -->|是| P[记录事件]
    O -->|否| Q[继续运行]
    
    subgraph 工作机器ID管理
        R[静态预分配]
        S[配置持久化]
    end
    
    subgraph 容错机制
        T[定期重连]
        U[时钟回拨处理]
    end
    
    subgraph 缓存策略
        V[内存一级缓存]
        W[本地二级缓存]
    end
