/**
 * PM V2 统一架构测试脚本
 * 用于验证重构后的功能是否正常工作
 */

// 测试配置
const TEST_CONFIG = {
    projectId: 'test_project_001',
    enableMockData: true,
    testTimeout: 10000 // 10秒超时
};

// 测试结果收集器
class TestResultCollector {
    constructor() {
        this.results = [];
        this.startTime = Date.now();
    }
    
    addResult(testName, success, message, duration) {
        this.results.push({
            testName,
            success,
            message,
            duration,
            timestamp: new Date().toISOString()
        });
        
        const status = success ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} ${testName} (${duration}ms): ${message}`);
    }
    
    getSummary() {
        const totalTests = this.results.length;
        const passedTests = this.results.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        const totalDuration = Date.now() - this.startTime;
        
        return {
            totalTests,
            passedTests,
            failedTests,
            successRate: totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0,
            totalDuration
        };
    }
    
    printSummary() {
        const summary = this.getSummary();
        console.log('\n' + '='.repeat(60));
        console.log('PM V2 统一架构测试报告');
        console.log('='.repeat(60));
        console.log(`总测试数: ${summary.totalTests}`);
        console.log(`通过: ${summary.passedTests}`);
        console.log(`失败: ${summary.failedTests}`);
        console.log(`成功率: ${summary.successRate}%`);
        console.log(`总耗时: ${summary.totalDuration}ms`);
        console.log('='.repeat(60));
        
        if (summary.failedTests > 0) {
            console.log('\n失败的测试:');
            this.results.filter(r => !r.success).forEach(result => {
                console.log(`❌ ${result.testName}: ${result.message}`);
            });
        }
    }
}

// 测试函数
class PM_V2_Tests {
    constructor() {
        this.collector = new TestResultCollector();
        this.appManager = null;
    }
    
    async runAllTests() {
        console.log('开始 PM V2 统一架构测试...\n');
        
        try {
            await this.testConfigurationLoading();
            await this.testUnifiedArchitectureLoading();
            await this.testAppManagerInitialization();
            await this.testComponentRegistration();
            await this.testDataManagerFunctionality();
            await this.testMockDataLoading();
            await this.testComponentRendering();
            
        } catch (error) {
            console.error('测试过程中发生错误:', error);
        } finally {
            this.collector.printSummary();
        }
    }
    
    async testConfigurationLoading() {
        const startTime = Date.now();
        try {
            // 测试PM V2数据配置是否正确加载
            if (typeof window.PM_V2_DATA_TYPE_MAPPING === 'undefined') {
                throw new Error('PM_V2_DATA_TYPE_MAPPING 未定义');
            }
            
            if (typeof window.PM_V2_WEBSOCKET_EVENTS === 'undefined') {
                throw new Error('PM_V2_WEBSOCKET_EVENTS 未定义');
            }
            
            // 检查关键数据类型是否存在
            const requiredDataTypes = ['progress', 'risk_assessment', 'manager_status', 'algorithm_logs'];
            for (const dataType of requiredDataTypes) {
                if (!window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
                    throw new Error(`缺少数据类型配置: ${dataType}`);
                }
            }
            
            this.collector.addResult(
                '配置文件加载',
                true,
                '所有配置文件正确加载',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                '配置文件加载',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testUnifiedArchitectureLoading() {
        const startTime = Date.now();
        try {
            // 测试统一架构核心类是否正确加载
            const requiredClasses = ['HttpClient', 'DataManager', 'BaseComponent', 'AppManager'];
            for (const className of requiredClasses) {
                if (typeof window[className] === 'undefined') {
                    throw new Error(`统一架构类未定义: ${className}`);
                }
            }
            
            this.collector.addResult(
                '统一架构加载',
                true,
                '所有核心类正确加载',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                '统一架构加载',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testAppManagerInitialization() {
        const startTime = Date.now();
        try {
            // 测试AppManager初始化
            this.appManager = new AppManager(TEST_CONFIG.projectId);
            
            if (!this.appManager.dataManager) {
                throw new Error('DataManager 未正确初始化');
            }
            
            this.collector.addResult(
                'AppManager初始化',
                true,
                'AppManager正确初始化',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                'AppManager初始化',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testComponentRegistration() {
        const startTime = Date.now();
        try {
            if (!this.appManager) {
                throw new Error('AppManager 未初始化');
            }
            
            // 测试组件注册
            const requiredComponents = [
                'ProjectProgressComponent',
                'RiskAssessmentComponent', 
                'ManagerStatusComponent',
                'AlgorithmMindsetComponent',
                'ConstraintReviewComponent',
                'KnowledgeBaseComponent',
                'HumanInputComponent',
                'ProjectOutputComponent'
            ];
            
            for (const componentName of requiredComponents) {
                if (typeof window[componentName] === 'undefined') {
                    throw new Error(`组件类未定义: ${componentName}`);
                }
            }
            
            this.collector.addResult(
                '组件注册',
                true,
                '所有组件类正确定义',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                '组件注册',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testDataManagerFunctionality() {
        const startTime = Date.now();
        try {
            if (!this.appManager || !this.appManager.dataManager) {
                throw new Error('DataManager 未初始化');
            }
            
            const dataManager = this.appManager.dataManager;
            
            // 测试数据类型配置获取
            const config = dataManager.getDataTypeConfig('progress');
            if (!config || !config.endpoint) {
                throw new Error('数据类型配置获取失败');
            }
            
            // 测试端点获取
            const endpoint = dataManager.getEndpoint('progress');
            if (!endpoint) {
                throw new Error('端点获取失败');
            }
            
            this.collector.addResult(
                'DataManager功能',
                true,
                'DataManager功能正常',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                'DataManager功能',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testMockDataLoading() {
        const startTime = Date.now();
        try {
            if (!this.appManager || !this.appManager.dataManager) {
                throw new Error('DataManager 未初始化');
            }
            
            const dataManager = this.appManager.dataManager;
            
            // 测试Mock数据加载
            const progressData = await dataManager.fetchData('progress');
            if (!progressData || !progressData.current_stage) {
                throw new Error('Mock数据加载失败');
            }
            
            this.collector.addResult(
                'Mock数据加载',
                true,
                'Mock数据正确加载',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                'Mock数据加载',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
    
    async testComponentRendering() {
        const startTime = Date.now();
        try {
            // 测试组件容器是否存在
            const containerIds = [
                'progress-area', 'risk-area', 'manager-area', 'algorithm-area',
                'constraint-area', 'knowledge-area', 'control-area', 'deliverables-area'
            ];
            
            for (const containerId of containerIds) {
                const container = document.getElementById(containerId);
                if (!container) {
                    throw new Error(`容器不存在: ${containerId}`);
                }
            }
            
            this.collector.addResult(
                '组件容器检查',
                true,
                '所有组件容器存在',
                Date.now() - startTime
            );
            
        } catch (error) {
            this.collector.addResult(
                '组件容器检查',
                false,
                error.message,
                Date.now() - startTime
            );
        }
    }
}

// 导出测试类
window.PM_V2_Tests = PM_V2_Tests;

// 自动运行测试（如果在测试模式下）
if (window.location.search.includes('test=true')) {
    document.addEventListener('DOMContentLoaded', async () => {
        const tests = new PM_V2_Tests();
        await tests.runAllTests();
    });
}
