#!/usr/bin/env node

/**
 * 记忆系统验证工具 (增强版)
 * 用于验证记忆索引文档与功能状态注册表的一致性，并集成权威性验证功能
 *
 * 使用方法:
 * node docs/ai-memory/tools/verify-memory-system.js
 * node docs/ai-memory/tools/verify-memory-system.js --feature F003
 * node docs/ai-memory/tools/verify-memory-system.js --check-sync
 * node docs/ai-memory/tools/verify-memory-system.js --full-report
 * node docs/ai-memory/tools/verify-memory-system.js --check-authority
 * node docs/ai-memory/tools/verify-memory-system.js --quick-check
 */

const fs = require('fs');
const path = require('path');

class MemorySystemVerifier {
    constructor() {
        this.rootPath = path.resolve(__dirname, '../../..');
        this.featureStatusPath = path.join(this.rootPath, 'docs/feature-status.json');
        this.memoryIndexPath = path.join(this.rootPath, 'docs/ai-memory');
        // 注意：docs/features是临时工作区域，不应作为历史参考
        // 只在验证当前开发功能时使用

        this.errors = [];
        this.warnings = [];
        this.info = [];

        this.featureStatus = null;
        this.authorityValidator = null;
        this.loadFeatureStatus();
        this.loadAuthorityValidator();
    }

    /**
     * 加载权威性验证器
     */
    loadAuthorityValidator() {
        try {
            const AuthorityValidator = require('./authority-validator.js');
            this.authorityValidator = new AuthorityValidator();
            this.info.push('✓ 权威性验证器加载成功');
        } catch (error) {
            this.warnings.push(`⚠ 权威性验证器加载失败: ${error.message}`);
        }
    }

    loadFeatureStatus() {
        try {
            const content = fs.readFileSync(this.featureStatusPath, 'utf8');
            this.featureStatus = JSON.parse(content);
            this.info.push(`✓ 功能状态注册表加载成功: ${this.featureStatusPath}`);
        } catch (error) {
            this.errors.push(`✗ 无法加载功能状态注册表: ${error.message}`);
            this.featureStatus = { feature_status_registry: { features: {} } };
        }
    }

    /**
     * 验证特定功能的同步状态
     */
    verifyFeature(featureId) {
        this.info.push(`\n=== 验证功能 ${featureId} ===`);
        
        const feature = this.featureStatus.feature_status_registry.features[featureId];
        if (!feature) {
            this.errors.push(`✗ 功能 ${featureId} 在状态注册表中不存在`);
            return false;
        }

        // 只对active_development状态的功能验证原始文档存在性
        // 因为docs/features是临时工作区域，不应作为历史参考
        if (feature.status === 'active_development') {
            const originalDocPath = path.join(this.rootPath, feature.original_doc_path);
            if (!fs.existsSync(originalDocPath)) {
                this.errors.push(`✗ 活跃开发功能的原始文档不存在: ${originalDocPath}`);
            } else {
                this.info.push(`✓ 活跃开发功能的原始文档存在: ${feature.original_doc_path}`);
            }
        } else {
            this.info.push(`📝 功能 ${featureId} 状态为 ${feature.status}，跳过原始文档验证 (docs/features仅用于当前开发)`);
        }

        // 验证记忆索引文档存在
        const memoryIndexPath = path.join(this.rootPath, feature.memory_index_path);
        if (!fs.existsSync(memoryIndexPath)) {
            this.errors.push(`✗ 记忆索引文档不存在: ${memoryIndexPath}`);
            return false;
        } else {
            this.info.push(`✓ 记忆索引文档存在: ${feature.memory_index_path}`);
        }

        // 验证记忆索引文档内容
        this.verifyMemoryIndexContent(featureId, feature, memoryIndexPath);

        // 检查同步状态
        this.checkSyncStatus(featureId, feature);

        return true;
    }

    /**
     * 验证记忆索引文档内容
     */
    verifyMemoryIndexContent(featureId, feature, memoryIndexPath) {
        try {
            const content = fs.readFileSync(memoryIndexPath, 'utf8');
            
            // 检查是否包含同步状态部分
            if (!content.includes('## 同步状态')) {
                this.warnings.push(`⚠ 记忆索引 ${featureId} 缺少"同步状态"部分`);
            } else {
                this.info.push(`✓ 记忆索引 ${featureId} 包含同步状态部分`);
            }

            // 检查是否包含警示信息
            if (!content.includes('⚠️ 重要提醒')) {
                this.warnings.push(`⚠ 记忆索引 ${featureId} 缺少警示信息`);
            } else {
                this.info.push(`✓ 记忆索引 ${featureId} 包含警示信息`);
            }

            // 检查功能ID是否一致
            const featureIdMatch = content.match(/功能ID[*:\s]*([A-Z]\d{3})/);
            if (featureIdMatch && featureIdMatch[1] !== featureId) {
                this.errors.push(`✗ 记忆索引中的功能ID (${featureIdMatch[1]}) 与预期 (${featureId}) 不匹配`);
            } else if (featureIdMatch) {
                this.info.push(`✓ 记忆索引中的功能ID匹配: ${featureId}`);
            }

            // 检查同步来源路径是否一致
            const syncSourceMatch = content.match(/同步来源[*:\s]*(.+)/);
            if (syncSourceMatch) {
                const syncSource = syncSourceMatch[1].trim();
                if (syncSource !== feature.original_doc_path) {
                    this.warnings.push(`⚠ 同步来源路径不匹配: 索引中为"${syncSource}"，注册表中为"${feature.original_doc_path}"`);
                } else {
                    this.info.push(`✓ 同步来源路径匹配`);
                }
            } else {
                this.warnings.push(`⚠ 记忆索引 ${featureId} 缺少同步来源信息`);
            }

        } catch (error) {
            this.errors.push(`✗ 无法读取记忆索引内容: ${error.message}`);
        }
    }

    /**
     * 检查同步状态
     */
    checkSyncStatus(featureId, feature) {
        const syncStatus = feature.sync_status;
        const lastSyncDate = new Date(feature.last_sync_date);
        const now = new Date();
        const daysSinceSync = Math.floor((now - lastSyncDate) / (1000 * 60 * 60 * 24));

        this.info.push(`📊 同步状态信息:`);
        this.info.push(`   - 功能状态: ${feature.status}`);
        this.info.push(`   - 同步状态: ${syncStatus}`);
        this.info.push(`   - 最后同步: ${feature.last_sync_date} (${daysSinceSync} 天前)`);
        this.info.push(`   - 优先级: ${feature.priority}`);

        // 根据功能状态检查同步时效性
        const syncRules = this.featureStatus.feature_status_registry.sync_rules;
        const statusRule = syncRules[feature.status];

        if (statusRule && daysSinceSync > statusRule.check_frequency_days) {
            this.warnings.push(`⚠ 功能 ${featureId} 同步超时: ${daysSinceSync} 天 > ${statusRule.check_frequency_days} 天阈值`);
        }

        // 检查同步状态的合理性
        if (syncStatus === 'requires_update') {
            this.warnings.push(`⚠ 功能 ${featureId} 需要更新，建议执行同步`);
        } else if (syncStatus === 'sync_failed') {
            this.errors.push(`✗ 功能 ${featureId} 同步失败，需要人工干预`);
        }

        // 根据功能状态给出文档使用建议
        const statusType = this.featureStatus.feature_status_registry.status_types[feature.status];
        if (statusType) {
            this.info.push(`📋 文档使用建议: ${statusType.doc_priority}`);
        }
    }

    /**
     * 验证所有功能
     */
    verifyAllFeatures() {
        this.info.push('\n=== 验证所有功能 ===');

        const features = this.featureStatus.feature_status_registry.features;
        const featureIds = Object.keys(features);

        if (featureIds.length === 0) {
            this.warnings.push('⚠ 没有找到任何功能定义');
            return;
        }

        this.info.push(`📋 发现 ${featureIds.length} 个功能: ${featureIds.join(', ')}`);

        featureIds.forEach(featureId => {
            this.verifyFeature(featureId);
        });
    }

    /**
     * 验证模块完整性 (Phase 4 新增)
     */
    verifyModuleIntegrity() {
        this.info.push('\n=== 验证模块完整性 ===');

        const commandsPath = path.join(this.rootPath, 'docs/ai-memory/L1-core/commands');

        // 定义预期的模块结构
        const expectedModules = {
            'core': ['database-ops.json', 'config-validation.json', 'error-handling.json'],
            'architecture': ['evolution.json', 'service-interface.json'],
            'quality': ['best-practices.json', 'duplication-check.json'],
            'document': ['creation.json', 'standards.json', 'execution.json'],
            'analysis': ['security.json', 'technical.json', 'business.json', 'metacognitive.json']
        };

        let totalModules = 0;
        let validModules = 0;

        Object.entries(expectedModules).forEach(([category, modules]) => {
            this.info.push(`\n📂 验证 ${category} 模块类别:`);

            const categoryPath = path.join(commandsPath, category);
            if (!fs.existsSync(categoryPath)) {
                this.errors.push(`✗ 模块类别目录不存在: ${category}`);
                return;
            }

            modules.forEach(moduleFile => {
                totalModules++;
                const modulePath = path.join(categoryPath, moduleFile);

                if (!fs.existsSync(modulePath)) {
                    this.errors.push(`✗ 模块文件不存在: ${category}/${moduleFile}`);
                    return;
                }

                // 验证模块文件格式
                try {
                    const content = fs.readFileSync(modulePath, 'utf8');
                    const moduleData = JSON.parse(content);

                    // 检查必需的字段
                    const requiredFields = ['metadata', 'commands', 'activation_rules'];
                    const missingFields = requiredFields.filter(field => !moduleData[field]);

                    if (missingFields.length > 0) {
                        this.warnings.push(`⚠ 模块 ${category}/${moduleFile} 缺少字段: ${missingFields.join(', ')}`);
                    } else {
                        this.info.push(`  ✓ ${moduleFile}: 格式正确`);
                        validModules++;
                    }

                    // 检查命令数量
                    const commandCount = Object.keys(moduleData.commands || {}).length;
                    this.info.push(`    - 包含 ${commandCount} 个命令`);

                } catch (error) {
                    this.errors.push(`✗ 模块文件格式错误 ${category}/${moduleFile}: ${error.message}`);
                }
            });
        });

        this.info.push(`\n📊 模块完整性统计:`);
        this.info.push(`   - 总模块数: ${totalModules}`);
        this.info.push(`   - 有效模块: ${validModules}`);
        this.info.push(`   - 完整性: ${((validModules / totalModules) * 100).toFixed(1)}%`);

        if (validModules === totalModules) {
            this.info.push('✓ 所有模块验证通过');
        } else {
            this.warnings.push(`⚠ ${totalModules - validModules} 个模块存在问题`);
        }
    }

    /**
     * 验证三级加载架构 (Phase 4 新增)
     */
    verifyThreeTierArchitecture() {
        this.info.push('\n=== 验证三级加载架构 ===');

        try {
            const attentionCommandsPath = path.join(this.rootPath, 'docs/ai-memory/L1-core/attention-commands.json');
            const content = fs.readFileSync(attentionCommandsPath, 'utf8');
            const config = JSON.parse(content);

            // 验证加载策略配置
            if (!config.loading_strategy) {
                this.errors.push('✗ 缺少 loading_strategy 配置');
                return;
            }

            const strategy = config.loading_strategy;

            // 验证 immediate 加载
            if (!strategy.immediate || !Array.isArray(strategy.immediate)) {
                this.errors.push('✗ immediate 加载配置缺失或格式错误');
            } else {
                this.info.push(`✓ immediate 加载: ${strategy.immediate.length} 个模块`);
                strategy.immediate.forEach(module => {
                    const modulePath = path.join(this.rootPath, 'docs/ai-memory/L1-core/commands', module);
                    if (fs.existsSync(modulePath)) {
                        this.info.push(`  ✓ ${module}`);
                    } else {
                        this.errors.push(`  ✗ immediate 模块不存在: ${module}`);
                    }
                });
            }

            // 验证 context 加载映射
            if (!strategy.context_mapping || typeof strategy.context_mapping !== 'object') {
                this.errors.push('✗ context_mapping 配置缺失或格式错误');
            } else {
                this.info.push(`✓ context 加载映射: ${Object.keys(strategy.context_mapping).length} 个任务类型`);
                Object.entries(strategy.context_mapping).forEach(([taskType, modules]) => {
                    this.info.push(`  📋 ${taskType}: ${modules.length} 个模块`);
                });
            }

            // 验证 on-demand 加载触发器
            if (!strategy.on_demand_triggers || typeof strategy.on_demand_triggers !== 'object') {
                this.errors.push('✗ on_demand_triggers 配置缺失或格式错误');
            } else {
                this.info.push(`✓ on-demand 触发器: ${Object.keys(strategy.on_demand_triggers).length} 个触发条件`);
                Object.entries(strategy.on_demand_triggers).forEach(([trigger, modules]) => {
                    this.info.push(`  🎯 ${trigger}: ${modules.length} 个模块`);
                });
            }

            // 验证 RIPER-5 集成
            if (!config.riper5_integration || typeof config.riper5_integration !== 'object') {
                this.warnings.push('⚠ RIPER-5 集成配置缺失');
            } else {
                const riper5Modes = ['RESEARCH', 'INNOVATE', 'PLAN', 'EXECUTE', 'REVIEW'];
                const configuredModes = Object.keys(config.riper5_integration);
                const missingModes = riper5Modes.filter(mode => !configuredModes.includes(mode));

                if (missingModes.length > 0) {
                    this.warnings.push(`⚠ RIPER-5 模式配置不完整，缺少: ${missingModes.join(', ')}`);
                } else {
                    this.info.push('✓ RIPER-5 集成配置完整');
                }
            }

        } catch (error) {
            this.errors.push(`✗ 三级加载架构验证失败: ${error.message}`);
        }
    }

    /**
     * 性能监控 (Phase 4 新增)
     */
    performanceMonitoring() {
        this.info.push('\n=== 性能监控 ===');

        try {
            const startTime = Date.now();

            // 模拟加载所有模块文件
            const commandsPath = path.join(this.rootPath, 'docs/ai-memory/L1-core/commands');
            const moduleFiles = this.getModuleFilesRecursively(commandsPath);

            let loadedModules = 0;
            let totalSize = 0;

            moduleFiles.forEach(filePath => {
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    JSON.parse(content); // 验证 JSON 格式
                    loadedModules++;
                    totalSize += content.length;
                } catch (error) {
                    this.warnings.push(`⚠ 模块加载失败: ${path.relative(this.rootPath, filePath)}`);
                }
            });

            const loadTime = Date.now() - startTime;

            this.info.push(`📊 性能统计:`);
            this.info.push(`   - 模块文件数: ${moduleFiles.length}`);
            this.info.push(`   - 成功加载: ${loadedModules}`);
            this.info.push(`   - 总大小: ${(totalSize / 1024).toFixed(2)} KB`);
            this.info.push(`   - 加载时间: ${loadTime} ms`);
            this.info.push(`   - 平均每模块: ${(loadTime / loadedModules).toFixed(2)} ms`);

            // 性能基准检查
            const avgLoadTime = loadTime / loadedModules;
            if (avgLoadTime > 10) {
                this.warnings.push(`⚠ 模块加载性能较慢，平均 ${avgLoadTime.toFixed(2)} ms/模块`);
            } else {
                this.info.push('✓ 模块加载性能良好');
            }

            // 检查 legacy 文件大小对比
            const legacyPath = path.join(this.rootPath, 'docs/ai-memory/L1-core/attention-commands-legacy.json');
            if (fs.existsSync(legacyPath)) {
                const legacySize = fs.readFileSync(legacyPath, 'utf8').length;
                const sizeReduction = ((legacySize - totalSize) / legacySize * 100).toFixed(1);
                this.info.push(`📈 模块化优化效果:`);
                this.info.push(`   - Legacy 文件: ${(legacySize / 1024).toFixed(2)} KB`);
                this.info.push(`   - 模块化后: ${(totalSize / 1024).toFixed(2)} KB`);
                this.info.push(`   - 大小变化: ${sizeReduction > 0 ? '-' : '+'}${Math.abs(sizeReduction)}%`);
            }

        } catch (error) {
            this.errors.push(`✗ 性能监控失败: ${error.message}`);
        }
    }

    /**
     * 递归获取模块文件
     */
    getModuleFilesRecursively(dir) {
        let files = [];
        if (!fs.existsSync(dir)) return files;

        const items = fs.readdirSync(dir, { withFileTypes: true });

        for (const item of items) {
            const fullPath = path.join(dir, item.name);
            if (item.isDirectory()) {
                files = files.concat(this.getModuleFilesRecursively(fullPath));
            } else if (item.name.endsWith('.json')) {
                files.push(fullPath);
            }
        }

        return files;
    }

    /**
     * 检查文档结构完整性
     */
    verifyDocumentStructure() {
        this.info.push('\n=== 验证文档结构 ===');

        // 检查关键目录是否存在 (移除docs/features通用检查)
        const keyDirectories = [
            'docs/ai-memory',
            'docs/ai-memory/L1-core',
            'docs/ai-memory/L1-core/commands',
            'docs/ai-memory/L1-core/commands/core',
            'docs/ai-memory/L1-core/commands/architecture',
            'docs/ai-memory/L1-core/commands/quality',
            'docs/ai-memory/L1-core/commands/document',
            'docs/ai-memory/L1-core/commands/analysis',
            'docs/ai-memory/L2-context',
            'docs/ai-memory/L3-index',
            'docs/ai-memory/L3-index/feature-index',
            'docs/ai-memory/L3-index/feature-index/by-project',
            'docs/ai-memory/templates',
            'docs/ai-memory/tools',
            'docs/common'  // 权威性文档目录
        ];

        keyDirectories.forEach(dir => {
            const fullPath = path.join(this.rootPath, dir);
            if (fs.existsSync(fullPath)) {
                this.info.push(`✓ 目录存在: ${dir}`);
            } else {
                this.errors.push(`✗ 关键目录缺失: ${dir}`);
            }
        });

        // 检查关键文件是否存在
        const keyFiles = [
            'docs/feature-status.json',
            'docs/ai-memory/templates/feature-index-template.md',
            'docs/ai-memory/tools/verify-memory-system.js',
            'docs/ai-memory/tools/authority-rules.json',  // 权威性规则文件
            'docs/ai-memory/L1-core/attention-commands.json',
            'docs/ai-memory/L1-core/attention-commands-legacy.json'
        ];

        keyFiles.forEach(file => {
            const fullPath = path.join(this.rootPath, file);
            if (fs.existsSync(fullPath)) {
                this.info.push(`✓ 文件存在: ${file}`);
            } else {
                this.errors.push(`✗ 关键文件缺失: ${file}`);
            }
        });

        // 添加docs/features使用说明
        this.info.push('\n📝 重要说明:');
        this.info.push('   - docs/features是临时工作区域，仅用于当前功能开发索引');
        this.info.push('   - docs/features不应作为历史参考材料');
        this.info.push('   - 历史功能信息应通过记忆索引系统访问');
        this.info.push('   - 只有active_development状态的功能才会验证原始文档存在性');
        this.info.push('   - 模块化架构已建立三级加载机制：immediate → context → on-demand');
    }

    /**
     * 执行权威性验证
     */
    async verifyAuthority() {
        this.info.push('\n=== 权威性验证 ===');

        if (!this.authorityValidator) {
            this.warnings.push('⚠ 权威性验证器不可用，跳过权威性验证');
            return;
        }

        try {
            const authorityReport = await this.authorityValidator.validateAll();

            // 整合权威性验证结果
            if (authorityReport.summary.failed > 0) {
                this.errors.push(`✗ 权威性验证失败: ${authorityReport.summary.failed} 个文件未通过验证`);
                authorityReport.details.failed.forEach(failure => {
                    this.errors.push(`  - ${failure.file}: ${failure.issues.join(', ')}`);
                });
            } else {
                this.info.push(`✓ 权威性验证通过: ${authorityReport.summary.passed} 个文件验证成功`);
            }

            if (authorityReport.summary.warnings > 0) {
                this.warnings.push(`⚠ 权威性验证警告: ${authorityReport.summary.warnings} 个警告`);
                authorityReport.details.warnings.forEach(warning => {
                    this.warnings.push(`  - ${warning.file}: ${warning.issue}`);
                });
            }

            this.info.push(`📊 权威性验证统计: 总计 ${authorityReport.summary.total_files} 个文件`);

        } catch (error) {
            this.errors.push(`✗ 权威性验证过程出错: ${error.message}`);
        }
    }

    /**
     * 快速检查 (实现@MEMORY_STATUS_QUICK_CHECK功能)
     */
    quickCheck(featureId = null) {
        this.info.push('\n=== 快速状态检查 ===');

        if (featureId) {
            // 检查特定功能状态
            const feature = this.featureStatus.feature_status_registry.features[featureId];
            if (!feature) {
                this.warnings.push(`⚠ 功能 ${featureId} 在状态注册表中不存在，建议确认功能ID`);
                return;
            }

            this.info.push(`📋 功能 ${featureId} 快速状态:`);
            this.info.push(`   - 功能状态: ${feature.status}`);
            this.info.push(`   - 同步状态: ${feature.sync_status}`);

            // 根据状态给出建议
            if (feature.status === 'active_development') {
                this.info.push(`✓ 建议: 优先使用原始功能文档 (${feature.original_doc_path})`);
            } else if (feature.sync_status === 'requires_update') {
                this.warnings.push(`⚠ 建议: 记忆索引需要更新，请考虑同步`);
            } else {
                this.info.push(`✓ 建议: 可以安全使用记忆索引文档`);
            }
        } else {
            // 全局快速检查
            const features = this.featureStatus.feature_status_registry.features;
            const activeDevelopment = Object.values(features).filter(f => f.status === 'active_development');
            const needsUpdate = Object.values(features).filter(f => f.sync_status === 'requires_update');

            this.info.push(`📊 全局状态概览:`);
            this.info.push(`   - 活跃开发功能: ${activeDevelopment.length} 个`);
            this.info.push(`   - 需要更新功能: ${needsUpdate.length} 个`);

            if (activeDevelopment.length > 0) {
                this.info.push(`📋 活跃开发功能 (建议使用原始文档):`);
                activeDevelopment.forEach(feature => {
                    const featureId = Object.keys(features).find(id => features[id] === feature);
                    this.info.push(`   - ${featureId}: ${feature.name}`);
                });
            }

            if (needsUpdate.length > 0) {
                this.warnings.push(`⚠ 需要更新的功能:`);
                needsUpdate.forEach(feature => {
                    const featureId = Object.keys(features).find(id => features[id] === feature);
                    this.warnings.push(`   - ${featureId}: ${feature.name}`);
                });
            }
        }
    }

    /**
     * 生成同步状态报告
     */
    generateSyncReport() {
        this.info.push('\n=== 同步状态报告 ===');
        
        const features = this.featureStatus.feature_status_registry.features;
        const statusCount = {};
        const syncStatusCount = {};

        Object.values(features).forEach(feature => {
            statusCount[feature.status] = (statusCount[feature.status] || 0) + 1;
            syncStatusCount[feature.sync_status] = (syncStatusCount[feature.sync_status] || 0) + 1;
        });

        this.info.push('📊 功能状态分布:');
        Object.entries(statusCount).forEach(([status, count]) => {
            this.info.push(`   - ${status}: ${count}`);
        });

        this.info.push('📊 同步状态分布:');
        Object.entries(syncStatusCount).forEach(([syncStatus, count]) => {
            this.info.push(`   - ${syncStatus}: ${count}`);
        });

        // 找出需要注意的功能
        const needAttention = Object.entries(features).filter(([id, feature]) => {
            return feature.sync_status === 'requires_update' || feature.sync_status === 'sync_failed';
        });

        if (needAttention.length > 0) {
            this.info.push('\n⚠️ 需要注意的功能:');
            needAttention.forEach(([id, feature]) => {
                this.info.push(`   - ${id}: ${feature.sync_status} (${feature.name})`);
            });
        }
    }

    /**
     * 输出结果报告
     */
    printReport() {
        console.log('\n' + '='.repeat(60));
        console.log('               记忆系统验证报告');
        console.log('='.repeat(60));

        if (this.info.length > 0) {
            console.log('\n📋 信息:');
            this.info.forEach(msg => console.log(msg));
        }

        if (this.warnings.length > 0) {
            console.log('\n⚠️ 警告:');
            this.warnings.forEach(msg => console.log(msg));
        }

        if (this.errors.length > 0) {
            console.log('\n❌ 错误:');
            this.errors.forEach(msg => console.log(msg));
        }

        console.log('\n' + '='.repeat(60));
        console.log(`总结: ${this.errors.length} 个错误, ${this.warnings.length} 个警告, ${this.info.length} 条信息`);
        
        if (this.errors.length === 0 && this.warnings.length === 0) {
            console.log('🎉 记忆系统验证通过！');
        } else if (this.errors.length === 0) {
            console.log('✅ 记忆系统基本正常，但有一些警告需要关注');
        } else {
            console.log('❌ 记忆系统存在问题，需要修复');
        }
        console.log('='.repeat(60));

        return this.errors.length === 0;
    }

    /**
     * 运行验证
     */
    async run(options = {}) {
        try {
            if (options.quickCheck) {
                // 快速检查模式
                this.quickCheck(options.feature);
            } else if (options.checkAuthority) {
                // 权威性验证模式
                await this.verifyAuthority();
            } else if (options.feature) {
                // 单个功能验证
                this.verifyFeature(options.feature);
            } else if (options.moduleIntegrity) {
                // 模块完整性验证 (Phase 4 新增)
                this.verifyDocumentStructure();
                this.verifyModuleIntegrity();
                this.verifyThreeTierArchitecture();
            } else if (options.performance) {
                // 性能监控 (Phase 4 新增)
                this.performanceMonitoring();
            } else {
                // 完整验证 (包含 Phase 4 新功能)
                this.verifyDocumentStructure();
                this.verifyModuleIntegrity();
                this.verifyThreeTierArchitecture();
                this.verifyAllFeatures();

                if (options.checkSync || options.fullReport) {
                    this.generateSyncReport();
                }

                if (options.fullReport) {
                    await this.verifyAuthority();
                    this.performanceMonitoring();
                }
            }

            return this.printReport();
        } catch (error) {
            console.error('验证过程中发生错误:', error.message);
            return false;
        }
    }
}

// 命令行参数解析
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];

        if (arg === '--feature' && i + 1 < args.length) {
            options.feature = args[i + 1];
            i++;
        } else if (arg === '--check-sync') {
            options.checkSync = true;
        } else if (arg === '--full-report') {
            options.fullReport = true;
        } else if (arg === '--check-authority') {
            options.checkAuthority = true;
        } else if (arg === '--quick-check') {
            options.quickCheck = true;
        } else if (arg === '--module-integrity') {
            options.moduleIntegrity = true;
        } else if (arg === '--performance') {
            options.performance = true;
        } else if (arg === '--help' || arg === '-h') {
            options.help = true;
        }
    }

    return options;
}

// 显示帮助信息
function showHelp() {
    console.log(`
记忆系统验证工具 (Phase 4 模块化版)

用法: node verify-memory-system.js [选项]

核心选项:
  --feature <ID>     验证特定功能
  --module-integrity 验证模块化架构 (推荐)
  --performance      性能监控分析
  --full-report      完整验证报告
  --quick-check      快速状态检查

常用示例:
  node verify-memory-system.js                    # 完整验证
  node verify-memory-system.js --module-integrity # 模块化验证
  node verify-memory-system.js --feature F003     # 特定功能

Phase 4 特性: 三级加载架构 (immediate → context → on-demand)
工具生态: 与 unified-validator.js 协同工作，提供专业化验证
`);
}

// 主函数
async function main() {
    const options = parseArgs();

    if (options.help) {
        showHelp();
        return;
    }

    console.log('🔍 启动记忆系统验证...');

    const verifier = new MemorySystemVerifier();
    const success = await verifier.run(options);

    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = MemorySystemVerifier; 