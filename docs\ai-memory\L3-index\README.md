# L3-Index 功能文档管理说明

## 架构定位

**L3-Index 是功能文档的主要存储位置**，提供完整的功能信息管理和智能访问。

### 文档存储策略

1. **L3-index/feature-index/** - 功能文档的主要存储位置
   - 包含完整的功能信息：基本信息、实现细节、使用指南、集成信息、测试信息
   - 按项目分组：`by-project/xkc-core/`, `by-project/xkc-uid/`, `by-project/xkc-center/`
   - 使用标准化模板确保信息完整性

2. **docs/features/** - 临时工作区域
   - 开发过程中的草稿和临时文件
   - 实验性文档和开发笔记
   - 完成后的内容整合到L3-index

### 访问协议

#### 主要访问方式
```
@L3:feature:{ID}  # 访问指定功能的完整文档
```

#### 项目映射访问
```
项目代码 → 功能ID → L3-index文档
XKC-CORE → F003 → L3-index/feature-index/by-project/xkc-core/F003-PostgreSQL迁移.md
XKC-UID → F004 → L3-index/feature-index/by-project/xkc-uid/feature-F004.md
```

#### 后备访问
- 当AI记忆系统不可用时，直接访问 `docs/features/`
- 使用 `docs/feature-status.json` 进行状态查询

### 功能状态管理

| 状态 | 文档优先级 | 同步策略 |
|------|------------|----------|
| active_development | L3-index为主，docs/features为辅助工作区 | 重要更新后手动同步 |
| stable_maintenance | L3-index为主要参考 | 状态变更时同步 |
| completed_archived | L3-index为唯一参考 | 无需同步 |

### 注意力命令集成

L3-index与注意力命令系统深度集成：

- **自动激活**: 基于关键词和任务类型自动激活相关功能文档
- **智能路由**: 根据项目代码和功能ID智能路由到正确的文档
- **上下文感知**: 结合RIPER-5模式提供最相关的文档内容

### 维护原则

1. **简化优先**: 避免复杂的同步状态管理
2. **内容完整**: L3-index文档包含完整功能信息，不仅仅是索引
3. **智能访问**: 通过注意力命令和项目映射实现智能文档发现
4. **向后兼容**: 保持与现有文档系统的兼容性

### 模板使用

使用 `docs/ai-memory/templates/feature-index-template.md` 创建新的功能文档：

1. 复制模板到对应项目目录
2. 替换所有 `{变量}` 为实际内容
3. 更新 `docs/feature-status.json` 状态信息
4. 验证注意力命令可以正确访问

这种架构简化了文档管理复杂性，同时保持了功能的完整性和智能访问能力。
