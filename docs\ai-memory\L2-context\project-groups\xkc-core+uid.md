# XKC-CORE与XKC-UID集成上下文

## 背景

XKC-CORE与XKC-UID的集成是XKongCloud架构中的关键组成部分，涉及分布式ID生成、数据库共享、配置管理等多个层面。这种集成采用门面模式简化复杂性，通过配置驱动实现灵活性，是演进架构设计的重要实践。

### 集成挑战
- 两个独立项目的生命周期协调
- 共享数据库资源的管理
- 配置参数的一致性保证
- 错误处理和恢复策略的统一

### 解决方案
- 采用门面模式隐藏集成复杂性
- 使用配置驱动架构支持灵活部署
- 建立统一的错误处理机制
- 实现自动化的测试策略

本文档提供了XKongCloud-Business-Internal-Core(XKC-CORE)与XKongCloud-Commons-UID(XKC-UID)集成的上下文信息，帮助理解两个项目之间的交互和集成方式。

## 功能概述

XKC-CORE与XKC-UID集成的主要目的是为XKC-CORE提供分布式唯一ID生成能力。这种集成使得XKC-CORE中的实体对象可以获取全局唯一的ID，确保在分布式环境中数据的一致性和完整性。

## 集成架构

XKC-CORE与XKC-UID之间采用依赖注入的方式进行集成，主要通过以下方式：

1. **Maven依赖**：XKC-CORE在pom.xml中添加对XKC-UID的依赖
2. **配置类集成**：XKC-CORE创建UidGeneratorConfig配置类，整合XKC-UID功能
3. **数据源共享**：XKC-CORE将其数据源传递给XKC-UID，用于表管理和工作节点注册
4. **KV参数服务**：通过XKC-CORE的KVParamService获取UID生成器配置参数

## 门面模式集成

XKC-CORE与XKC-UID的集成采用门面模式，通过UidGeneratorFacade简化配置和使用。

### UidGeneratorConfig类

```java
@Configuration
@DependsOn("kvParamService")
public class UidGeneratorConfig {

    @Autowired
    private KVParamService kvParamService;

    @Autowired
    private DataSource dataSource;

    @Value("${xkong.kv.cluster-id}")
    private String clusterId;

    @Bean
    @Primary
    public UidGeneratorFacade uidGeneratorFacade() {
        // 使用构建器模式创建UidGeneratorFacade，所有参数从KV服务获取
        UidGeneratorFacade facade = new UidGeneratorFacadeBuilder()
            .withDataSource(dataSource)
            .withSchemaName(getRequiredParam("uid.schema.name"))
            .withApplicationName(clusterId)
            .withEnvironment(getRequiredParam("uid.instance.environment"))
            .withInstanceGroup(getRequiredParam("uid.instance.group"))
            .withLocalStoragePath(getRequiredParam("uid.instance.local-storage-path"))
            .withRecoveryEnabled(getBooleanParam("uid.instance.recovery.enabled"))
            .withHighConfidenceThreshold(getIntParam("uid.instance.recovery.high-confidence-threshold"))
            .withMinimumAcceptableScore(getIntParam("uid.instance.recovery.minimum-acceptable-score"))
            .withRecoveryStrategy(getRequiredParam("uid.instance.recovery.strategy"))
            .withRecoveryTimeoutSeconds(getIntParam("uid.instance.recovery.timeout-seconds"))
            .withEncryptionEnabled(getBooleanParam("uid.instance.encryption.enabled"))
            .withLeaseDurationSeconds(getIntParam("uid.worker.lease-duration-seconds"))
            .withEpochStr(getRequiredParam("uid.epochStr"))
            .withTimeBits(getIntParam("uid.timeBits"))
            .withWorkerBits(getIntParam("uid.workerBits"))
            .withSeqBits(getIntParam("uid.seqBits"))
            .withBoostPower(getIntParam("uid.boostPower"))
            .withPaddingFactor(getIntParam("uid.paddingFactor"))
            .withScheduleInterval(getLongParam("uid.scheduleInterval"))
            .build();

        return facade;
    }

    // UidGenerator适配器Bean和辅助方法...
}
```

### 实体类ID生成

```java
@Service
public class UserService {
    @Autowired
    private UidGeneratorFacade uidGeneratorFacade;
    
    @Autowired
    private UserRepository userRepository;
    
    public User createUser(String username, String email) {
        User user = new User();
        // 使用UID生成器获取唯一ID
        user.setUserId(uidGeneratorFacade.getUID());
        user.setUsername(username);
        user.setEmail(email);
        user.setRegistTime(LocalDateTime.now());
        
        return userRepository.save(user);
    }
    
    // 其他方法...
}
```

## 数据库交互

XKC-UID使用XKC-CORE提供的数据源进行数据库操作，主要涉及以下表：

1. **infra_uid.worker_node表**：存储工作节点信息
   ```sql
   CREATE TABLE infra_uid.worker_node (
       id BIGINT PRIMARY KEY,
       host_name VARCHAR(64) NOT NULL,
       port VARCHAR(64) NOT NULL,
       type INT NOT NULL,
       launch_date DATE NOT NULL,
       modified TIMESTAMP NOT NULL,
       created TIMESTAMP NOT NULL
   );
   ```

2. **infra_uid.instance_registry表**：存储实例注册信息
   ```sql
   CREATE TABLE infra_uid.instance_registry (
       id BIGINT PRIMARY KEY,
       instance_id VARCHAR(128) NOT NULL,
       application_name VARCHAR(128) NOT NULL,
       environment VARCHAR(32) NOT NULL,
       instance_group VARCHAR(128) NOT NULL,
       hostname VARCHAR(128) NOT NULL,
       ip_address VARCHAR(64) NOT NULL,
       port INT NOT NULL,
       machine_fingerprints TEXT,
       status VARCHAR(32) NOT NULL,
       worker_id BIGINT,
       worker_assignment_time TIMESTAMP,
       lease_expiration_time TIMESTAMP,
       created_time TIMESTAMP NOT NULL,
       last_updated_time TIMESTAMP NOT NULL,
       CONSTRAINT uk_instance_id UNIQUE (instance_id)
   );
   ```

## 配置参数

XKC-CORE与XKC-UID集成涉及的关键配置参数包括：

| 参数名 | 说明 | 示例值 | 配置位置 |
|-------|------|-------|--------|
| `uid.schema.name` | UID表所在的Schema名称 | `infra_uid` | KV参数服务 |
| `uid.epochStr` | 时间基点 | `2025-01-01` | KV参数服务 |
| `uid.timeBits` | 时间戳位数 | `31` | KV参数服务 |
| `uid.workerBits` | 工作机器ID位数 | `18` | KV参数服务 |
| `uid.seqBits` | 序列号位数 | `14` | KV参数服务 |
| `uid.instance.environment` | 环境标识 | `dev`/`prod` | KV参数服务 |
| `uid.instance.group` | 实例组标识 | `development`/`production` | KV参数服务 |
| `uid.instance.recovery.strategy` | 恢复策略 | `ALERT_AUTO_WITH_TIMEOUT` | KV参数服务 |

## 生命周期管理

XKC-CORE负责管理XKC-UID组件的生命周期，确保资源的正确创建和释放：

1. **初始化顺序**：
   - PostgreSQLConfig初始化数据源
   - UidGeneratorConfig初始化UID生成器
   - UidGeneratorFacade自动创建并管理所需的表

2. **关闭顺序**：
   - UidGeneratorFacade在应用关闭前先关闭
   - 数据源在UidGeneratorFacade之后关闭

```java
@Bean
@DependsOn("uidGeneratorFacade")
public UidShutdownOrderBean uidShutdownOrder(UidGeneratorFacade facade) {
    return new UidShutdownOrderBean(facade);
}

public static class UidShutdownOrderBean implements DisposableBean {
    private final UidGeneratorFacade facade;

    public UidShutdownOrderBean(UidGeneratorFacade facade) {
        this.facade = facade;
    }

    @Override
    @PreDestroy
    public void destroy() {
        if (facade != null) {
            facade.close();
        }
    }
}
```

## 错误处理策略

XKC-CORE与XKC-UID集成中的错误处理策略包括：

1. **参数验证错误**：
   - 缺少必需参数时，应用启动失败，提供明确的错误信息
   - 参数格式错误时，应用启动失败，提供格式修正建议

2. **数据库连接错误**：
   - 记录详细的异常信息
   - 应用启动失败，要求先解决数据库连接问题

3. **实例ID恢复错误**：
   - 根据配置的恢复策略处理
   - 告警并记录日志
   - 可能创建新实例或尝试匹配最佳实例

4. **工作机器ID分配错误**：
   - 记录错误信息
   - 重试分配
   - 如果多次重试失败，应用启动失败

## 测试策略

XKC-CORE与XKC-UID集成的测试策略包括：

1. **单元测试**：
   - 测试UidGeneratorConfig配置类
   - 测试参数验证逻辑
   - 测试适配器兼容性

2. **集成测试**：
   - 测试ID生成功能
   - 测试批量ID生成
   - 测试实例恢复功能
   - 测试资源关闭顺序

3. **性能测试**：
   - 测试单线程ID生成速率
   - 测试多线程并发生成
   - 测试在高负载下的性能表现

## 常见问题和解决方案

1. **WorkerId分配冲突**：
   - 问题：多个实例尝试分配相同的WorkerId
   - 解决方案：使用数据库事务和乐观锁确保分配的原子性

2. **实例恢复失败**：
   - 问题：特征码匹配度不够，无法恢复实例
   - 解决方案：调整匹配阈值，或使用ALERT_AUTO_WITH_TIMEOUT策略自动创建新实例

3. **ID耗尽风险**：
   - 问题：序列号位数不足，可能导致ID耗尽
   - 解决方案：调整UID参数配置，增加序列号位数或减少时间戳精度

4. **表自动创建失败**：
   - 问题：缺少创建表的权限
   - 解决方案：确保数据库用户有适当的表创建权限，或手动创建所需的表 