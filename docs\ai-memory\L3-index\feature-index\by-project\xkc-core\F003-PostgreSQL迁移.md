# 功能索引：F003-PostgreSQL迁移

## 基本信息

- **功能ID**: F003
- **功能名称**: PostgreSQL迁移
- **所属项目**: xkongcloud-business-internal-core (XKC-CORE)
- **开始日期**: 2025-05-08
- **完成状态**: 进行中
- **当前阶段**: 第二阶段实施中
- **负责人**: AI助手

## 同步状态

- **索引创建日期**: 2025-01-15
- **最后同步日期**: 2025-05-28
- **同步来源**: docs/features/F003-PostgreSQL迁移-20250508/
- **功能状态**: active_development
- **同步状态**: up_to_date
- **同步检查频率**: 每次重要更新后
- **优先级**: high

> **⚠️ 重要提醒**: 此索引文档为AI记忆辅助文档，信息时效性取决于同步状态。
> 
> - **当前功能状态为"active_development"**: 请**优先参考**原始功能文档 `docs/features/F003-PostgreSQL迁移-20250508/` 获取最新信息
> - **当前同步状态为"up_to_date"**: 此索引信息与原始文档基本同步，可作为主要参考
> 
> 使用命令 `@sync:feature:F003` 可手动触发索引同步更新。

## 功能描述

PostgreSQL迁移功能旨在将XKongCloud项目的数据存储从Cassandra迁移到PostgreSQL，以提高数据一致性、查询灵活性和开发效率。该功能包括基础设施准备、公共库开发、数据访问层重构和数据迁移四个阶段。

## 实施计划

PostgreSQL迁移功能分为四个阶段实施：

1. **第一阶段：基础设施准备**
   - PostgreSQL环境配置
   - 数据库用户和权限配置
   - Schema创建和命名规范实施
   - KV参数配置

2. **第二阶段：xkongcloud-commons-uid公共库开发**
   - 基于百度UidGenerator实现持久化工作节点ID分配
   - 实现机器指纹特征码生成和匹配
   - 实现实例恢复策略
   - 实现门面模式和构建器模式

3. **第三阶段：数据访问层重构**
   - 创建PostgreSQL配置类
   - JPA实体类映射
   - Repository接口实现
   - 事务管理和批量操作

4. **第四阶段：数据迁移和切换**
   - 数据结构映射
   - 增量同步机制
   - 数据验证和一致性检查
   - 生产环境切换

## 相关文档

### 计划文档

- [实施计划](docs/features/F003-PostgreSQL迁移-20250508/plan/implementation-plan.md)
- [第一阶段详细实施指南](docs/features/F003-PostgreSQL迁移-20250508/plan/phase1-detailed-implementation-guide.md)
- [第二阶段实施计划](docs/features/F003-PostgreSQL迁移-20250508/plan/phase2-implementation-plan.md)
- [第三阶段实施计划](docs/features/F003-PostgreSQL迁移-20250508/plan/phase3-implementation-plan.md)

### 设计文档

- [Schema规划指南](docs/common/middleware/postgresql/schema-planning-guide.md)
- [百度UidGenerator PostgreSQL实现](docs/common/middleware/integration/baidu-uid-generator-postgresql-implementation.md)

### 相关功能

- [F004-CommonsUidLibrary](docs/features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md)

## 主要组件

### PostgreSQLConfig

PostgreSQLConfig类负责配置PostgreSQL数据库连接和JPA实体管理器工厂，主要功能包括：

- 从KVParamService获取数据库连接参数
- 配置HikariCP连接池
- 配置JPA和Hibernate参数
- 创建实体管理器工厂和事务管理器
- 配置命名策略

```java
@Configuration
@DependsOn("kvParamService")
public class PostgreSQLConfig {
    // 数据源配置
    @Bean
    @Primary
    public DataSource dataSource() { ... }
    
    // 实体管理器工厂配置
    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() { ... }
    
    // 事务管理器配置
    @Bean
    public PlatformTransactionManager transactionManager() { ... }
    
    // 命名策略配置
    public static class PostgreSQLNamingStrategy implements PhysicalNamingStrategy { ... }
}
```

### UidGeneratorConfig

UidGeneratorConfig类负责配置百度UidGenerator，通过门面模式简化配置和使用，主要功能包括：

- 从KVParamService获取UID生成器参数
- 使用UidGeneratorFacadeBuilder创建UidGeneratorFacade实例
- 配置持久化实例ID和特征码恢复
- 管理UID生成器组件的生命周期

```java
@Configuration
@DependsOn("kvParamService")
public class UidGeneratorConfig {
    @Bean
    @Primary
    public UidGeneratorFacade uidGeneratorFacade() { ... }
    
    @Bean
    public UidGenerator uidGenerator(UidGeneratorFacade facade) { ... }
    
    @Bean
    @DependsOn("uidGeneratorFacade")
    public UidShutdownOrderBean uidShutdownOrder(UidGeneratorFacade facade) { ... }
}
```

### UidGeneratorFacade

UidGeneratorFacade类是xkongcloud-commons-uid公共库的核心组件，实现门面模式隐藏内部复杂性，主要功能包括：

- 提供简单的API获取UID
- 管理内部组件的生命周期
- 自动处理表创建和工作节点ID分配
- 实现实例ID持久化和恢复

```java
public class UidGeneratorFacade implements AutoCloseable {
    private final CachedUidGenerator generator;
    private final WorkerIdAssigner assigner;
    private final PersistentInstanceManager instanceManager;
    
    // 获取UID
    public long getUID() { ... }
    
    // 批量获取UID
    public long[] getUIDBatch(int size) { ... }
    
    // 关闭资源
    @Override
    public void close() { ... }
}
```

### PersistentInstanceManager

PersistentInstanceManager类负责管理应用实例的持久化和恢复，主要功能包括：

- 生成和存储唯一实例ID
- 收集机器指纹特征码
- 实现特征码匹配算法
- 支持多种恢复策略

```java
public class PersistentInstanceManager {
    // 初始化实例ID
    public String initializeInstanceId() { ... }
    
    // 注册实例
    public InstanceRegistration registerInstance() { ... }
    
    // 寻找匹配实例
    public Optional<InstanceMatch> findMatchingInstance() { ... }
    
    // 存储实例ID
    public void storeInstanceId(String instanceId) { ... }
}
```

## 核心特性

1. **多Schema组织架构**
   - 业务Schema使用`<业务领域>_<可选子域>`格式命名
   - 基础设施Schema使用`infra_<组件类型>`格式命名
   - 通用功能Schema使用`common_<功能类型>`格式命名

2. **门面模式和构建器模式集成**
   - 使用UidGeneratorFacade简化配置和使用
   - 使用UidGeneratorFacadeBuilder支持链式调用和参数验证

3. **实例恢复机制**
   - 基于机器指纹特征码的实例匹配
   - 支持多种恢复策略：告警并创建新实例、自动匹配最佳实例
   - 特征码加权匹配算法

4. **配置驱动架构**
   - 从KV参数服务获取配置
   - 参数化配置，支持不同环境和场景
   - 强制参数验证，确保配置正确性

## 当前状态和下一步

目前，PostgreSQL迁移功能已完成第一阶段（基础设施准备），正在进行第二阶段（xkongcloud-commons-uid公共库开发）。下一步将完成以下工作：

1. 完成xkongcloud-commons-uid公共库开发
2. 实现UidGeneratorFacade门面类和UidGeneratorFacadeBuilder构建器类
3. 实现PersistentInstanceManager和PersistentInstanceWorkerIdAssigner类
4. 开始第三阶段数据访问层重构

## 相关约束和注意事项

1. **实体类Schema指定强制约束**
   - 所有实体类必须使用`@Table(name = "表名", schema = "schema名")`明确指定Schema
   - 不允许依赖任何默认Schema设置

2. **门面模式强制使用**
   - 必须通过UidGeneratorFacade使用UID库的所有功能
   - 禁止直接创建或使用内部组件

3. **参数验证要求**
   - 所有从KV参数服务获取的参数必须进行非空验证
   - 缺少必需参数时应用应无法启动，并显示明确的错误信息

4. **生产环境差异**
   - 生产环境必须使用`postgresql.ddl-auto=validate`或`none`
   - 生产环境必须启用`uid.instance.encryption.enabled=true`
   - 生产环境应使用`uid.instance.recovery.strategy=ALERT_AUTO_WITH_TIMEOUT`

## 相关资源和文档

### 技术文档
1. [PostgreSQL技术栈上下文](docs/ai-memory/L2-context/tech-stack/postgresql-stack.md)
2. [数据库任务上下文](docs/ai-memory/L2-context/task-types/database-tasks.md)
3. [全局硬约束](docs/ai-memory/L1-core/global-constraints.md)

### 设计指南
1. [Schema规划指南](docs/common/middleware/postgresql/schema-planning-guide.md)
2. [百度UidGenerator PostgreSQL实现](docs/common/middleware/integration/baidu-uid-generator-postgresql-implementation.md)

### 相关功能
1. [F004-CommonsUidLibrary索引](docs/ai-memory/L3-index/feature-index/by-project/xkc-uid/feature-F004.md)
2. [XKC-CORE与XKC-UID依赖关系](docs/ai-memory/L3-index/dependency-index/XKC-CORE-UID.md) 