/**
 * 统一数据管理器 - 负责所有组件的数据获取、缓存和实时更新
 */
class DataManager {
    constructor() {
        this.data = new Map();
        this.subscribers = new Map();
        this.wsClient = null;
        this.httpClient = new HttpClient();
        this.cache = new Map();
        this.cacheTimeout = new Map();

        // 任务驱动模式：使用task_id而非project_path
        this.currentTaskId = null;
        // 移除currentProjectPath属性，保持纯任务驱动架构

        this.setupWebSocket();
    }
    
    /**
     * 统一的数据获取方法
     * @param {string} dataType - 数据类型
     * @param {object} params - 请求参数
     * @param {boolean} useCache - 是否使用缓存
     * @returns {Promise} 数据Promise
     */
    async fetchData(dataType, params = {}, useCache = true) {
        const cacheKey = `${dataType}_${JSON.stringify(params)}`;

        // 检查缓存
        if (useCache && this.cache.has(cacheKey)) {
            const cachedData = this.cache.get(cacheKey);
            this.setData(cacheKey, cachedData);
            return cachedData;
        }

        // 获取数据类型配置
        const config = this.getDataTypeConfig(dataType);

        // 如果有Mock数据且在开发模式下，优先使用Mock数据
        if (config.mockData && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
            console.log(`Using mock data for ${dataType}`);
            const data = config.mockData;
            this.setData(dataType, data);

            // 设置缓存
            if (useCache && config.cache) {
                this.setCache(cacheKey, data, config.cacheTTL || 60000);
            }

            return data;
        }

        const endpoint = this.getEndpoint(dataType);
        if (!endpoint) {
            throw new Error(`Unknown data type: ${dataType}`);
        }

        try {
            console.log(`Fetching ${dataType} from ${endpoint}`);
            const response = await this.httpClient.get(endpoint, params);

            if (response.success) {
                const data = response.data;
                this.setData(dataType, data);

                // 设置缓存
                if (useCache && config.cache) {
                    this.setCache(cacheKey, data, config.cacheTTL || 60000);
                }

                return data;
            } else {
                throw new Error(response.message || 'Request failed');
            }
        } catch (error) {
            console.error(`Failed to fetch ${dataType}:`, error);

            // 如果API请求失败且有Mock数据，回退到Mock数据
            if (config.mockData) {
                console.warn(`API failed, falling back to mock data for ${dataType}`);
                const data = config.mockData;
                this.setData(dataType, data);
                return data;
            }

            throw error;
        }
    }
    
    /**
     * 统一的数据设置方法
     * @param {string} key - 数据键
     * @param {any} value - 数据值
     */
    setData(key, value) {
        const oldValue = this.data.get(key);
        this.data.set(key, value);
        
        // 通知所有订阅者
        const subscribers = this.subscribers.get(key) || [];
        subscribers.forEach(callback => {
            try {
                callback(value, oldValue);
            } catch (error) {
                console.error('Subscriber error:', error);
            }
        });
        
        console.log(`Data updated: ${key}`, value);
    }
    
    /**
     * 统一的数据订阅方法
     * @param {string} key - 数据键
     * @param {function} callback - 回调函数
     * @returns {function} 取消订阅函数
     */
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, []);
        }
        this.subscribers.get(key).push(callback);
        
        // 如果已有数据，立即调用回调
        if (this.data.has(key)) {
            try {
                callback(this.data.get(key));
            } catch (error) {
                console.error('Initial callback error:', error);
            }
        }
        
        // 返回取消订阅函数
        return () => {
            const subscribers = this.subscribers.get(key);
            if (subscribers) {
                const index = subscribers.indexOf(callback);
                if (index > -1) {
                    subscribers.splice(index, 1);
                }
            }
        };
    }
    
    /**
     * 获取数据
     * @param {string} key - 数据键
     * @returns {any} 数据值
     */
    getData(key) {
        return this.data.get(key);
    }
    
    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间(毫秒)
     */
    setCache(key, value, ttl = 60000) {
        this.cache.set(key, value);
        
        // 清除旧的超时
        if (this.cacheTimeout.has(key)) {
            clearTimeout(this.cacheTimeout.get(key));
        }
        
        // 设置新的超时
        const timeoutId = setTimeout(() => {
            this.cache.delete(key);
            this.cacheTimeout.delete(key);
        }, ttl);
        
        this.cacheTimeout.set(key, timeoutId);
    }
    
    /**
     * 获取缓存TTL
     * @param {string} dataType - 数据类型
     * @returns {number} TTL毫秒数
     */
    getCacheTTL(dataType) {
        const ttlMap = {
            'progress': 30000,        // 30秒
            'risk_assessment': 60000, // 1分钟
            'constraints': 120000,    // 2分钟
            'knowledge_graph': 120000,// 2分钟
            'manager_status': 10000,  // 10秒
            'algorithm_logs': 5000,   // 5秒
            'deliverables': 60000     // 1分钟
        };
        return ttlMap[dataType] || 60000;
    }
    
    /**
     * 设置PM-V2标准WebSocket连接
     */
    setupWebSocket() {
        // PM-V2标准连接URL
        const wsURL = `ws://localhost:25526/ws/pm-v2`;
        console.log(`🔗 Connecting to PM-V2 WebSocket: ${wsURL}`);

        // 使用Socket.IO客户端连接到PM-V2命名空间
        this.wsClient = io('/ws/pm-v2', {
            auth: {
                Authorization: `Bearer ${this.getAuthToken()}`
            },
            transports: ['websocket'],
            upgrade: false
        });

        // 连接成功事件
        this.wsClient.on('connect', () => {
            console.log('✅ PM-V2 WebSocket connected');
            this.isConnected = true;

            // 启动心跳机制
            this.startHeartbeat();
        });

        // 连接建立确认
        this.wsClient.on('connection_established', (message) => {
            console.log('🎯 PM-V2连接建立确认:', message);
            this.handleStandardMessage(message);
        });



        // 审查进度更新
        this.wsClient.on('review_progress_update', (message) => {
            console.log('📊 审查进度更新:', message);
            this.handleStandardMessage(message);
        });

        // 审查阶段进度
        this.wsClient.on('review_stage_progress', (message) => {
            console.log('⚡ 审查阶段进度:', message);
            this.handleStandardMessage(message);
        });

        // 审查阶段完成
        this.wsClient.on('review_stage_completed', (message) => {
            console.log('✅ 审查阶段完成:', message);
            this.handleStandardMessage(message);
        });

        // 工作区创建
        this.wsClient.on('workspace_created', (message) => {
            console.log('🏗️ 工作区创建:', message);

            // 保存task_id到当前实例
            if (message.data && message.data.task_id) {
                this.currentTaskId = message.data.task_id;
                console.log('✅ 任务ID已保存:', this.currentTaskId);
            }

            this.handleStandardMessage(message);
        });

        // 审查启动
        this.wsClient.on('admission_review_started', (message) => {
            console.log('🚀 审查启动:', message);
            this.handleStandardMessage(message);
        });

        // 心跳响应
        this.wsClient.on('pong', (message) => {
            console.log('💓 心跳响应:', message);
            this.lastHeartbeatResponse = Date.now();
        });

        // 错误处理
        this.wsClient.on('error', (message) => {
            console.error('❌ PM-V2错误:', message);
            this.handleStandardMessage(message);
        });

        // 断开连接事件
        this.wsClient.on('disconnect', (reason) => {
            console.log('❌ PM-V2 WebSocket disconnected:', reason);
            this.isConnected = false;
            this.stopHeartbeat();

            // 自动重连
            setTimeout(() => this.setupWebSocket(), 5000);
        });

        // 连接错误
        this.wsClient.on('connect_error', (error) => {
            console.error('❌ PM-V2 WebSocket connection error:', error);
        });
    }
    
    /**
     * 处理PM-V2标准WebSocket消息
     * @param {object} message - 标准化WebSocket消息
     */
    handleStandardMessage(message) {
        const { type, data, timestamp, task_id, sequence } = message;
        console.log(`📨 PM-V2消息接收: ${type}`, {
            data,
            timestamp,
            task_id,
            sequence
        });

        // 验证消息格式
        if (!this.validateStandardMessage(message)) {
            console.warn('⚠️ 消息格式不符合PM-V2标准:', message);
            return;
        }

        // 根据事件类型更新对应的数据
        switch (type) {
            case 'review_stage_progress':
                this.updateData('review_progress', data);
                this.notifyComponents('review_stage_progress', data);
                break;
            case 'review_stage_completed':
                this.updateData('review_progress', data);
                this.notifyComponents('review_stage_completed', data);
                break;
            case 'review_completed':
                this.updateData('review_status', data);
                this.notifyComponents('review_completed', data);
                break;
            case 'workspace_created':
                this.updateData('workspace_info', data);
                this.notifyComponents('workspace_created', data);
                break;
            case 'admission_review_started':
                this.updateData('review_status', data);
                this.notifyComponents('admission_review_started', data);
                break;
            case 'error':
                console.error('❌ PM-V2错误消息:', data);
                this.notifyComponents('error', data);
                break;
            default:
                console.log(`🔍 未知PM-V2消息类型: ${type}`);
                this.notifyComponents('unknown_message', { type, data });
        }
    }

    /**
     * 验证PM-V2标准消息格式
     * @param {object} message - 消息对象
     * @returns {boolean} 是否符合标准
     */
    validateStandardMessage(message) {
        const requiredFields = ['type', 'data', 'timestamp'];
        // task_id是可选的，因为某些消息（如错误消息）可能不包含task_id
        return requiredFields.every(field => message.hasOwnProperty(field));
    }

    /**
     * 启动心跳机制（30秒间隔）
     */
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }

        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.wsClient) {
                console.log('💓 发送心跳ping');
                this.wsClient.emit('ping');

                // 检查心跳响应超时 (v4.2.1: 已移除 - 该逻辑过于严格且与服务器端不协调，完全信任服务器的ping/pong机制)
                /* setTimeout(() => {
                    const now = Date.now();
                    if (this.lastHeartbeatResponse && (now - this.lastHeartbeatResponse) > 35000) {
                        console.warn('⚠️ 心跳响应超时，重新连接');
                        this.wsClient.disconnect();
                    }
                }, 5000); */
            }
        }, 30000); // 30秒心跳间隔

        console.log('💓 心跳机制启动');
    }

    /**
     * 停止心跳机制
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
            console.log('💓 心跳机制停止');
        }
    }



    /**
     * 初始化工作区
     * @param {string} projectPath - 项目路径
     * 注意：这里只临时使用projectPath发送初始化请求，
     * 后续所有操作都应该基于从workspace_created事件中获取的task_id
     */
    initializeWorkspace(projectPath) {
        if (this.isConnected && this.wsClient) {
            console.log('🏗️ 初始化工作区:', projectPath);
            // 不保存projectPath作为长期状态，保持任务驱动架构的纯净性
            this.wsClient.emit('initialize_workspace', {
                project_path: projectPath
            });
        } else {
            console.warn('⚠️ WebSocket未连接，无法初始化工作区');
        }
    }

    /**
     * 启动审查任务
     * @param {string} taskId - 任务ID
     */
    startReviewTask(taskId) {
        if (this.isConnected && this.wsClient) {
            console.log('🚀 启动审查任务:', taskId);
            this.wsClient.emit('start_review_task', {
                task_id: taskId
            });
        } else {
            console.warn('⚠️ WebSocket未连接，无法启动审查任务');
        }
    }

    /**
     * 获取认证Token
     * @returns {string} 认证Token
     */
    getAuthToken() {
        // TODO: 实现实际的Token获取逻辑
        return 'pm_v2_demo_token_' + Date.now();
    }

    /**
     * 通知组件数据更新
     * @param {string} eventType - 事件类型
     * @param {object} data - 数据
     */
    notifyComponents(eventType, data) {
        // 触发自定义事件，让组件监听
        const event = new CustomEvent('pm_v2_data_update', {
            detail: {
                eventType,
                data,
                timestamp: new Date().toISOString()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 处理WebSocket消息（兼容旧版本）
     * @param {object} message - WebSocket消息
     */
    handleWebSocketMessage(message) {
        // 兼容旧版本消息格式，转换为标准格式
        console.warn('⚠️ 使用旧版本消息格式，建议升级到PM-V2标准');
        this.handleStandardMessage(message);
    }
    
    /**
     * 更新进度数据
     */
    updateProgressData(data) {
        const currentData = this.getData('progress') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('progress', updatedData);
    }
    
    /**
     * 更新阶段零指标
     */
    updateStageZeroMetrics(data) {
        const progressData = this.getData('progress') || {};
        progressData.stage_zero_metrics = { ...progressData.stage_zero_metrics, ...data };
        this.setData('progress', progressData);
    }
    
    /**
     * 更新关键指标
     */
    updateKeyMetrics(data) {
        const progressData = this.getData('progress') || {};
        progressData.key_metrics = { ...progressData.key_metrics, ...data };
        this.setData('progress', progressData);
    }
    
    /**
     * 更新风险评估数据
     */
    updateRiskAssessment(data) {
        const currentData = this.getData('risk_assessment') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('risk_assessment', updatedData);
    }
    
    /**
     * 更新约束数据
     */
    updateConstraintData(data) {
        // 更新约束列表
        const constraintsData = this.getData('constraints') || { constraints: [] };
        
        if (data.constraint) {
            const existingIndex = constraintsData.constraints.findIndex(
                c => c.id === data.constraint.id
            );
            
            if (existingIndex >= 0) {
                constraintsData.constraints[existingIndex] = data.constraint;
            } else {
                constraintsData.constraints.push(data.constraint);
            }
            
            this.setData('constraints', constraintsData);
            
            // 如果是当前选中的约束，也更新详情
            this.setData(`constraint_detail_${data.constraint.id}`, data.constraint);
        }
    }
    
    /**
     * 更新知识图谱数据
     */
    updateKnowledgeGraph(data) {
        const currentData = this.getData('knowledge_graph') || { nodes: [], connections: [] };
        
        if (data.operation === 'add_node' && data.node) {
            currentData.nodes.push(data.node);
            if (data.new_connections) {
                currentData.connections.push(...data.new_connections);
            }
        }
        
        this.setData('knowledge_graph', currentData);
    }
    
    /**
     * 更新管理器状态
     */
    updateManagerStatus(data) {
        const currentData = this.getData('manager_status') || {};
        const updatedData = { ...currentData, ...data };
        this.setData('manager_status', updatedData);
    }
    
    /**
     * 添加算法日志条目
     */
    addAlgorithmLogEntry(data) {
        const logsData = this.getData('algorithm_logs') || { process_logs: [] };
        logsData.process_logs.push(data);
        
        // 保持最近100条日志
        if (logsData.process_logs.length > 100) {
            logsData.process_logs.shift();
        }
        
        this.setData('algorithm_logs', logsData);
    }
    
    /**
     * 更新交付结果
     */
    updateDeliverables(data) {
        const currentData = this.getData('deliverables') || { output_files: [] };
        
        if (data.file) {
            const existingIndex = currentData.output_files.findIndex(
                f => f.id === data.file.id
            );
            
            if (existingIndex >= 0) {
                currentData.output_files[existingIndex] = data.file;
            } else {
                currentData.output_files.push(data.file);
            }
        }
        
        this.setData('deliverables', currentData);
    }
    
    /**
     * 获取API端点
     * @param {string} dataType - 数据类型
     * @returns {string} API端点
     */
    getEndpoint(dataType) {
        // 优先使用PM V2数据配置
        if (window.PM_V2_DATA_TYPE_MAPPING && window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
            return window.PM_V2_DATA_TYPE_MAPPING[dataType].endpoint;
        }

        // PM-V2架构下不再使用固定projectId端点
        console.warn(`⚠️ 数据类型 ${dataType} 未在PM-V2映射中找到，请检查配置`);
        return null;
    }

    /**
     * 获取数据类型的配置信息
     * @param {string} dataType - 数据类型
     * @returns {object} 配置信息
     */
    getDataTypeConfig(dataType) {
        if (window.PM_V2_DATA_TYPE_MAPPING && window.PM_V2_DATA_TYPE_MAPPING[dataType]) {
            return window.PM_V2_DATA_TYPE_MAPPING[dataType];
        }

        // 默认配置
        return {
            endpoint: this.getEndpoint(dataType),
            method: 'GET',
            cache: true,
            cacheTTL: 60000,
            mockData: null
        };
    }
    
    /**
     * 清理资源
     */
    destroy() {
        // 清理WebSocket连接
        if (this.wsClient) {
            this.wsClient.close();
            this.wsClient = null;
        }
        
        // 清理缓存超时
        this.cacheTimeout.forEach(timeoutId => clearTimeout(timeoutId));
        this.cacheTimeout.clear();
        
        // 清理数据
        this.data.clear();
        this.subscribers.clear();
        this.cache.clear();
        
        console.log('DataManager destroyed');
    }
}
