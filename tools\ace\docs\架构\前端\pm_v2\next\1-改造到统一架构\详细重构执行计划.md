# PM_V2 详细重构执行计划

## 1. 概述

本文档基于对 `project_manager_v2.html` 的深入分析和对统一架构规范的理解，为 PM_V2 的重构提供一份详细、可执行的步骤。计划的核心是将一个庞大的 HTML 文件拆分为多个独立、可维护的组件。

## 2. 总体迁移策略

我们将采用“逐个击破”的策略，为8个功能区域分别创建组件，并逐步将逻辑从旧代码迁移到新组件中。

```mermaid
graph TD
    A[开始: project_manager_v2.html] --> B{识别8个功能区域};
    B --> C1[创建 ProjectProgressComponent];
    B --> C2[创建 RiskAssessmentComponent];
    B --> C3[创建 ManagerStatusComponent];
    B --> C4[创建 AlgorithmMindsetComponent];
    B --> C5[创建 ConstraintReviewComponent];
    B --> C6[创建 KnowledgeBaseComponent];
    B --> C7[创建 HumanInputComponent];
    B --> C8[创建 ProjectOutputComponent];

    subgraph "组件化迁移过程"
        direction LR
        D1[迁移HTML] --> D2[定义数据类型] --> D3[改造渲染逻辑] --> D4[绑定事件] --> D5[处理兼容性];
    end

    C1 --> D1;
    C2 --> D1;
    C3 --> D1;
    C4 --> D1;
    C5 --> D1;
    C6 --> D1;
    C7 --> D1;
    C8 --> D1;

    D5 --> E[完成: 8个独立组件];
```

## 3. 组件拆分详细计划

### 3.1. 项目进度监控 (ProjectProgressComponent)

-   **新文件名**: `ProjectProgressComponent.js`
-   **目标容器 ID**: `progress-area` (已在高级计划中定义，对应HTML中的 `grid-area-1-2`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:1831`](tools/ace/src/web_interface/templates/project_manager_v2.html:1831) 到 [`project_manager_v2.html:1956`](tools/ace/src/web_interface/templates/project_manager_v2.html:1956) 的 `<div class="grid-area grid-area-1-2 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['progress', 'stage_metrics']`。
    -   数据来源: 对应于 `PM_V2_DATA_TYPE_MAPPING` 中的 `progress` 和 `stage_metrics`。
-   **渲染逻辑适配**:
    -   `render()`: 将迁移的 HTML 包装在此方法中。使用 `this.getData('progress')` 和 `this.getData('stage_metrics')` 来获取数据，并替换掉所有硬编码的值（如 `100%`, `25/25` 等）。
-   **事件绑定策略**:
    -   此区域目前没有复杂的 `onclick` 事件，主要是数据展示。`bindEvents()` 可以暂时为空。
-   **兼容性桥接**:
    -   需要确保 `renderProcessOverview` (如果旧代码中存在此函数) 的逻辑被完全吸收或废弃。

### 3.2. 项目风险评估 (RiskAssessmentComponent)

-   **新文件名**: `RiskAssessmentComponent.js`
-   **目标容器 ID**: `risk-area` (对应HTML中的 `grid-area-3`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2057`](tools/ace/src/web_interface/templates/project_manager_v2.html:2057) 到 [`project_manager_v2.html:2129`](tools/ace/src/web_interface/templates/project_manager_v2.html:2129) 的 `<div class="grid-area grid-area-3 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['risk_assessment', 'health_report']`。
-   **渲染逻辑适配**:
    -   `render()`: 包含可靠性评分的圆形进度条、风险预防措施和文档健康报告。数据从 `this.getData('risk_assessment')` 获取。
-   **事件绑定策略**:
    -   `bindEvents()`: 需要为 "查看完整报告" 链接 (`#report-link`) 绑定点击事件。
-   **兼容性桥接**:
    -   旧的 `renderRiskAssessment` 函数逻辑需要被 `render()` 方法替代。

### 3.3. 项目经理状态 (ManagerStatusComponent)

-   **新文件名**: `ManagerStatusComponent.js`
-   **目标容器 ID**: `manager-area` (对应HTML中的 `grid-area-4`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2132`](tools/ace/src/web_interface/templates/project_manager_v2.html:2132) 到 [`project_manager_v2.html:2168`](tools/ace/src/web_interface/templates/project_manager_v2.html:2168) 的 `<div class="grid-area grid-area-4 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['manager_status', 'task_status']`。
-   **渲染逻辑适配**:
    -   `render()`: 渲染项目经理角色、当前任务、工作状态等信息。
-   **事件绑定策略**:
    -   `bindEvents()`: 暂时为空，此区域为纯展示。

### 3.4. Python主持人算法思维 (AlgorithmMindsetComponent)

-   **新文件名**: `AlgorithmMindsetComponent.js`
-   **目标容器 ID**: `algorithm-area` (对应HTML中的 `grid-area-5`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:1959`](tools/ace/src/web_interface/templates/project_manager_v2.html:1959) 到 [`project_manager_v2.html:2054`](tools/ace/src/web_interface/templates/project_manager_v2.html:2054) 的 `<div class="grid-area grid-area-5 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['algorithm_logs']`。
-   **渲染逻辑适配**:
    -   `render()`: 渲染AI-算法协同状态和日志条目。日志条目应动态生成。
-   **事件绑定策略**:
    -   `bindEvents()`: 使用事件委托来处理所有日志条目的点击事件，替代原有的内联 `onclick="showLogDetail(...)"` 和 `onclick="toggleLogDetail(...)"`。
-   **兼容性桥接**:
    -   需要创建一个 `showLogDetail` 的桥接函数，该函数会调用新组件的方法来更新 "详细区" (`HumanInputComponent`) 的内容。这可以通过 `appManager.getComponent('human-input').updateDetail(logContent)` 实现。

### 3.5. 项目约束审查 (ConstraintReviewComponent)

-   **新文件名**: `ConstraintReviewComponent.js`
-   **目标容器 ID**: `constraint-area` (对应HTML中的 `grid-area-6`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2170`](tools/ace/src/web_interface/templates/project_manager_v2.html:2170) 到 [`project_manager_v2.html:2177`](tools/ace/src/web_interface/templates/project_manager_v2.html:2177) 的 `<div class="grid-area grid-area-6 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['constraint_detail']`。
-   **渲染逻辑适配**:
    -   `render()`: 默认显示提示信息。当从 `KnowledgeBaseComponent` 接收到事件时，渲染特定约束的详细信息。
-   **事件绑定策略**:
    -   `bindEvents()`: 暂时为空。
-   **公共方法**:
    -   需要提供一个公共方法，如 `displayConstraint(constraintData)`，供其他组件调用。

### 3.6. 项目知识库 (KnowledgeBaseComponent)

-   **新文件名**: `KnowledgeBaseComponent.js`
-   **目标容器 ID**: `knowledge-area` (对应HTML中的 `grid-area-7`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2180`](tools/ace/src/web_interface/templates/project_manager_v2.html:2180) 到 [`project_manager_v2.html:2340`](tools/ace/src/web_interface/templates/project_manager_v2.html:2340) 的 `<div class="grid-area grid-area-7 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['knowledge_graph']`。
-   **渲染逻辑适配**:
    -   `render()`: 动态渲染知识图谱的节点和连接线。
-   **事件绑定策略**:
    -   `bindEvents()`:
        -   为帮助图标 `?` 绑定点击事件，替代 `onclick="showKnowledgeBaseHelp(event)"`。
        -   为知识图谱容器使用事件委托，处理所有 `constraint-node` 的点击事件，替代 `onclick="showConstraintDetail('...')"`。点击时，获取约束ID，然后调用 `ConstraintReviewComponent` 的公共方法来显示详情。
-   **兼容性桥接**:
    -   `showConstraintDetail` 和 `showKnowledgeBaseHelp` 全局函数需要被废弃，其逻辑移入组件的事件处理器中。

### 3.7. 人类输入控制区 (HumanInputComponent)

-   **新文件名**: `HumanInputComponent.js`
-   **目标容器 ID**: `control-area` (对应HTML中的 `grid-area-8`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2343`](tools/ace/src/web_interface/templates/project_manager_v2.html:2343) 到 [`project_manager_v2.html:2383`](tools/ace/src/web_interface/templates/project_manager_v2.html:2383) 的 `<div class="grid-area grid-area-8 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['project_list', 'control_status']`。
-   **渲染逻辑适配**:
    -   `render()`: 渲染详细区、项目选择器、输入框和控制按钮。
-   **事件绑定策略**:
    -   `bindEvents()`:
        -   为 "刷新" 和 "新建" 按钮绑定点击事件，替代 `onclick="refreshProjects()"` 和 `onclick="createNewProject()"`。
        -   为 "开始", "暂停", "停止", "扫描" 按钮绑定点击事件，替代 `onclick`。
-   **公共方法**:
    -   提供 `updateDetail(content)` 方法，供 `AlgorithmMindsetComponent` 调用以显示日志详情。
-   **兼容性桥接**:
    -   `refreshProjects`, `createNewProject`, `startMeeting`, `pauseMeeting`, `stopMeeting`, `handleScanningClick` 等全局函数将被废弃。

### 3.8. 项目交付结果 (ProjectOutputComponent)

-   **新文件名**: `ProjectOutputComponent.js`
-   **目标容器 ID**: `deliverables-area` (对应HTML中的 `grid-area-9`)
-   **代码迁移路径**:
    -   **HTML**: 从 [`project_manager_v2.html:2386`](tools/ace/src/web_interface/templates/project_manager_v2.html:2386) 到 [`project_manager_v2.html:2446`](tools/ace/src/web_interface/templates/project_manager_v2.html:2446) 的 `<div class="grid-area grid-area-9 ...">...</div>` 内的所有内容。
-   **数据类型映射**:
    -   `getDataTypes()`: 应返回 `['deliverables']`。
-   **渲染逻辑适配**:
    -   `render()`: 渲染审计状态、产出链接和处理统计。
-   **事件绑定策略**:
    -   `bindEvents()`: 为动态生成的产出链接绑定点击事件。

## 4. 总结与后续步骤

本计划详细定义了将 `project_manager_v2.html` 重构为8个独立组件的完整路径。下一步是创建 `pm_v2_unified_init.js` 和 `pm_v2_components.js` 文件，并开始逐个实现上述组件。

完成此计划后，PM_V2的前端代码将更加模块化、可维护，并完全融入统一架构。