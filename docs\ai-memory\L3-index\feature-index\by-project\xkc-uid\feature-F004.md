# 功能索引: F004 - CommonsUidLibrary

## 基本信息

- **功能ID**: F004
- **功能名称**: CommonsUidLibrary
- **所属项目**: xkongcloud-commons-uid (XKC-UID)
- **开始日期**: 2025-05-11
- **完成状态**: 维护中
- **当前阶段**: 稳定维护阶段
- **负责人**: AI助手

## 功能概述
- **功能ID**: F004
- **功能名称**: CommonsUidLibrary
- **所属项目**: xkongcloud-commons-uid (XKC-UID)
- **主要目标**: 提供标准化的分布式唯一ID生成库，支持持久化实例身份管理和机器特征码自动恢复

## 同步状态
- **索引创建日期**: 2025-01-15
- **最后同步日期**: 2025-05-28
- **同步来源**: docs/features/F004-CommonsUidLibrary-20250511/
- **功能状态**: stable_maintenance
- **同步状态**: up_to_date
- **同步检查频率**: 每周检查一次
- **优先级**: medium

> **⚠️ 重要提醒**: 此索引文档为AI记忆辅助文档，信息时效性取决于同步状态。
> 
> - **当前功能状态为"stable_maintenance"**: 可以使用此记忆索引，但需验证同步状态
> - **当前同步状态为"up_to_date"**: 此索引信息与原始文档基本同步，可作为主要参考
> 
> 使用命令 `@sync:feature:F004` 可手动触发索引同步更新。

## 关键信息
- **技术栈**: 百度UidGenerator, PostgreSQL, Spring Framework, JPA
- **依赖功能**: 无
- **被依赖功能**: F003 (PostgreSQL迁移功能依赖此UID库)
- **核心组件**: UidGeneratorFacade, PersistentInstanceManager, PersistentInstanceWorkerIdAssigner

## 实现细节
- **核心类/方法**: 
  - `UidGeneratorFacade.getUID()` - 获取分布式唯一ID
  - `PersistentInstanceManager.initializeInstanceId()` - 初始化实例ID
  - `PersistentInstanceWorkerIdAssigner.assignWorkerId()` - 分配工作节点ID
- **关键配置**: 
  - `uid.instance.encryption.enabled` - 实例ID加密开关
  - `uid.instance.recovery.strategy` - 实例恢复策略
  - `uid.schema.name` - 数据库Schema名称
- **数据结构**: 
  - `infra_uid.instance_registry` - 实例注册表
  - `infra_uid.worker_id_assignment` - 工作节点ID分配表
  - `infra_uid.encryption_key` - 加密密钥表
- **异常处理**: 使用xkongcloud-common-exception统一异常处理框架

## 开发状态追踪
- **当前开发阶段**: 维护和优化阶段
- **完成进度**: 95% (核心功能已完成，进行最终优化)
- **下一步计划**: 性能优化和文档完善
- **关键风险点**: 生产环境下的并发性能需要验证

## 使用指南
- **初始化方式**: 
  1. 配置数据源和Schema
  2. 使用UidGeneratorFacadeBuilder创建Facade实例
  3. 通过Facade获取UID
- **常见用例**: 
  - 业务实体ID生成
  - 分布式任务标识生成
  - 追踪ID生成
- **最佳实践**: 
  - 使用门面模式访问所有功能
  - 启用实例ID加密保护
  - 配置合适的恢复策略
- **已知限制**: 
  - 需要PostgreSQL支持
  - 单个应用实例最多支持1024个Worker ID

## 集成信息
- **API接口**: 
  - `UidGeneratorFacade.getUID()` - 获取单个UID
  - `UidGeneratorFacade.getUIDBatch(int size)` - 批量获取UID
  - `UidGeneratorFacade.parseUid(long uid)` - 解析UID结构
- **配置要求**: 
  - PostgreSQL数据库连接
  - infra_uid Schema已创建
  - KV参数服务可用
- **环境依赖**: 
  - Java 8+
  - Spring Framework 5+
  - PostgreSQL 12+
- **性能指标**: 
  - 单线程UID生成: >10万/秒
  - 批量生成: >50万/秒
  - 实例恢复时间: <5秒

## 测试信息
- **测试覆盖率**: 85% (单元测试和集成测试)
- **关键测试用例**: 
  - UID唯一性测试
  - 并发生成测试
  - 实例恢复测试
  - 加密/解密测试
- **已知问题**: 
  - 极高并发下可能出现工作节点ID竞争
  - 特征码匹配算法需要进一步优化
- **测试环境**: PostgreSQL 17, Docker容器化测试

## 相关文档
- **原始功能文档**: `docs/features/F004-CommonsUidLibrary-20250511/`
- **API文档**: `docs/features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md`
- **测试文档**: `docs/features/F004-CommonsUidLibrary-20250511/test/`
- **相关示例**: `docs/features/F004-CommonsUidLibrary-20250511/mermaid/`
- **功能状态注册表**: `docs/feature-status.json`

## 变更历史
- **2025-01-10**: 完成核心功能开发，进入维护阶段 - 同步状态: up_to_date
- **2025-01-05**: 实现加密功能和实例恢复机制 - 同步状态: up_to_date
- **2024-12-20**: 项目启动，完成基础架构设计 - 同步状态: up_to_date

---

**使用此索引的注意事项**:
1. 此功能当前状态为stable_maintenance，可以作为主要参考
2. 如需最新的详细技术信息，请参考原始功能文档
3. 使用前建议验证同步状态是否为最新
4. 重要配置变更后应及时更新此索引

## 相关资源和文档

### 内部项目文档
1. [XKC-CORE与XKC-UID依赖关系](docs/ai-memory/L3-index/dependency-index/XKC-CORE-UID.md)
2. [F003-PostgreSQL迁移索引](docs/ai-memory/L3-index/feature-index/by-project/xkc-core/F003-PostgreSQL迁移.md)
3. [XKC-CORE与XKC-UID集成上下文](docs/ai-memory/L2-context/project-groups/xkc-core+uid.md)

### 技术文档
1. [百度UidGenerator PostgreSQL实现](docs/common/middleware/integration/baidu-uid-generator-postgresql-implementation.md)
2. [PostgreSQL技术栈上下文](docs/ai-memory/L2-context/tech-stack/postgresql-stack.md)
3. [全局硬约束](docs/ai-memory/L1-core/global-constraints.md)

### 原始功能文档
1. [实施计划](docs/features/F004-CommonsUidLibrary-20250511/plan/implementation-plan.md)
2. [设计文档](docs/features/F004-CommonsUidLibrary-20250511/design/commons-uid-library-design.md)
3. [测试文档](docs/features/F004-CommonsUidLibrary-20250511/test/)
4. [Mermaid图表](docs/features/F004-CommonsUidLibrary-20250511/mermaid/) 