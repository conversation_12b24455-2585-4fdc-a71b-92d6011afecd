#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Universal Debugger - 通用调试器系统

版本: V1.0
创建日期: 2025-01-19
描述: 统一的调试器系统，JS直接调用，动态创建执行器
核心理念: 一个文件，JS驱动，动态执行
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, Optional
from datetime import datetime

# 确保可以导入src目录下的模块
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, '..')
sys.path.insert(0, src_dir)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UniversalDebugger:
    """通用调试器 - JS直接调用，动态创建执行器"""
    
    def __init__(self):
        self.execution_history = []
    
    async def debug(self, js_params: Dict[str, Any]) -> Dict[str, Any]:
        """JS直接调用入口"""
        start_time = datetime.now()
        
        try:
            logger.info(f"收到JS调试请求: {js_params.get('executor_role', 'unknown')}")
            
            # 1. 解析JS参数
            executor_role = js_params.get('executor_role', 'code_generator')
            task_id = js_params.get('task_id', f"debug_{datetime.now().timestamp()}")
            task_context = js_params.get('task_context', {})
            
            # 2. 动态创建执行器
            executor = await self._create_executor(executor_role, task_id, task_context)
            
            # 3. 执行任务
            result = await self._execute_task(executor, js_params)
            
            # 4. 记录历史
            execution_record = {
                'timestamp': start_time.isoformat(),
                'executor_role': executor_role,
                'task_id': task_id,
                'success': result.get('success', False),
                'execution_time': (datetime.now() - start_time).total_seconds(),
                'result': result
            }
            self.execution_history.append(execution_record)
            
            logger.info(f"调试任务完成: {result.get('success', False)}")
            return result
            
        except Exception as e:
            logger.error(f"调试任务失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_time': (datetime.now() - start_time).total_seconds()
            }
    
    async def _create_executor(self, executor_role: str, task_id: str, task_context: Dict) -> Any:
        """动态创建执行器 - 支持单实例和多实例模式"""
        try:
            # 从task_context中获取包信息和实例模式
            package_info = task_context.get('package_info', {})
            instance_mode = task_context.get('instance_mode', 'create_new')  # 'singleton' 或 'create_new'
            
            package_name = package_info.get('package_name', 'executors')
            module_name = package_info.get('module_name', 'validation_driven_executor')
            class_name = package_info.get('class_name', 'ValidationDrivenExecutor')
            
            # 通用动态导入系统 - 支持任意包、模块、类
            try:
                import importlib
                import importlib.util
                
                # 通用导入策略
                executor_class = None
                import_errors = []
                
                # 策略1: 标准包导入 (package.module.class)
                try:
                    if module_name:
                        full_module_path = f"{package_name}.{module_name}"
                    else:
                        full_module_path = package_name
                    
                    module = importlib.import_module(full_module_path)
                    executor_class = getattr(module, class_name)
                    logger.info(f"✅ 策略1成功: {full_module_path}.{class_name}")
                except (ImportError, AttributeError) as e:
                    import_errors.append(f"策略1(标准包导入)失败: {e}")
                
                # 策略2: 直接模块导入 (module.class)
                if executor_class is None:
                    try:
                        module = importlib.import_module(module_name or package_name)
                        executor_class = getattr(module, class_name)
                        logger.info(f"✅ 策略2成功: {module_name or package_name}.{class_name}")
                    except (ImportError, AttributeError) as e:
                        import_errors.append(f"策略2(直接模块导入)失败: {e}")
                
                # 策略3: 文件路径导入 (适用于不在包中的模块)
                if executor_class is None:
                    try:
                        # 尝试从当前目录查找模块文件
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        possible_paths = [
                            os.path.join(current_dir, '..', package_name, f"{module_name}.py"),
                            os.path.join(current_dir, '..', f"{module_name}.py"),
                            os.path.join(current_dir, '..', package_name, '__init__.py'),
                            os.path.join(current_dir, '..', f"{package_name}.py")
                        ]
                        
                        for file_path in possible_paths:
                            if os.path.exists(file_path):
                                spec = importlib.util.spec_from_file_location(module_name or package_name, file_path)
                                if spec and spec.loader:
                                    module = importlib.util.module_from_spec(spec)
                                    spec.loader.exec_module(module)
                                    executor_class = getattr(module, class_name)
                                    logger.info(f"✅ 策略3成功: {file_path}.{class_name}")
                                    break
                    except (ImportError, AttributeError, Exception) as e:
                        import_errors.append(f"策略3(文件路径导入)失败: {e}")
                
                # 策略4: 系统路径搜索导入
                if executor_class is None:
                    try:
                        # 在sys.path中搜索模块
                        for path in sys.path:
                            if os.path.exists(path):
                                for root, dirs, files in os.walk(path):
                                    for file in files:
                                        if file.endswith('.py') and (module_name in file or package_name in file):
                                            try:
                                                file_path = os.path.join(root, file)
                                                module_name_from_file = os.path.splitext(file)[0]
                                                spec = importlib.util.spec_from_file_location(module_name_from_file, file_path)
                                                if spec and spec.loader:
                                                    module = importlib.util.module_from_spec(spec)
                                                    spec.loader.exec_module(module)
                                                    if hasattr(module, class_name):
                                                        executor_class = getattr(module, class_name)
                                                        logger.info(f"✅ 策略4成功: {file_path}.{class_name}")
                                                        break
                                            except Exception:
                                                continue
                                    if executor_class:
                                        break
                            if executor_class:
                                break
                    except Exception as e:
                        import_errors.append(f"策略4(系统路径搜索)失败: {e}")
                
                # 如果所有策略都失败
                if executor_class is None:
                    error_msg = f"无法导入 {package_name}.{module_name}.{class_name}，尝试的策略:\n" + "\n".join(import_errors)
                    logger.warning(error_msg)
                    raise ImportError(f"所有导入策略都失败: {error_msg}")
                
                # 根据实例模式创建或获取执行器
                if instance_mode == 'singleton':
                    # 单实例模式：尝试获取现有实例或创建新实例
                    return self._get_or_create_singleton(executor_class, executor_role, task_context)
                else:
                    # 多实例模式：创建新实例
                    return executor_class(
                        task_id=task_id,
                        executor_role=executor_role,
                        task_context=task_context
                    )
                
            except ImportError as e:
                logger.warning(f"无法导入指定包 {package_name}.{module_name}: {e}")
                # 回退到模拟执行器
                logger.info("使用模拟执行器作为回退方案")
                return self._create_mock_executor(task_id, executor_role, task_context)
                
        except Exception as e:
            logger.error(f"执行器创建失败: {e}")
            raise Exception(f"无法创建执行器: {e}")
    
    def _get_or_create_singleton(self, executor_class, executor_role: str, task_context: Dict) -> Any:
        """获取或创建单实例执行器"""
        # 使用executor_role作为单实例的key
        singleton_key = f"{executor_role}_{hash(str(task_context))}"
        
        if not hasattr(self, '_singleton_instances'):
            self._singleton_instances = {}
        
        if singleton_key not in self._singleton_instances:
            logger.info(f"创建新的单实例执行器: {executor_role}")
            self._singleton_instances[singleton_key] = executor_class(
                task_id=f"singleton_{executor_role}",
                executor_role=executor_role,
                task_context=task_context
            )
        else:
            logger.info(f"复用现有单实例执行器: {executor_role}")
        
        return self._singleton_instances[singleton_key]
    
    def _create_mock_executor(self, task_id: str, executor_role: str, task_context: Dict) -> Any:
        """创建模拟执行器作为回退方案"""
        class MockExecutor:
            def __init__(self, task_id, executor_role, task_context):
                self.task_id = task_id
                self.executor_role = executor_role
                self.task_context = task_context
            
            async def execute_with_validation(self, original_content, pycrud_operations, guardrails, constraints, context, confidence_threshold=0.85):
                # 模拟执行结果
                import time
                time.sleep(1)  # 模拟执行时间
                
                return type('ExecutionResult', (), {
                    'success': True,
                    'confidence': 0.9,
                    'generated_content': f"模拟生成的{executor_role}内容",
                    'execution_time': 1.0,
                    'error_message': '',
                    'execution_result': None,
                    'validation_result': None
                })()
        
        return MockExecutor(task_id, executor_role, task_context)
    
    async def _execute_task(self, executor: Any, js_params: Dict) -> Dict[str, Any]:
        """执行任务"""
        try:
            # 从JS参数中提取执行参数
            execution_params = js_params.get('execution_params', {})
            
            # 调用执行器的execute_with_validation方法
            result = await executor.execute_with_validation(
                original_content=execution_params.get('original_content', ''),
                pycrud_operations=execution_params.get('pycrud_operations'),
                guardrails=execution_params.get('guardrails', {}),
                constraints=execution_params.get('constraints', {}),
                context=execution_params.get('context', {}),
                confidence_threshold=execution_params.get('confidence_threshold', 0.85)
            )
            
            # 返回结果
            return {
                'success': result.success,
                'confidence': result.confidence,
                'generated_content': result.generated_content,
                'execution_time': result.execution_time,
                'error_message': result.error_message,
                'execution_result': result.execution_result,
                'validation_result': result.validation_result
            }
            
        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_history(self) -> list:
        """获取执行历史"""
        return self.execution_history
    
    def clear_history(self):
        """清空历史"""
        self.execution_history.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.execution_history:
            return {}
        
        total = len(self.execution_history)
        success = sum(1 for record in self.execution_history if record['success'])
        total_time = sum(record['execution_time'] for record in self.execution_history)
        
        return {
            'total_executions': total,
            'successful_executions': success,
            'success_rate': success / total if total > 0 else 0,
            'average_time': total_time / total if total > 0 else 0,
            'total_time': total_time
        }


# ==================== 全局调试器实例 ====================

# 创建全局调试器实例
debugger = UniversalDebugger()


# ==================== 便捷函数 ====================

async def debug_from_js(js_params: Dict[str, Any]) -> Dict[str, Any]:
    """JS调用的便捷函数"""
    return await debugger.debug(js_params)


def get_debug_history() -> list:
    """获取调试历史"""
    return debugger.get_history()


def clear_debug_history():
    """清空调试历史"""
    debugger.clear_history()


def get_debug_stats() -> Dict[str, Any]:
    """获取调试统计"""
    return debugger.get_stats()


# ==================== 测试函数 ====================

async def test_debugger():
    """测试调试器"""
    # 模拟JS参数
    test_params = {
        'executor_role': 'code_generator',
        'task_id': 'test_task_001',
        'task_context': {
            '项目背景': '测试项目',
            '技术栈': 'Python'
        },
        'execution_params': {
            'original_content': '生成一个简单的Hello World函数',
            'guardrails': {
                '禁止项': {
                    '不能使用': ['eval', 'exec']
                }
            },
            'constraints': {
                '依赖条件': {
                    '必须集成': ['Python 3.9']
                }
            },
            'context': {
                '项目背景': '测试项目',
                '技术栈': 'Python'
            },
            'confidence_threshold': 0.85
        }
    }
    
    result = await debug_from_js(test_params)
    print(f"测试结果: {json.dumps(result, indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    asyncio.run(test_debugger()) 