#!/usr/bin/env node

/**
 * 修复权威性源文件路径
 * 自动找到存在的docs/common文件并更新权威性元数据
 */

const fs = require('fs');
const path = require('path');

// 智能映射规则 - 根据文件内容和名称匹配合适的源文件
const smartMappings = [
    {
        file: 'docs/ai-memory/L2-context/task-types/documentation-tasks.json',
        possibleSources: [
            'docs/common/best-practices/document-management',
            'docs/common/templates'
        ]
    },
    {
        file: 'docs/ai-memory/L2-context/project-groups/xkc-core+uid.json',
        possibleSources: [
            'docs/common/architecture/patterns',
            'docs/common/architecture/principles'
        ]
    },
    {
        file: 'docs/ai-memory/L2-context/automation/document-creation-automation.json',
        possibleSources: [
            'docs/common/best-practices/document-management',
            'docs/common/templates'
        ]
    }
];

function findExistingFiles(directory) {
    const files = [];
    
    if (!fs.existsSync(directory)) {
        return files;
    }
    
    const items = fs.readdirSync(directory, { withFileTypes: true });
    
    for (const item of items) {
        const fullPath = path.join(directory, item.name);
        if (item.isFile() && (item.name.endsWith('.md') || item.name.endsWith('.json'))) {
            files.push(fullPath);
        } else if (item.isDirectory()) {
            files.push(...findExistingFiles(fullPath));
        }
    }
    
    return files;
}

function findBestMatch(targetFile, possibleSources) {
    // 获取目标文件的关键词
    const targetName = path.basename(targetFile, '.json');
    const keywords = targetName.split('-');
    
    let bestMatch = null;
    let bestScore = 0;
    
    for (const sourceDir of possibleSources) {
        const files = findExistingFiles(sourceDir);
        
        for (const file of files) {
            const fileName = path.basename(file, path.extname(file));
            let score = 0;
            
            // 计算匹配分数
            for (const keyword of keywords) {
                if (fileName.toLowerCase().includes(keyword.toLowerCase())) {
                    score++;
                }
            }
            
            if (score > bestScore) {
                bestScore = score;
                bestMatch = file;
            }
        }
    }
    
    // 如果没有找到好的匹配，使用第一个可用文件
    if (!bestMatch && possibleSources.length > 0) {
        const files = findExistingFiles(possibleSources[0]);
        if (files.length > 0) {
            bestMatch = files[0];
        }
    }
    
    return bestMatch;
}

function fixAuthoritySource(filePath, newSourceFile) {
    try {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠ 文件不存在: ${filePath}`);
            return false;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const jsonContent = JSON.parse(content);

        if (!jsonContent.authority_metadata) {
            console.log(`⚠ ${filePath} 没有权威性元数据`);
            return false;
        }

        // 更新源文件路径
        jsonContent.authority_metadata.source_file = newSourceFile;
        jsonContent.authority_metadata.last_sync_date = new Date().toISOString();

        // 写回文件
        fs.writeFileSync(filePath, JSON.stringify(jsonContent, null, 2));
        console.log(`✅ 已更新 ${filePath} 的源文件为: ${newSourceFile}`);
        return true;

    } catch (error) {
        console.error(`❌ 处理 ${filePath} 时出错: ${error.message}`);
        return false;
    }
}

function main() {
    console.log('🔧 开始修复权威性源文件路径...\n');
    
    let successCount = 0;
    let totalCount = smartMappings.length;

    for (const mapping of smartMappings) {
        console.log(`🔍 处理: ${mapping.file}`);
        
        const bestMatch = findBestMatch(mapping.file, mapping.possibleSources);
        
        if (bestMatch) {
            console.log(`  找到匹配: ${bestMatch}`);
            if (fixAuthoritySource(mapping.file, bestMatch)) {
                successCount++;
            }
        } else {
            console.log(`  ❌ 未找到合适的源文件`);
        }
        
        console.log('');
    }

    console.log('📊 处理结果:');
    console.log(`  总文件数: ${totalCount}`);
    console.log(`  成功处理: ${successCount}`);
    console.log(`  失败数量: ${totalCount - successCount}`);
    
    if (successCount === totalCount) {
        console.log('\n🎉 所有文件处理完成！');
    } else {
        console.log('\n⚠ 部分文件处理失败，请检查错误信息');
    }
}

if (require.main === module) {
    main();
}
