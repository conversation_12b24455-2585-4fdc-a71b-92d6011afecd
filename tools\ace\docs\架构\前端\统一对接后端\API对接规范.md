# API对接规范

## 概述

本文档定义了前端统一架构与后端API的对接规范，确保数据交互的一致性和可靠性。

---

## 1. 数据类型映射

### 标准数据类型
```javascript
const DATA_TYPE_MAPPING = {
    // 进度相关
    'progress': '/projects/{projectId}/progress',
    'stage_metrics': '/projects/{projectId}/stage-metrics',
    
    // 风险评估
    'risk_assessment': '/projects/{projectId}/risk-assessment',
    'health_report': '/projects/{projectId}/health-report',
    
    // 约束管理
    'constraints': '/projects/{projectId}/constraints',
    'constraint_detail': '/projects/{projectId}/constraints/{constraintId}',
    
    // 知识库
    'knowledge_graph': '/projects/{projectId}/knowledge-graph',
    'graph_layout': '/projects/{projectId}/knowledge-graph/layout',
    
    // 管理器状态
    'manager_status': '/projects/{projectId}/manager-status',
    'task_status': '/projects/{projectId}/current-task',
    
    // 算法日志
    'algorithm_logs': '/projects/{projectId}/algorithm-logs',
    'log_detail': '/projects/{projectId}/algorithm-logs/{logId}',
    
    // 交付结果
    'deliverables': '/projects/{projectId}/deliverables',
    'file_download': '/files/{fileId}/download'
};
```

### 数据类型注册
```javascript
// 在DataManager中注册新的数据类型
class DataManager {
    constructor(projectId) {
        super(projectId);
        this.registerDataTypes();
    }
    
    registerDataTypes() {
        // 注册标准数据类型
        Object.entries(DATA_TYPE_MAPPING).forEach(([type, endpoint]) => {
            this.registerDataType(type, endpoint);
        });
        
        // 注册自定义数据类型
        this.registerCustomDataTypes();
    }
    
    registerDataType(type, endpoint) {
        this.dataTypeEndpoints.set(type, endpoint);
    }
    
    registerCustomDataTypes() {
        // 项目特定的数据类型
        this.registerDataType('custom_metrics', '/projects/{projectId}/custom-metrics');
        this.registerDataType('user_preferences', '/projects/{projectId}/user-preferences');
    }
}
```

---

## 2. 请求规范

### HTTP请求标准格式
```javascript
// GET请求
const response = await httpClient.get('/projects/123/progress', {
    // 查询参数
    include: 'stage_metrics,key_metrics',
    format: 'detailed',
    timestamp: Date.now()
});

// POST请求
const response = await httpClient.post('/projects/123/actions/start', {
    // 请求体
    stage: 'all',
    options: {
        force_restart: false,
        skip_validation: false
    }
});

// PUT请求
const response = await httpClient.put('/projects/123/constraints/GB001', {
    // 更新数据
    params: {
        max_ms: 2000,
        unit: 'milliseconds'
    },
    description: '更新后的描述'
});
```

### 请求头规范
```javascript
const STANDARD_HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Client-Version': '2.0.0',
    'X-Request-ID': generateRequestId(),
    'Authorization': 'Bearer {token}'
};

// 在HttpClient中自动添加
class HttpClient {
    constructor() {
        this.defaultHeaders = STANDARD_HEADERS;
    }
    
    addRequestInterceptor((config) => {
        // 自动添加请求ID
        config.headers['X-Request-ID'] = generateRequestId();
        
        // 添加时间戳
        config.headers['X-Timestamp'] = new Date().toISOString();
        
        return config;
    });
}
```

### 错误处理规范
```javascript
// 标准错误响应格式
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "请求参数验证失败",
        "details": {
            "field": "projectId",
            "reason": "项目ID不能为空"
        }
    },
    "timestamp": "2025-01-31T14:17:30Z",
    "request_id": "req_123456789"
}

// 错误处理实现
class HttpClient {
    async request(method, url, data, headers) {
        try {
            const response = await fetch(url, config);
            const result = await response.json();
            
            if (!response.ok) {
                throw new APIError(result.error, response.status);
            }
            
            return result;
        } catch (error) {
            if (error instanceof APIError) {
                throw error;
            }
            
            // 网络错误或其他错误
            throw new NetworkError(error.message);
        }
    }
}

// 自定义错误类
class APIError extends Error {
    constructor(errorInfo, status) {
        super(errorInfo.message);
        this.code = errorInfo.code;
        this.details = errorInfo.details;
        this.status = status;
    }
}

class NetworkError extends Error {
    constructor(message) {
        super(message);
        this.code = 'NETWORK_ERROR';
    }
}
```

---

## 3. 响应数据规范

### 标准响应格式
```javascript
// 成功响应
{
    "success": true,
    "data": {
        // 实际数据
    },
    "meta": {
        "total": 100,
        "page": 1,
        "per_page": 20,
        "has_more": true
    },
    "timestamp": "2025-01-31T14:17:30Z",
    "request_id": "req_123456789"
}

// 分页响应
{
    "success": true,
    "data": {
        "items": [...],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 100,
            "per_page": 20,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

### 数据转换规范
```javascript
// 响应数据转换器
class ResponseTransformer {
    static transform(dataType, rawResponse) {
        const transformers = {
            'progress': this.transformProgress,
            'constraints': this.transformConstraints,
            'risk_assessment': this.transformRiskAssessment
        };
        
        const transformer = transformers[dataType];
        return transformer ? transformer(rawResponse) : rawResponse;
    }
    
    static transformProgress(rawData) {
        return {
            currentStage: {
                number: rawData.current_stage.stage_number,
                name: rawData.current_stage.stage_name,
                status: rawData.current_stage.status,
                progress: rawData.current_stage.progress_percentage
            },
            stageZeroMetrics: {
                preValidationRate: rawData.stage_zero_metrics.pre_validation_pass_rate,
                conflictPrevention: rawData.stage_zero_metrics.conflict_prevention_count,
                schemaValidation: {
                    passed: rawData.stage_zero_metrics.schema_validation_passed,
                    total: rawData.stage_zero_metrics.schema_validation_total
                }
            },
            keyMetrics: {
                constraintsDiscovered: rawData.key_metrics.atomic_constraints_discovered,
                contractsGenerated: rawData.key_metrics.global_contracts_generated,
                reliabilityScore: rawData.key_metrics.current_reliability_score
            }
        };
    }
    
    static transformConstraints(rawData) {
        return {
            constraints: rawData.constraints.map(constraint => ({
                id: constraint.id,
                category: constraint.category,
                type: constraint.type,
                params: constraint.params,
                description: constraint.description,
                sourceBlockId: constraint.source_block_id,
                parentId: constraint.parent_id,
                isForked: !!constraint.parent_id,
                createdAt: new Date(constraint.created_at),
                updatedAt: new Date(constraint.updated_at)
            })),
            total: rawData.total,
            hasMore: rawData.has_more
        };
    }
}

// 在DataManager中使用转换器
class DataManager {
    async fetchData(dataType, params, useCache) {
        const rawResponse = await this.httpClient.get(endpoint, params);
        
        if (rawResponse.success) {
            // 应用数据转换
            const transformedData = ResponseTransformer.transform(dataType, rawResponse.data);
            this.setData(cacheKey, transformedData);
            return transformedData;
        }
        
        throw new Error(rawResponse.error?.message || 'Request failed');
    }
}
```

---

## 4. WebSocket规范

### 连接管理
```javascript
class WebSocketManager {
    constructor(projectId) {
        this.projectId = projectId;
        this.url = `ws://localhost:25526/ws/project/${projectId}`;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.connect();
    }
    
    connect() {
        this.ws = new WebSocket(this.url);
        
        this.ws.onopen = () => {
            console.log('WebSocket connected');
            this.reconnectAttempts = 0;
            this.sendAuth();
        };
        
        this.ws.onmessage = (event) => {
            this.handleMessage(JSON.parse(event.data));
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket disconnected');
            this.scheduleReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
        };
    }
    
    sendAuth() {
        this.send({
            type: 'auth',
            data: {
                token: this.getAuthToken(),
                client_info: {
                    version: '2.0.0',
                    user_agent: navigator.userAgent
                }
            }
        });
    }
    
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            setTimeout(() => {
                console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                this.connect();
            }, delay);
        }
    }
}
```

### 消息格式规范
```javascript
// 标准WebSocket消息格式
{
    "type": "event_type",
    "data": {
        // 事件数据
    },
    "timestamp": "2025-01-31T14:17:30Z",
    "sequence": 12345,
    "project_id": "project_123"
}

// 事件类型映射
const WEBSOCKET_EVENT_MAPPING = {
    // 进度事件
    'stage_progress_update': 'progress',
    'stage_zero_metrics_update': 'progress',
    'key_metrics_update': 'progress',
    
    // 风险事件
    'reliability_score_update': 'risk_assessment',
    'risk_prevented': 'risk_assessment',
    
    // 约束事件
    'constraint_created': 'constraints',
    'constraint_updated': 'constraints',
    
    // 知识库事件
    'knowledge_graph_update': 'knowledge_graph',
    'node_relationship_update': 'knowledge_graph',
    
    // 管理器事件
    'manager_status_update': 'manager_status',
    'current_task_update': 'manager_status',
    
    // 算法事件
    'algorithm_log_entry': 'algorithm_logs',
    'ai_algorithm_collaboration_update': 'algorithm_logs',
    
    // 交付事件
    'deliverable_ready': 'deliverables',
    'processing_complete': 'deliverables'
};

// 消息处理
class DataManager {
    handleWebSocketMessage(message) {
        const { type, data, timestamp, sequence } = message;
        
        // 检查消息序列号，避免重复处理
        if (this.isMessageProcessed(sequence)) {
            return;
        }
        
        // 根据事件类型更新对应的数据
        const dataType = WEBSOCKET_EVENT_MAPPING[type];
        if (dataType) {
            this.updateDataFromWebSocket(dataType, type, data);
        } else {
            console.warn(`Unknown WebSocket event type: ${type}`);
        }
        
        // 记录已处理的消息
        this.markMessageProcessed(sequence);
    }
    
    updateDataFromWebSocket(dataType, eventType, eventData) {
        const currentData = this.getData(dataType) || {};
        let updatedData;
        
        switch (eventType) {
            case 'stage_progress_update':
                updatedData = {
                    ...currentData,
                    current_stage: { ...currentData.current_stage, ...eventData }
                };
                break;
                
            case 'constraint_created':
                updatedData = {
                    ...currentData,
                    constraints: [...(currentData.constraints || []), eventData.constraint]
                };
                break;
                
            default:
                updatedData = { ...currentData, ...eventData };
        }
        
        this.setData(dataType, updatedData);
    }
}
```

---

## 5. 缓存策略

### 缓存配置
```javascript
const CACHE_STRATEGIES = {
    // 高频更新数据 - 短缓存
    'manager_status': { ttl: 10000, strategy: 'short' },
    'algorithm_logs': { ttl: 5000, strategy: 'short' },
    
    // 中频更新数据 - 中等缓存
    'progress': { ttl: 30000, strategy: 'medium' },
    'risk_assessment': { ttl: 60000, strategy: 'medium' },
    
    // 低频更新数据 - 长缓存
    'constraints': { ttl: 120000, strategy: 'long' },
    'knowledge_graph': { ttl: 120000, strategy: 'long' },
    'deliverables': { ttl: 300000, strategy: 'long' }
};

// 智能缓存管理
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.metadata = new Map();
    }
    
    set(key, value, options = {}) {
        const strategy = CACHE_STRATEGIES[key] || { ttl: 60000, strategy: 'medium' };
        const ttl = options.ttl || strategy.ttl;
        
        this.cache.set(key, value);
        this.metadata.set(key, {
            timestamp: Date.now(),
            ttl: ttl,
            strategy: strategy.strategy,
            accessCount: 0
        });
        
        // 设置过期清理
        setTimeout(() => {
            this.delete(key);
        }, ttl);
    }
    
    get(key) {
        if (!this.cache.has(key)) {
            return null;
        }
        
        const metadata = this.metadata.get(key);
        if (Date.now() - metadata.timestamp > metadata.ttl) {
            this.delete(key);
            return null;
        }
        
        // 更新访问计数
        metadata.accessCount++;
        
        return this.cache.get(key);
    }
    
    invalidate(pattern) {
        // 支持模式匹配的缓存失效
        const regex = new RegExp(pattern);
        
        for (const key of this.cache.keys()) {
            if (regex.test(key)) {
                this.delete(key);
            }
        }
    }
}
```

---

## 6. 性能优化

### 请求优化
```javascript
// 请求去重
class RequestDeduplicator {
    constructor() {
        this.pendingRequests = new Map();
    }
    
    async request(key, requestFn) {
        if (this.pendingRequests.has(key)) {
            // 返回正在进行的请求
            return this.pendingRequests.get(key);
        }
        
        const promise = requestFn().finally(() => {
            this.pendingRequests.delete(key);
        });
        
        this.pendingRequests.set(key, promise);
        return promise;
    }
}

// 批量请求
class BatchRequestManager {
    constructor() {
        this.batchQueue = [];
        this.batchTimer = null;
        this.batchDelay = 50; // 50ms内的请求合并
    }
    
    addRequest(request) {
        this.batchQueue.push(request);
        
        if (!this.batchTimer) {
            this.batchTimer = setTimeout(() => {
                this.processBatch();
            }, this.batchDelay);
        }
    }
    
    async processBatch() {
        const batch = this.batchQueue.splice(0);
        this.batchTimer = null;
        
        if (batch.length === 0) return;
        
        // 发送批量请求
        const response = await httpClient.post('/api/batch', {
            requests: batch.map(req => ({
                method: req.method,
                url: req.url,
                data: req.data
            }))
        });
        
        // 分发响应
        response.data.forEach((result, index) => {
            const request = batch[index];
            if (result.success) {
                request.resolve(result.data);
            } else {
                request.reject(new Error(result.error.message));
            }
        });
    }
}
```

### 数据压缩
```javascript
// 响应数据压缩
class DataCompressor {
    static compress(data) {
        // 移除null和undefined值
        return JSON.parse(JSON.stringify(data, (key, value) => {
            return value === null || value === undefined ? undefined : value;
        }));
    }
    
    static decompress(compressedData) {
        // 恢复数据结构
        return compressedData;
    }
}

// 在HttpClient中使用
class HttpClient {
    addResponseInterceptor((data, response) => {
        // 自动解压缩
        if (response.headers.get('content-encoding') === 'custom') {
            return DataCompressor.decompress(data);
        }
        return data;
    });
}
```

---

## 7. 监控和调试

### API调用监控
```javascript
class APIMonitor {
    constructor() {
        this.metrics = {
            requests: new Map(),
            errors: new Map(),
            performance: new Map()
        };
    }
    
    recordRequest(url, method, duration, status) {
        const key = `${method} ${url}`;
        
        if (!this.metrics.requests.has(key)) {
            this.metrics.requests.set(key, {
                count: 0,
                totalDuration: 0,
                errors: 0
            });
        }
        
        const metric = this.metrics.requests.get(key);
        metric.count++;
        metric.totalDuration += duration;
        
        if (status >= 400) {
            metric.errors++;
        }
    }
    
    getMetrics() {
        const result = {};
        
        for (const [key, metric] of this.metrics.requests) {
            result[key] = {
                count: metric.count,
                averageDuration: metric.totalDuration / metric.count,
                errorRate: metric.errors / metric.count,
                successRate: (metric.count - metric.errors) / metric.count
            };
        }
        
        return result;
    }
}

// 在HttpClient中集成监控
class HttpClient {
    constructor() {
        this.monitor = new APIMonitor();
    }
    
    async request(method, url, data, headers) {
        const startTime = Date.now();
        
        try {
            const response = await fetch(url, config);
            const duration = Date.now() - startTime;
            
            this.monitor.recordRequest(url, method, duration, response.status);
            
            return await response.json();
        } catch (error) {
            const duration = Date.now() - startTime;
            this.monitor.recordRequest(url, method, duration, 0);
            throw error;
        }
    }
}
```

### 调试工具
```javascript
// API调试工具
window.apiDebug = {
    // 查看API监控数据
    getMetrics() {
        const dataManager = getAppManager()?.dataManager;
        return dataManager?.httpClient?.monitor?.getMetrics();
    },
    
    // 查看缓存状态
    getCacheStatus() {
        const dataManager = getAppManager()?.dataManager;
        return {
            size: dataManager?.cache?.size,
            keys: Array.from(dataManager?.cache?.keys() || [])
        };
    },
    
    // 清理缓存
    clearCache(pattern) {
        const dataManager = getAppManager()?.dataManager;
        if (pattern) {
            dataManager?.cache?.invalidate(pattern);
        } else {
            dataManager?.cache?.clear();
        }
    },
    
    // 模拟API错误
    simulateError(dataType) {
        const dataManager = getAppManager()?.dataManager;
        dataManager?.handleError(new Error(`Simulated error for ${dataType}`));
    }
};
```

这套API对接规范确保了前后端数据交互的标准化、可靠性和高性能！
