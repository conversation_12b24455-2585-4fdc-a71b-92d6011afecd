# 弃用API检测集成到Checklist的示例

## 问题背景

在Spring Boot 3.4.0中，@MockBean被标记为弃用（for removal in 4.0.0），推荐使用@MockitoBean替代。这类API变更如果在设计和验证阶段没有及时发现，会导致：

1. **编译错误**：在升级Spring Boot版本后代码无法编译
2. **运行时错误**：测试配置失效，Mock行为异常
3. **维护成本**：需要大量手动修改现有代码
4. **技术债务**：使用过时API影响代码质量

## 解决方案：集成到Checklist模板

### 1. 在AI幻觉防护检查中集成

```markdown
## 🚨 AI幻觉防护检查
@checklist-templates:ai_hallucination_prevention

### 弃用API检测（新增）
- [ ] **检测@MockBean使用**
  ```bash
  # 执行目录: c:\ExchangeWorks\xkong\xkongcloud (项目根目录)
  grep -r "@MockBean" src/test/java/
  # 预期结果：无输出或已知的待迁移文件
  ```

- [ ] **验证Spring Boot版本兼容性**
  ```bash
  # 检查Spring Boot版本
  mvn dependency:tree | grep spring-boot-starter
  # 如果版本≥3.4.0，确保不使用@MockBean
  ```

- [ ] **验证替代API可用性**
  ```bash
  # 检查@MockitoBean是否可用
  grep -r "@MockitoBean" src/test/java/ || echo "需要迁移到@MockitoBean"
  ```
```

### 2. 在标准化验证命令中集成

```markdown
## 📋 标准化验证命令
@checklist-templates:standardized_verification_commands

### API兼容性验证（新增）
```bash
# 执行目录: c:\ExchangeWorks\xkong\xkongcloud (项目根目录)

# 1. 弃用API检测
@DEPRECATED_API_DETECTION
grep -r "@MockBean\|@Deprecated" src/test/java/

# 2. 编译验证（确保API兼容）
mvn clean compile

# 3. 测试执行验证（确保Mock正常工作）
mvn clean test

# 4. 依赖版本检查
mvn dependency:tree | grep -E "spring-boot|mockito"
```
```

### 3. 在通用检查列表模板中集成

```markdown
## 📋 通用检查列表模板
@checklist-templates:universal_checklist_template

### 开始前强制检查（更新）
- [ ] 确认当前工作目录位置
- [ ] 明确代码类型和目标目录
- [ ] 激活边界护栏和范围限制
- [ ] 设置认知负载控制点
- [ ] **检测弃用API使用** ⭐ 新增
- [ ] **验证依赖版本兼容性** ⭐ 新增

### 编写过程中检查（每50行）（更新）
- [ ] 验证代码编译通过
- [ ] 检查依赖关系正确性
- [ ] 确认未超出范围边界
- [ ] 清理临时调试代码
- [ ] **避免使用弃用API** ⭐ 新增

### 完成后强制检查（更新）
- [ ] 执行标准化验证命令
- [ ] 确认回滚方案可用
- [ ] 清理所有临时代码
- [ ] 更新文档和记录
- [ ] **最终弃用API扫描** ⭐ 新增
```

## 具体实施示例

### Spring Boot测试配置Checklist

```markdown
# Spring Boot测试配置实施检查清单

**文档更新时间**: 2025-01-15
**用途**: AI记忆辅助和进度跟踪，特别关注API兼容性
**检查频率**: 每完成一个步骤后更新
**关联计划**: Spring Boot测试配置实施计划

## 🚨 AI记忆护栏检查清单
@checklist-templates:ai_memory_guardrail_system

## 🚨 AI执行目录位置提醒（必读）
@checklist-templates:directory_location_reminder_system

## 🚨 弃用API检测专项检查
@DEPRECATED_API_DETECTION

### Spring Boot 3.4.0兼容性检查
- [ ] **检查当前Spring Boot版本**
  ```bash
  # 执行目录: c:\ExchangeWorks\xkong\xkongcloud
  mvn dependency:tree | grep spring-boot-starter | head -1
  ```

- [ ] **扫描@MockBean使用**
  ```bash
  # 检测所有@MockBean使用
  find src/test/java -name "*.java" -exec grep -l "@MockBean" {} \;
  # 预期：列出需要迁移的文件或无输出
  ```

- [ ] **验证@MockitoBean可用性**
  ```bash
  # 检查spring-test依赖
  mvn dependency:tree | grep spring-test
  # 确保版本支持@MockitoBean
  ```

### API迁移执行
- [ ] **替换@MockBean注解**
  - 文件: [具体文件路径]
  - 替换: @MockBean → @MockitoBean
  - 状态: 🔲 未开始 / 🟡 进行中 / ✅ 已完成

- [ ] **更新import语句**
  - 替换: `org.springframework.boot.test.mock.mockito.MockBean`
  - 为: `org.springframework.test.context.bean.override.mockito.MockitoBean`
  - 状态: 🔲 未开始 / 🟡 进行中 / ✅ 已完成

### 验证测试
- [ ] **编译验证**
  ```bash
  mvn clean compile
  # 预期：编译成功，无弃用警告
  ```

- [ ] **测试执行验证**
  ```bash
  mvn clean test
  # 预期：所有测试通过，Mock行为正常
  ```

- [ ] **最终扫描**
  ```bash
  # 确认无@MockBean残留
  grep -r "@MockBean" src/test/java/ || echo "迁移完成"
  ```

## 🔄 回滚方案
@checklist-templates:rollback_mechanism

### 迁移失败回滚
- **方案1**: 恢复原始@MockBean配置
- **方案2**: 临时降级Spring Boot版本
- **方案3**: 使用手动Mock配置替代

## 📊 进度跟踪

### API迁移进度
- @MockBean检测：✅ 已完成
- @MockitoBean迁移：🟡 进行中 (2/5 文件)
- 测试验证：🔲 待开始

### 风险状态
- 🟢 低风险：API迁移路径明确
- 🟡 中风险：需要验证Mock行为一致性
- 🔴 高风险：无（当前无高风险项）
```

## 自动激活机制

当AI遇到以下关键词时，会自动激活弃用API检测：

- @MockBean
- Spring Boot 3.4.0
- deprecated API
- version compatibility
- API migration
- spring test
- mock bean configuration

## 预期效果

1. **提前发现**：在设计阶段就识别弃用API使用
2. **自动检测**：通过命令自动扫描代码中的问题
3. **明确迁移**：提供具体的迁移步骤和验证方法
4. **质量保障**：确保迁移后功能正常，无技术债务
5. **知识积累**：将解决方案沉淀到记忆库，避免重复问题

通过这种方式，我们可以在AI编程过程中主动防范API兼容性问题，提高代码质量和维护效率。
