# PM V2统一架构代码改动清单

## 概述

本文档详细列出了PM V2统一架构重构所需的具体代码改动，包括新增文件、修改文件和删除内容。

---

## 1. 新增文件

### 1.1 PM V2统一架构初始化脚本
**文件路径**: `tools/ace/src/web_interface/static/js/pm_v2_unified_init.js`
**文件大小**: 约150行
**功能**: 统一架构初始化和组件注册

### 1.2 PM V2业务组件集合
**文件路径**: `tools/ace/src/web_interface/static/js/pm_v2_components.js`
**文件大小**: 约800行
**功能**: 包含所有8个PM V2业务组件的实现

---

## 2. 修改文件

### 2.1 HTML模板修改
**文件**: `tools/ace/src/web_interface/templates/project_manager_v2.html`

#### 修改点1: 添加脚本引用 (第2449行之前)
```html
<!-- 在现有脚本引用之前添加 -->
<!-- 统一架构核心 -->
<script src="{{ url_for('static', filename='js/unified/http-client.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified/data-manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified/base-component.js') }}"></script>
<script src="{{ url_for('static', filename='js/unified/app-manager.js') }}"></script>

<!-- PM V2业务组件 -->
<script src="{{ url_for('static', filename='js/pm_v2_components.js') }}"></script>

<!-- PM V2统一架构初始化 -->
<script src="{{ url_for('static', filename='js/pm_v2_unified_init.js') }}"></script>
```

#### 修改点2: 添加容器ID
```html
<!-- 第1831行: 进度监控区域 -->
<div class="grid-area grid-area-1-2 vscode-scrollbar" id="progress-area">

<!-- 第2057行: 风险评估区域 -->
<div class="grid-area grid-area-3 vscode-scrollbar" id="risk-area">

<!-- 第2132行: 项目经理状态区域 -->
<div class="grid-area grid-area-4 vscode-scrollbar" id="manager-area">

<!-- 第1959行: 算法思维日志区域 -->
<div class="grid-area grid-area-5 vscode-scrollbar" id="algorithm-area">

<!-- 第2170行: 约束审查区域 -->
<div class="grid-area grid-area-6 vscode-scrollbar" id="constraint-area">

<!-- 第2180行: 知识库区域 -->
<div class="grid-area grid-area-7 vscode-scrollbar" id="knowledge-area">

<!-- 第2343行: 控制区域 -->
<div class="grid-area grid-area-8" id="control-area">

<!-- 第2386行: 交付结果区域 -->
<div class="grid-area grid-area-9 vscode-scrollbar" id="deliverables-area">
```

### 2.2 传统业务逻辑修改
**文件**: `tools/ace/src/web_interface/static/js/project_manager_v2_app.js`

#### 修改点1: 添加兼容性检查 (第1行之后)
```javascript
// 兼容性检查：如果统一架构已初始化，则跳过传统初始化
if (window.pmV2AppManager) {
    console.log('统一架构已初始化，跳过传统模式初始化');
    
    // 设置桥接函数，供传统代码调用
    setupUnifiedArchitectureBridge();
    
    // 跳过后续的传统初始化
    return;
} else {
    console.log('使用传统模式初始化');
}
```

#### 修改点2: WebSocket处理桥接 (第4-5行修改)
```javascript
// 原有代码
const socket = io();

// 修改为
const socket = io();

// 添加统一架构桥接
function bridgeWebSocketToUnified(eventType, data) {
    if (window.pmV2AppManager) {
        window.pmV2AppManager.dataManager.handleWebSocketMessage({
            type: eventType,
            data: data,
            timestamp: new Date().toISOString()
        });
        return true;
    }
    return false;
}
```

#### 修改点3: 事件处理桥接 (在所有socket.on之前添加)
```javascript
// 为所有WebSocket事件添加桥接
const originalSocketOn = socket.on.bind(socket);
socket.on = function(eventType, handler) {
    return originalSocketOn(eventType, function(data) {
        // 尝试桥接到统一架构
        if (!bridgeWebSocketToUnified(eventType, data)) {
            // 如果桥接失败，使用传统处理
            handler(data);
        }
    });
};
```

#### 修改点4: 添加桥接函数 (文件末尾添加)
```javascript
// 统一架构桥接函数
function setupUnifiedArchitectureBridge() {
    // 将传统函数桥接到统一架构
    window.makeDecision = function(decision) {
        const controlComponent = window.pmV2AppManager.getComponent('control');
        if (controlComponent && controlComponent.makeDecision) {
            controlComponent.makeDecision(decision);
        }
    };
    
    window.showConstraintDetail = function(constraintId) {
        const constraintComponent = window.pmV2AppManager.getComponent('constraint');
        if (constraintComponent && constraintComponent.selectConstraint) {
            constraintComponent.selectConstraint(constraintId);
        }
    };
    
    // 其他需要桥接的函数...
}
```

### 2.3 通用函数库修改
**文件**: `tools/ace/src/web_interface/static/js/nine_grid_base.js`

#### 修改点1: 添加统一架构检测 (第5行之后)
```javascript
/**
 * 检查是否在统一架构环境中
 * @returns {boolean} 是否使用统一架构
 */
function isUnifiedArchitecture() {
    return window.pmV2AppManager && window.pmV2AppManager.isInitialized;
}
```

#### 修改点2: 修改约束详情显示函数 (第700行附近)
```javascript
// 原有函数
function showConstraintDetail(constraintId) {
    console.log('显示约束详情:', constraintId);
    // 原有代码...
}

// 修改为
function showConstraintDetail(constraintId) {
    if (isUnifiedArchitecture()) {
        // 使用统一架构的约束组件
        const constraintComponent = window.pmV2AppManager.getComponent('constraint');
        if (constraintComponent && constraintComponent.selectConstraint) {
            constraintComponent.selectConstraint(constraintId);
            return;
        }
    }
    
    // 传统处理方式
    console.log('显示约束详情:', constraintId);
    // 原有代码...
}
```

#### 修改点3: 修改其他交互函数
```javascript
// 修改提示框函数，支持统一架构
function showTooltip(element, text, tooltipId = 'node-tooltip') {
    if (isUnifiedArchitecture()) {
        // 统一架构可能有自己的提示框实现
        const knowledgeComponent = window.pmV2AppManager.getComponent('knowledge');
        if (knowledgeComponent && knowledgeComponent.showTooltip) {
            knowledgeComponent.showTooltip(element, text);
            return;
        }
    }
    
    // 传统提示框逻辑
    const tooltip = document.getElementById(tooltipId);
    // 原有代码...
}
```

---

## 3. 数据类型配置

### 3.1 DataManager配置扩展
**文件**: `tools/ace/src/web_interface/static/js/unified/data-manager.js`

#### 添加PM V2数据类型映射
```javascript
// 在DataManager构造函数中添加
const PM_V2_DATA_TYPE_MAPPING = {
    // 进度相关
    'progress': '/api/v2/projects/{projectId}/progress',
    'stage_metrics': '/api/v2/projects/{projectId}/stage-metrics',
    
    // 风险评估
    'risk_assessment': '/api/v2/projects/{projectId}/risk-assessment',
    'health_report': '/api/v2/projects/{projectId}/health-report',
    
    // 约束管理
    'constraints': '/api/v2/projects/{projectId}/constraints',
    'constraint_detail': '/api/v2/projects/{projectId}/constraints/{constraintId}',
    
    // 知识库
    'knowledge_graph': '/api/v2/projects/{projectId}/knowledge-graph',
    'graph_layout': '/api/v2/projects/{projectId}/knowledge-graph/layout',
    
    // 管理器状态
    'manager_status': '/api/v2/projects/{projectId}/manager-status',
    'task_status': '/api/v2/projects/{projectId}/current-task',
    
    // 算法日志
    'algorithm_logs': '/api/v2/projects/{projectId}/algorithm-logs',
    'log_detail': '/api/v2/projects/{projectId}/algorithm-logs/{logId}',
    
    // 控制状态
    'control_status': '/api/v2/projects/{projectId}/control-status',
    
    // 交付结果
    'deliverables': '/api/v2/projects/{projectId}/deliverables',
    'file_download': '/files/{fileId}/download'
};

// 注册数据类型
Object.entries(PM_V2_DATA_TYPE_MAPPING).forEach(([type, endpoint]) => {
    this.registerDataType(type, endpoint);
});
```

### 3.2 WebSocket事件映射
```javascript
// 在DataManager中添加PM V2事件映射
const PM_V2_WEBSOCKET_EVENT_MAPPING = {
    // 进度事件
    'stage_progress_update': 'progress',
    'stage_zero_metrics_update': 'progress',
    'key_metrics_update': 'progress',
    
    // 风险事件
    'reliability_score_update': 'risk_assessment',
    'risk_prevented': 'risk_assessment',
    'health_report_generated': 'risk_assessment',
    
    // 约束事件
    'constraint_created': 'constraints',
    'constraint_updated': 'constraints',
    'constraint_validated': 'constraints',
    
    // 知识库事件
    'knowledge_graph_update': 'knowledge_graph',
    'node_relationship_update': 'knowledge_graph',
    
    // 管理器事件
    'manager_status_update': 'manager_status',
    'current_task_update': 'manager_status',
    'pm_decision_required': 'manager_status',
    
    // 算法事件
    'algorithm_log_entry': 'algorithm_logs',
    'ai_algorithm_collaboration_update': 'algorithm_logs',
    'python_commander_thinking': 'algorithm_logs',
    
    // 交付事件
    'deliverable_ready': 'deliverables',
    'processing_complete': 'deliverables',
    'audit_report_generated': 'deliverables'
};

// 扩展事件映射
Object.assign(this.websocketEventMapping, PM_V2_WEBSOCKET_EVENT_MAPPING);
```

### 3.3 缓存策略配置
```javascript
// 在DataManager中添加PM V2缓存策略
const PM_V2_CACHE_STRATEGIES = {
    // 高频更新数据 - 短缓存
    'manager_status': { ttl: 10000, strategy: 'short' },
    'algorithm_logs': { ttl: 5000, strategy: 'short' },
    'control_status': { ttl: 15000, strategy: 'short' },
    
    // 中频更新数据 - 中等缓存
    'progress': { ttl: 30000, strategy: 'medium' },
    'risk_assessment': { ttl: 60000, strategy: 'medium' },
    'task_status': { ttl: 20000, strategy: 'medium' },
    
    // 低频更新数据 - 长缓存
    'constraints': { ttl: 120000, strategy: 'long' },
    'knowledge_graph': { ttl: 120000, strategy: 'long' },
    'deliverables': { ttl: 300000, strategy: 'long' },
    'health_report': { ttl: 180000, strategy: 'long' }
};

// 应用缓存策略
Object.assign(this.cacheStrategies, PM_V2_CACHE_STRATEGIES);
```

---

## 4. 文件大小估算

| 文件类型 | 文件数量 | 预估大小 | 总大小 |
|----------|----------|----------|--------|
| 新增JS文件 | 2 | 950行 | 约40KB |
| 修改HTML文件 | 1 | +20行 | +1KB |
| 修改JS文件 | 2 | +150行 | +6KB |
| 配置扩展 | 1 | +100行 | +4KB |
| **总计** | **6** | **1220行** | **约51KB** |

---

## 5. 改动影响分析

### 5.1 向后兼容性
- ✅ **完全兼容**: 传统模式继续工作
- ✅ **平滑切换**: 自动检测和降级
- ✅ **功能保持**: 所有现有功能保持不变

### 5.2 性能影响
- ✅ **加载优化**: 按需加载组件
- ✅ **缓存提升**: 智能缓存减少网络请求
- ✅ **渲染优化**: 防抖更新减少重绘

### 5.3 维护成本
- ✅ **代码减少**: 消除重复代码
- ✅ **标准化**: 统一的开发模式
- ✅ **可测试性**: 组件化便于单元测试

---

## 6. 实施检查清单

### 6.1 Phase 1: 基础设施
- [ ] 创建 `pm_v2_unified_init.js`
- [ ] 创建 `pm_v2_components.js`
- [ ] 修改 `project_manager_v2.html` 添加脚本引用
- [ ] 修改 `project_manager_v2.html` 添加容器ID
- [ ] 配置数据类型映射

### 6.2 Phase 2: 组件实现
- [ ] 实现 PMV2ProgressComponent
- [ ] 实现 PMV2RiskComponent
- [ ] 实现 PMV2ManagerComponent
- [ ] 实现 PMV2AlgorithmComponent
- [ ] 实现 PMV2ConstraintComponent
- [ ] 实现 PMV2KnowledgeComponent
- [ ] 实现 PMV2ControlComponent
- [ ] 实现 PMV2DeliverablesComponent

### 6.3 Phase 3: 兼容性
- [ ] 修改 `project_manager_v2_app.js` 添加兼容性检查
- [ ] 修改 `nine_grid_base.js` 添加统一架构支持
- [ ] 实现WebSocket桥接
- [ ] 实现函数桥接

### 6.4 Phase 4: 测试验证
- [ ] 功能完整性测试
- [ ] 兼容性切换测试
- [ ] 性能对比测试
- [ ] 用户体验测试

### 6.5 Phase 5: 部署上线
- [ ] 代码审查通过
- [ ] 测试环境验证
- [ ] 生产环境部署
- [ ] 监控告警配置

---

## 7. 总结

这个代码改动清单提供了PM V2统一架构重构的具体实施指导：

1. **改动范围明确**: 6个文件，1220行代码，约51KB
2. **风险可控**: 保持完全向后兼容
3. **实施简单**: 分阶段执行，每步可验证
4. **质量保证**: 完整的检查清单和测试策略

通过这些具体的代码改动，PM V2将成功迁移到统一架构，获得更好的可维护性、扩展性和性能表现。
