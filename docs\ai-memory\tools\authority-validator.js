/**
 * Authority Validator for docs/common → docs/ai-memory Consistency
 * 
 * This tool validates that all docs/ai-memory content is traceable to docs/common
 * sources and maintains consistency with authoritative standards.
 */

const fs = require('fs');
const path = require('path');

class AuthorityValidator {
    constructor() {
        this.authorityRules = this.loadAuthorityRules();
        this.validationResults = {
            passed: [],
            failed: [],
            warnings: []
        };
    }

    loadAuthorityRules() {
        try {
            const rulesPath = path.join(__dirname, 'authority-rules.json');
            return JSON.parse(fs.readFileSync(rulesPath, 'utf8'));
        } catch (error) {
            console.error('Failed to load authority rules:', error);
            return null;
        }
    }

    async validateAll() {
        console.log('Starting comprehensive authority validation...');
        
        this.validationResults = { passed: [], failed: [], warnings: [] };
        
        // Validate L1-core files
        await this.validateL1CoreFiles();
        
        // Validate L2-context files
        await this.validateL2ContextFiles();
        
        // Validate L3-index files
        await this.validateL3IndexFiles();
        
        // Generate validation report
        return this.generateValidationReport();
    }

    async validateL1CoreFiles() {
        const l1CorePath = 'docs/ai-memory/L1-core';
        const files = [
            'global-constraints.json',
            'key-patterns.json',
            'attention-commands.json',
            'project-matrix.json'
        ];

        for (const file of files) {
            const filePath = path.join(l1CorePath, file);
            await this.validateFile(filePath, 'L1-core');
        }
    }

    async validateL2ContextFiles() {
        const l2ContextPath = 'docs/ai-memory/L2-context';
        const subdirs = ['task-types', 'tech-stack', 'project-groups', 'automation'];

        for (const subdir of subdirs) {
            const subdirPath = path.join(l2ContextPath, subdir);
            if (fs.existsSync(subdirPath)) {
                const files = fs.readdirSync(subdirPath).filter(f => f.endsWith('.json'));
                for (const file of files) {
                    const filePath = path.join(subdirPath, file);
                    await this.validateFile(filePath, 'L2-context');
                }
            }
        }
    }

    async validateL3IndexFiles() {
        const l3IndexPath = 'docs/ai-memory/L3-index';
        const subdirs = ['feature-index', 'keyword-index', 'dependency-index'];

        for (const subdir of subdirs) {
            const subdirPath = path.join(l3IndexPath, subdir);
            if (fs.existsSync(subdirPath)) {
                this.validateIndexDirectory(subdirPath, 'L3-index');
            }
        }
    }

    async validateFile(filePath, layer) {
        if (!fs.existsSync(filePath)) {
            this.validationResults.warnings.push({
                file: filePath,
                issue: 'File does not exist',
                layer: layer
            });
            return;
        }

        // Skip .md files for JSON validation - they use different validation rules
        if (filePath.endsWith('.md')) {
            await this.validateMarkdownFile(filePath, layer);
            return;
        }

        try {
            const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
            
            // Check for authority metadata
            const authorityCheck = this.validateAuthorityMetadata(content, filePath);
            
            // Check for traceability to docs/common
            const traceabilityCheck = this.validateTraceability(content, filePath);
            
            // Check for consistency with docs/common sources
            const consistencyCheck = await this.validateConsistency(content, filePath);
            
            if (authorityCheck.valid && traceabilityCheck.valid && consistencyCheck.valid) {
                this.validationResults.passed.push({
                    file: filePath,
                    layer: layer,
                    checks: ['authority', 'traceability', 'consistency']
                });
            } else {
                this.validationResults.failed.push({
                    file: filePath,
                    layer: layer,
                    issues: [
                        ...(!authorityCheck.valid ? [authorityCheck.issue] : []),
                        ...(!traceabilityCheck.valid ? [traceabilityCheck.issue] : []),
                        ...(!consistencyCheck.valid ? [consistencyCheck.issue] : [])
                    ]
                });
            }
            
        } catch (error) {
            this.validationResults.failed.push({
                file: filePath,
                layer: layer,
                issues: [`JSON parsing error: ${error.message}`]
            });
        }
    }

    async validateMarkdownFile(filePath, layer) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');

            // For .md files, check for sync status metadata in the content
            const hasSyncStatus = content.includes('## 同步状态') || content.includes('sync_status');
            const hasSourceInfo = content.includes('同步来源') || content.includes('source_file');

            if (hasSyncStatus && hasSourceInfo) {
                this.validationResults.passed.push({
                    file: filePath,
                    layer: layer,
                    checks: ['markdown_metadata']
                });
            } else {
                this.validationResults.warnings.push({
                    file: filePath,
                    issue: 'Markdown file missing sync status or source information',
                    layer: layer
                });
            }

        } catch (error) {
            this.validationResults.failed.push({
                file: filePath,
                layer: layer,
                issues: [`Markdown file read error: ${error.message}`]
            });
        }
    }

    validateAuthorityMetadata(content, filePath) {
        // Check if file contains proper authority metadata
        const metadata = content.metadata?.authority_metadata || content.authority_metadata;
        
        if (!metadata) {
            return {
                valid: false,
                issue: 'Missing authority metadata section'
            };
        }

        const requiredFields = ['source_file', 'last_sync_date', 'sync_status'];
        const missingFields = requiredFields.filter(field => !metadata[field]);
        
        if (missingFields.length > 0) {
            return {
                valid: false,
                issue: `Missing required authority fields: ${missingFields.join(', ')}`
            };
        }

        return { valid: true };
    }

    validateTraceability(content, filePath) {
        // Check if content can be traced back to docs/common sources
        const metadata = content.metadata?.authority_metadata || content.authority_metadata;
        
        if (!metadata || !metadata.source_file) {
            return {
                valid: false,
                issue: 'No source file traceability information'
            };
        }

        const sourceFile = metadata.source_file;
        if (!sourceFile.startsWith('docs/common/')) {
            return {
                valid: false,
                issue: `Source file ${sourceFile} is not in docs/common/`
            };
        }

        // Check if source file exists
        if (!fs.existsSync(sourceFile)) {
            return {
                valid: false,
                issue: `Source file ${sourceFile} does not exist`
            };
        }

        return { valid: true };
    }

    async validateConsistency(content, filePath) {
        // This would implement detailed consistency checking
        // For now, we perform basic validation

        const metadata = content.metadata?.authority_metadata || content.authority_metadata;
        if (!metadata) {
            return { valid: false, issue: 'No metadata for consistency check' };
        }

        // Check sync status
        const syncStatus = metadata.sync_status;
        if (syncStatus === 'requires_update' || syncStatus === 'sync_failed') {
            return {
                valid: false,
                issue: `File requires synchronization (status: ${syncStatus})`
            };
        }

        return { valid: true };
    }

    validateIndexDirectory(dirPath, layer) {
        // Validate index directory structure and content
        const files = fs.readdirSync(dirPath, { withFileTypes: true });
        
        for (const file of files) {
            if (file.isFile() && (file.name.endsWith('.json') || file.name.endsWith('.md'))) {
                const filePath = path.join(dirPath, file.name);
                this.validateFile(filePath, layer);
            } else if (file.isDirectory()) {
                this.validateIndexDirectory(path.join(dirPath, file.name), layer);
            }
        }
    }

    generateValidationReport() {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                total_files: this.validationResults.passed.length + this.validationResults.failed.length,
                passed: this.validationResults.passed.length,
                failed: this.validationResults.failed.length,
                warnings: this.validationResults.warnings.length
            },
            details: this.validationResults,
            recommendations: this.generateRecommendations()
        };

        console.log('\n=== Authority Validation Report ===');
        console.log(`Total files validated: ${report.summary.total_files}`);
        console.log(`Passed: ${report.summary.passed}`);
        console.log(`Failed: ${report.summary.failed}`);
        console.log(`Warnings: ${report.summary.warnings}`);

        if (report.summary.failed > 0) {
            console.log('\nFailed validations:');
            this.validationResults.failed.forEach(failure => {
                console.log(`  ${failure.file}: ${failure.issues.join(', ')}`);
            });
        }

        if (report.summary.warnings > 0) {
            console.log('\nWarnings:');
            this.validationResults.warnings.forEach(warning => {
                console.log(`  ${warning.file}: ${warning.issue}`);
            });
        }

        return report;
    }

    generateRecommendations() {
        const recommendations = [];
        
        if (this.validationResults.failed.length > 0) {
            recommendations.push('Update failed files to include proper authority metadata');
            recommendations.push('Ensure all AI memory files are traceable to docs/common sources');
            recommendations.push('Run authority sync to update inconsistent files');
        }

        if (this.validationResults.warnings.length > 0) {
            recommendations.push('Review warning files for potential issues');
        }

        return recommendations;
    }

    async fixAuthorityIssues() {
        console.log('Attempting to fix authority issues...');
        
        for (const failure of this.validationResults.failed) {
            await this.attemptFix(failure);
        }
        
        console.log('Authority fix attempt completed');
    }

    async attemptFix(failure) {
        // This would implement automatic fixing of common authority issues
        console.log(`Attempting to fix: ${failure.file}`);
        
        // For now, we just log the intended action
        console.log(`Would fix issues: ${failure.issues.join(', ')}`);
    }
}

// Export for use as a module
module.exports = AuthorityValidator;

// CLI usage
if (require.main === module) {
    const validator = new AuthorityValidator();
    
    const command = process.argv[2];
    
    if (command === 'validate') {
        validator.validateAll().then(report => {
            process.exit(report.summary.failed > 0 ? 1 : 0);
        });
    } else if (command === 'fix') {
        validator.validateAll().then(() => {
            return validator.fixAuthorityIssues();
        });
    } else {
        console.log('Usage: node authority-validator.js [validate|fix]');
        process.exit(1);
    }
}
