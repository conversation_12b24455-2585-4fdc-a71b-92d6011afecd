/* 九宫格基础样式 - 提取自nine_grid.html */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 确保html和body占满全屏 */
html, body {
    height: 100%;
    overflow: hidden;
}

/* 撑满全屏九宫格布局 - 三等分布局 */
.nine-grid-container {
    width: 100vw;
    height: 100vh;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    grid-gap: 2px;
    overflow: hidden;
    background-color: #1E1F22;
    grid-template-areas:
        "area1-2 area5   area3"
        "area4   area5   area6"
        "area7   area8   area9";
}

/* 九宫格区域基础样式 */
.grid-area {
    background-color: #2A2D30;
    border: 1px solid #3C3F41;
    padding: 0.1rem 1rem 1rem 1rem;
    overflow: hidden; /* 防止网格区域本身滚动，内容滚动由area-content处理 */
    color: #BBBBBB;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    display: flex;
    flex-direction: column; /* 确保内容垂直排列 */
}

/* 特定区域的grid-area定义 */
.grid-area-1-2 { grid-area: area1-2; }
.grid-area-3 { grid-area: area3; }
.grid-area-4 { grid-area: area4; }
.grid-area-5 { grid-area: area5; }
.grid-area-6 { grid-area: area6; }
.grid-area-7 { grid-area: area7; }
.grid-area-8 { grid-area: area8; }
.grid-area-9 { grid-area: area9; }

/* 区域标题样式 */
.area-title {
    font-size: calc(0.5rem + 0.15vw);
    font-weight: bold;
    color: #0078D4;
    margin: 0; /* 移除margin避免影响flex布局 */
    border-bottom: 1px solid #3C3F41;
    padding-bottom: 0.2rem;
    flex-shrink: 0; /* 防止标题被压缩 */
}

/* 区域内容样式 */
.area-content {
    font-size: calc(0.8rem + 0.2vw);
    line-height: 1.4;
    flex: 1; /* 占据剩余空间 */
    overflow-y: auto; /* 允许垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动条 */
    padding-right: 0.5rem; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.area-content::-webkit-scrollbar {
    width: 6px;
}

.area-content::-webkit-scrollbar-track {
    background: #2A2D30;
    border-radius: 3px;
}

.area-content::-webkit-scrollbar-thumb {
    background: #4A4D50;
    border-radius: 3px;
}

.area-content::-webkit-scrollbar-thumb:hover {
    background: #5A5D60;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-active { background-color: #4CAF50; }
.status-thinking { background-color: #FF9800; }
.status-converging { background-color: #2196F3; }
.status-completed { background-color: #9C27B0; }
.status-warning { background-color: #F44336; }

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #393B40;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    height: 100%;
    background-color: #0078D4;
    transition: width 0.3s ease;
}

/* 自动隐藏左侧菜单 */
.left-menu-trigger {
    position: fixed;
    left: 0;
    top: 0;
    width: 10px;
    height: 100vh;
    z-index: 999;
    background: transparent;
    cursor: pointer;
}

.left-menu {
    position: fixed;
    left: -250px;
    top: 0;
    width: 250px;
    height: 100vh;
    background: rgba(30, 31, 34, 0.95);
    backdrop-filter: blur(10px);
    border-right: 1px solid #3C3F41;
    z-index: 1000;
    transition: transform 0.3s ease;
    padding: 1rem;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

.left-menu.show {
    transform: translateX(250px);
}

.menu-item {
    display: block;
    color: #BBBBBB;
    text-decoration: none;
    padding: 0.8rem 1rem;
    margin: 0.2rem 0;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.menu-item:hover {
    background-color: #393B40;
    color: #0078D4;
    transform: translateX(5px);
}

.menu-title {
    color: #0078D4;
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #3C3F41;
}

/* VSCode风格滚动条 */
.vscode-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #424242 #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar {
    width: 14px;
}

.vscode-scrollbar::-webkit-scrollbar-track {
    background: #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb {
    background-color: #424242;
    border-radius: 0px;
    border: 3px solid #1E1F22;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #4F4F4F;
}

.vscode-scrollbar::-webkit-scrollbar-thumb:active {
    background-color: #6C6C6C;
}

/* 线框按钮hover效果 */
.control-buttons button:hover {
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* 基础按钮样式 */
.control-buttons {
    display: flex;
    gap: 0.3rem;
}

.control-buttons button {
    flex: 1;
    padding: 0.3rem;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s;
    font-size: 0.8rem;
}

.control-buttons button:hover {
    opacity: 1;
}

.control-buttons button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* 通用进度条组件样式 */
.progress-bar {
    height: 8px;
    background: #2A2D30;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.3rem;
    position: relative;
}

.progress-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 4px;
}

.progress-fill.success {
    background: linear-gradient(90deg, #4CAF50, #81C784);
}

.progress-fill.warning {
    background: linear-gradient(90deg, #FF9800, #FFB74D);
}

.progress-fill.info {
    background: linear-gradient(90deg, #2196F3, #64B5F6);
}

.progress-fill.danger {
    background: linear-gradient(90deg, #F44336, #EF5350);
}

.progress-fill.primary {
    background: linear-gradient(90deg, #0078D4, #40A9FF);
}

/* 通用状态指示器样式 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-indicator.status-active {
    background: #4CAF50;
    box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
}

.status-indicator.status-pending {
    background: #FF9800;
    box-shadow: 0 0 4px rgba(255, 152, 0, 0.5);
}

.status-indicator.status-error {
    background: #F44336;
    box-shadow: 0 0 4px rgba(244, 67, 54, 0.5);
}

.status-indicator.status-thinking {
    background: #2196F3;
    box-shadow: 0 0 4px rgba(33, 150, 243, 0.5);
    animation: pulse-thinking 2s infinite;
}

.status-indicator.status-converging {
    background: #9C27B0;
    box-shadow: 0 0 4px rgba(156, 39, 176, 0.5);
    animation: pulse-converging 1.5s infinite;
}

@keyframes pulse-thinking {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes pulse-converging {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

/* 通用圆形进度环样式 */
.circular-progress {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem auto;
}

.circular-progress svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circular-progress .progress-circle {
    fill: none;
    stroke-width: 6;
    stroke-linecap: round;
}

.circular-progress .progress-bg {
    stroke: #2A2D30;
}

.circular-progress .progress-bar-circle {
    stroke-dasharray: 226;
    stroke-dashoffset: 226;
    transition: stroke-dashoffset 0.5s ease;
}

.circular-progress .progress-bar-circle.success {
    stroke: #4CAF50;
}

.circular-progress .progress-bar-circle.warning {
    stroke: #FF9800;
}

.circular-progress .progress-bar-circle.danger {
    stroke: #F44336;
}

.circular-progress .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.circular-progress .progress-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #BBBBBB;
}

.circular-progress .progress-label {
    font-size: 0.7rem;
    color: #888;
}

/* 通用风险列表样式 */
.risk-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.4rem;
    padding: 0.3rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 3px;
    border-left: 3px solid;
}

.risk-item.critical {
    border-left-color: #F44336;
}

.risk-item.high {
    border-left-color: #FF9800;
}

.risk-item.medium {
    border-left-color: #2196F3;
}

.risk-item.low {
    border-left-color: #4CAF50;
}

.risk-badge {
    display: inline-block;
    padding: 0.1rem 0.3rem;
    border-radius: 3px;
    font-size: 0.6rem;
    font-weight: bold;
    margin-right: 0.5rem;
    min-width: 50px;
    text-align: center;
}

.risk-badge.critical {
    background: #F44336;
    color: white;
}

.risk-badge.high {
    background: #FF9800;
    color: white;
}

.risk-badge.medium {
    background: #2196F3;
    color: white;
}

.risk-badge.low {
    background: #4CAF50;
    color: white;
}

/* 通用微动画效果 */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(0, 120, 212, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 15px rgba(0, 120, 212, 0.6);
        transform: scale(1.02);
    }
}

@keyframes float-up {
    0% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
    100% { transform: translateY(0); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in-right {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* 处理中状态动画 */
.processing {
    animation: pulse-glow 2s infinite;
}

/* 按钮悬停增强效果 */
.control-buttons button {
    position: relative;
    overflow: hidden;
}

.control-buttons button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.control-buttons button:hover::before {
    left: 100%;
}

.control-buttons button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 进度条动画增强 */
.progress-fill {
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

/* 状态指示器脉冲增强 */
.status-indicator.status-active {
    animation: pulse-glow 3s infinite;
}

/* 圆形进度环动画 */
.progress-bar-circle {
    animation: draw-circle 2s ease-out;
}

@keyframes draw-circle {
    from { stroke-dashoffset: 226; }
    to { stroke-dashoffset: 79.1; }
}

/* 数据更新闪烁效果 */
.data-update {
    animation: pulse-highlight 0.8s ease-out;
}

@keyframes pulse-highlight {
    0% { background-color: rgba(0, 120, 212, 0.3); }
    100% { background-color: transparent; }
}

/* 通用详细区样式 (区域8) */
#detail-area {
    position: relative;
}

#detail-title {
    position: absolute;
    top: 4px;
    left: 8px;
    background: #3C3F41;
    color: #BBBBBB;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7rem;
    z-index: 10;
}

#detail-content {
    padding: 0.5rem;
    font-family: monospace;
    font-size: 0.8rem;
    color: #BBBBBB;
    line-height: 1.3;
    height: 100%;
    display: flex;
    flex-direction: column;
}

#detail-placeholder {
    color: #666;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}