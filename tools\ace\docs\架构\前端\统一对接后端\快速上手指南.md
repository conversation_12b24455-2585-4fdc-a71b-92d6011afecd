# 前端统一架构快速上手指南

## 5分钟快速开始

### 第一步：引入核心文件
```html
<!-- 在你的HTML页面中添加以下脚本 -->
<script src="static/js/unified/http-client.js"></script>
<script src="static/js/unified/data-manager.js"></script>
<script src="static/js/unified/base-component.js"></script>
<script src="static/js/unified/app-manager.js"></script>
```

### 第二步：创建你的第一个组件
```javascript
// static/js/unified/components/my-component.js
class MyComponent extends BaseComponent {
    getDataTypes() {
        return ['progress']; // 指定需要的数据类型
    }
    
    render() {
        const data = this.getData('progress');
        this.container.innerHTML = `
            <div>我的组件</div>
            <div>数据: ${JSON.stringify(data)}</div>
        `;
    }
}
```

### 第三步：初始化应用
```javascript
// 在页面加载完成后
document.addEventListener('DOMContentLoaded', async () => {
    // 初始化应用
    const appManager = await initializeApp('my-project-id');
    
    // 注册组件
    appManager.registerComponent('my', 'my-container', MyComponent);
    
    console.log('应用启动成功！');
});
```

### 第四步：在HTML中添加容器
```html
<div id="my-container">加载中...</div>
```

**完成！** 你的第一个组件已经可以工作了。

---

## 常用模式

### 模式1：显示列表数据
```javascript
class ListComponent extends BaseComponent {
    getDataTypes() {
        return ['items'];
    }
    
    render() {
        const items = this.getData('items') || [];
        this.container.innerHTML = `
            <div class="list-container">
                ${items.map(item => `
                    <div class="list-item">
                        <h3>${item.title}</h3>
                        <p>${item.description}</p>
                    </div>
                `).join('')}
            </div>
        `;
    }
}
```

### 模式2：处理用户交互
```javascript
class InteractiveComponent extends BaseComponent {
    getDataTypes() {
        return ['data'];
    }
    
    render() {
        this.container.innerHTML = `
            <button id="refresh-btn">刷新数据</button>
            <div id="content"></div>
        `;
    }
    
    bindEvents() {
        const refreshBtn = this.find('#refresh-btn');
        refreshBtn.addEventListener('click', () => {
            this.refresh(); // 刷新数据
        });
    }
}
```

### 模式3：实时数据更新
```javascript
class RealtimeComponent extends BaseComponent {
    getDataTypes() {
        return ['realtime_data'];
    }
    
    onDataUpdate(dataType, data, oldData) {
        if (dataType === 'realtime_data') {
            // 数据更新时自动调用
            this.updateDisplay(data);
        }
    }
    
    updateDisplay(data) {
        const content = this.find('#content');
        content.innerHTML = `最新数据: ${data.value}`;
        
        // 添加更新动画
        content.classList.add('updated');
        setTimeout(() => content.classList.remove('updated'), 500);
    }
}
```

---

## 迁移现有代码

### 从现有函数迁移
```javascript
// 原有代码
function updateProgress(data) {
    document.getElementById('progress').innerHTML = `进度: ${data.percentage}%`;
}

// 迁移到组件
class ProgressComponent extends BaseComponent {
    getDataTypes() {
        return ['progress'];
    }
    
    render() {
        const data = this.getData('progress');
        if (data) {
            this.updateProgress(data); // 直接复用原有函数
        }
    }
    
    // 复制原有函数，无需修改
    updateProgress(data) {
        this.container.innerHTML = `进度: ${data.percentage}%`;
    }
}
```

### 从jQuery迁移
```javascript
// 原有jQuery代码
$(document).ready(function() {
    $.get('/api/data', function(data) {
        $('#container').html('<div>' + data.message + '</div>');
    });
});

// 迁移到统一架构
class DataComponent extends BaseComponent {
    getDataTypes() {
        return ['data'];
    }
    
    render() {
        const data = this.getData('data');
        if (data) {
            this.container.innerHTML = `<div>${data.message}</div>`;
        }
    }
}
```

---

## 调试技巧

### 在浏览器控制台中调试
```javascript
// 查看应用状态
debug.getAppStatus()

// 查看特定组件
debug.getComponent('progress')

// 查看数据管理器
debug.getDataManager()

// 刷新所有组件
debug.refreshAll()

// 重试失败的组件
debug.retryComponent('progress')
```

### 查看组件数据
```javascript
// 在组件的render方法中添加调试信息
render() {
    const data = this.getData('progress');
    console.log('Component data:', data); // 调试输出
    
    // 渲染逻辑...
}
```

### 监听数据变化
```javascript
// 在组件中监听数据变化
onDataUpdate(dataType, data, oldData) {
    console.log(`Data updated: ${dataType}`, { data, oldData });
    super.onDataUpdate(dataType, data, oldData);
}
```

---

## 常见问题解决

### 问题1：组件不显示
**检查清单：**
- [ ] HTML中是否有对应的容器元素？
- [ ] 容器ID是否正确？
- [ ] 组件是否正确注册？
- [ ] 浏览器控制台是否有错误？

**解决方法：**
```javascript
// 检查容器是否存在
const container = document.getElementById('my-container');
console.log('Container exists:', !!container);

// 检查组件是否注册
const component = debug.getComponent('my');
console.log('Component registered:', !!component);
```

### 问题2：数据不更新
**检查清单：**
- [ ] getDataTypes()方法是否返回正确的数据类型？
- [ ] 后端API是否正常返回数据？
- [ ] WebSocket连接是否正常？

**解决方法：**
```javascript
// 检查数据类型
console.log('Required data types:', this.getDataTypes());

// 检查数据是否存在
const data = this.getData('progress');
console.log('Current data:', data);

// 手动刷新数据
await this.refresh('progress');
```

### 问题3：WebSocket连接失败
**解决方法：**
```javascript
// 检查WebSocket状态
const dataManager = debug.getDataManager();
console.log('WebSocket state:', dataManager.wsClient.readyState);

// 手动重连
dataManager.setupWebSocket();
```

---

## 最佳实践

### 1. 组件命名
```javascript
// ✅ 好的命名
class UserProfileComponent extends BaseComponent {}
class OrderListComponent extends BaseComponent {}
class PaymentFormComponent extends BaseComponent {}

// ❌ 不好的命名
class Component1 extends BaseComponent {}
class MyComp extends BaseComponent {}
class UserThing extends BaseComponent {}
```

### 2. 数据处理
```javascript
// ✅ 好的做法
render() {
    const data = this.getData('users');
    
    // 检查数据是否存在
    if (!data || !Array.isArray(data.users)) {
        this.renderEmptyState();
        return;
    }
    
    // 渲染数据
    this.renderUserList(data.users);
}

// ❌ 不好的做法
render() {
    const data = this.getData('users');
    // 直接使用数据，可能导致错误
    this.container.innerHTML = data.users.map(user => `<div>${user.name}</div>`).join('');
}
```

### 3. 错误处理
```javascript
// ✅ 好的做法
async loadData() {
    try {
        await super.loadData();
    } catch (error) {
        console.error(`Failed to load data for ${this.containerId}:`, error);
        this.renderError(error);
    }
}

// ❌ 不好的做法
async loadData() {
    await super.loadData(); // 没有错误处理
}
```

### 4. 性能优化
```javascript
// ✅ 好的做法
constructor(containerId, dataManager, config) {
    super(containerId, dataManager, config);
    
    // 使用防抖避免频繁更新
    this.debouncedRender = this.debounce(this.render.bind(this), 300);
}

onDataUpdate(dataType, data) {
    this.debouncedRender();
}

// ❌ 不好的做法
onDataUpdate(dataType, data) {
    this.render(); // 每次数据更新都立即渲染
}
```

---

## 进阶用法

### 自定义数据转换
```javascript
class AdvancedComponent extends BaseComponent {
    onDataUpdate(dataType, data) {
        // 自定义数据转换
        if (dataType === 'raw_data') {
            const transformedData = this.transformData(data);
            this.renderWithTransformedData(transformedData);
        }
    }
    
    transformData(rawData) {
        return {
            items: rawData.items.map(item => ({
                ...item,
                displayName: `${item.name} (${item.status})`
            }))
        };
    }
}
```

### 组件间通信
```javascript
class ParentComponent extends BaseComponent {
    onChildEvent(eventData) {
        // 处理子组件事件
        console.log('Child event received:', eventData);
    }
    
    bindEvents() {
        // 监听自定义事件
        window.addEventListener('child-component-event', (event) => {
            this.onChildEvent(event.detail);
        });
    }
}

class ChildComponent extends BaseComponent {
    triggerParentEvent(data) {
        // 触发自定义事件
        const event = new CustomEvent('child-component-event', {
            detail: data
        });
        window.dispatchEvent(event);
    }
}
```

### 动态组件加载
```javascript
async function loadComponentDynamically(type, containerId) {
    // 动态导入组件
    const module = await import(`./components/${type}-component.js`);
    const ComponentClass = module.default;
    
    // 注册并初始化组件
    const appManager = getAppManager();
    appManager.registerComponent(type, containerId, ComponentClass);
    
    return appManager.getComponent(type);
}
```

---

## 总结

通过这个快速上手指南，你应该能够：

1. ✅ 在5分钟内创建第一个组件
2. ✅ 理解基本的开发模式
3. ✅ 迁移现有代码到新架构
4. ✅ 解决常见问题
5. ✅ 遵循最佳实践

**下一步：** 查看[前端统一架构使用规范.md](./前端统一架构使用规范.md)了解更详细的开发规范。

**需要帮助？** 在浏览器控制台中使用`debug`对象进行调试，或查看故障排除指南。
