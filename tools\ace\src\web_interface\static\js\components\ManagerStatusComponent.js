/**
 * 项目经理状态 (ManagerStatusComponent)
 *
 * 功能：
 * - 显示当前项目经理的角色。
 * - 显示当前正在执行的任务。
 * - 显示工作状态和当前处理的文档。
 * - 展示任务进度和已处理时间。
 * - 支持任务驱动工作流的审查状态显示。
 * - 显示工作区状态和审查进度。
 *
 * 数据依赖：
 * - manager_status: 包含项目经理的角色、工作状态等。
 * - task_status: 包含当前任务、进度和时间。
 * - admission_review: 包含审查任务的状态和进度。
 * - workspace_status: 包含工作区状态信息。
 */
class ManagerStatusComponent extends BaseComponent {
    constructor(containerId, dataManager) {
        super(containerId, dataManager);
        this.workspaceStatus = 'idle';
        this.reviewStatus = null;
        this.currentTaskId = null;
    }

    getDataTypes() {
        return ['manager_status', 'task_status', 'admission_review', 'workspace_status'];
    }

    render() {
        const managerStatus = this.getData('manager_status') || {};
        const taskStatus = this.getData('task_status') || {};
        const admissionReviewData = this.getData('admission_review') || {};
        const workspaceStatusData = this.getData('workspace_status') || {};

        // 更新内部状态
        this.workspaceStatus = workspaceStatusData.status || this.workspaceStatus;
        this.reviewStatus = admissionReviewData.status || this.reviewStatus;
        this.currentTaskId = workspaceStatusData.task_id || this.currentTaskId;

        const workStatusColor = (managerStatus.work_status || 'ACTIVE').toLowerCase() === 'active' ? '#4CAF50' : '#FF9800';

        // 根据工作区状态调整显示内容
        const currentTask = this.getDisplayTask(taskStatus, admissionReviewData);
        const currentDoc = this.getDisplayDocument(managerStatus, admissionReviewData);

        this.container.innerHTML = `
            <div class="area-title">项目经理状态 (PM Status)</div>
            <div class="area-content">
                <!-- 工作区状态 -->
                ${this.renderWorkspaceStatus()}

                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>当前项目经理:</strong> <span id="current-pm-role">${managerStatus.current_pm_role || '首席架构师AI'}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator ${this.getTaskStatusIndicator()}"></span>
                    <strong>当前任务:</strong> <span id="current-task">${currentTask}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active"></span>
                    <strong>工作状态:</strong> <span id="work-status" style="color: ${workStatusColor};">${this.getDisplayWorkStatus(managerStatus)}</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-converging"></span>
                    <strong>处理文档:</strong> <span id="current-doc">${currentDoc}</span>
                </div>

                <!-- 任务进度 -->
                <div style="margin-top: 1rem;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.2rem;">
                        <span><strong>任务进度:</strong></span>
                        <span style="color: #2196F3; font-weight: bold;">${taskStatus.progress || '75'}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill info" style="width: ${taskStatus.progress || '75'}%;"></div>
                    </div>
                </div>

                <div class="status-item" style="margin-top: 0.5rem;">
                    <strong>已处理时间:</strong> <span id="processing-time" style="color: #FF9800;">${taskStatus.processing_time || '2分30秒'}</span>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 监听任务驱动工作流相关事件
        document.addEventListener('pm_v2_data_update', (event) => {
            const { eventType, data } = event.detail;
            switch (eventType) {
                case 'workspace_created':
                    this.handleWorkspaceCreated(data);
                    break;
                case 'admission_review_started':
                    this.handleReviewStarted(data);
                    break;
                case 'review_stage_progress':
                    this.handleReviewProgress(data);
                    break;
                case 'review_completed':
                    this.handleReviewCompleted(data);
                    break;
            }
        });
    }

    // 工作区状态渲染
    renderWorkspaceStatus() {
        const statusColors = {
            'idle': '#666',
            'initializing': '#FF9800',
            'ready': '#4CAF50',
            'reviewing': '#2196F3',
            'completed': '#4CAF50',
            'failed': '#F44336'
        };

        const statusTexts = {
            'idle': '等待初始化',
            'initializing': '正在初始化...',
            'ready': '工作区就绪',
            'reviewing': '审查进行中',
            'completed': '审查完成',
            'failed': '审查失败'
        };

        return `
            <div class="status-item" style="margin-bottom: 0.8rem; padding: 0.5rem; background: #2A2D30; border-radius: 4px; border: 1px solid #3C3F41;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <div style="width: 6px; height: 6px; border-radius: 50%; background: ${statusColors[this.workspaceStatus]};"></div>
                    <strong>工作区状态:</strong>
                    <span style="color: ${statusColors[this.workspaceStatus]};">${statusTexts[this.workspaceStatus]}</span>
                    ${this.currentTaskId ? `<span style="color: #888; font-size: 0.8rem; margin-left: 0.5rem;">ID: ${this.currentTaskId.substring(0, 8)}...</span>` : ''}
                </div>
            </div>
        `;
    }

    // 获取任务状态指示器
    getTaskStatusIndicator() {
        switch (this.workspaceStatus) {
            case 'reviewing':
                return 'status-thinking';
            case 'ready':
                return 'status-active';
            case 'completed':
                return 'status-converging';
            case 'failed':
                return 'status-error';
            default:
                return 'status-pending';
        }
    }

    // 获取显示任务
    getDisplayTask(taskStatus, admissionReviewData) {
        if (this.workspaceStatus === 'reviewing' && admissionReviewData.current_stage) {
            const stageNames = {
                'stage_1': '阶段一：全局契约生成',
                'stage_2': '阶段二：引用式契约生成',
                'stage_3': '阶段三：契约履行与审计',
                'stage_4': '阶段四：整体性审计'
            };
            return stageNames[admissionReviewData.current_stage] || '审查任务进行中';
        }
        return taskStatus.current_task || '从01号文档中识别设计意图并进行实体分类';
    }

    // 获取显示文档
    getDisplayDocument(managerStatus, admissionReviewData) {
        if (this.workspaceStatus === 'reviewing' && admissionReviewData.current_document) {
            return admissionReviewData.current_document;
        }
        return managerStatus.current_doc || '1-总体架构设计-V2.md';
    }

    // 获取显示工作状态
    getDisplayWorkStatus(managerStatus) {
        switch (this.workspaceStatus) {
            case 'reviewing':
                return 'REVIEWING';
            case 'ready':
                return 'READY';
            case 'completed':
                return 'COMPLETED';
            case 'failed':
                return 'FAILED';
            default:
                return managerStatus.work_status || 'ACTIVE';
        }
    }

    // 事件处理方法
    handleWorkspaceCreated(data) {
        this.workspaceStatus = 'ready';
        this.currentTaskId = data.task_id;
        this.render();
    }

    handleReviewStarted(data) {
        this.workspaceStatus = 'reviewing';
        this.reviewStatus = data.status;
        this.render();
    }

    handleReviewProgress(data) {
        // 更新审查进度，重新渲染
        this.render();
    }

    handleReviewCompleted(data) {
        this.workspaceStatus = 'completed';
        this.reviewStatus = 'completed';
        this.render();
    }
}