/**
 * 人类输入控制区 (HumanInputComponent)
 * 
 * 功能：
 * - 显示从其他组件传递过来的详细信息。
 * - 包含用户输入文本框。
 * - 提供开始、暂停、停止和扫描等核心控制按钮。
 * 
 * 数据依赖：
 * - control_status: 控制按钮的启用/禁用状态。
 */
class HumanInputComponent extends BaseComponent {
    constructor(containerId, dataManager) {
        super(containerId, dataManager);
        this.currentTaskId = null;
        // 移除currentProjectPath属性，保持纯任务驱动架构
        this.workspaceStatus = 'idle'; // idle, initializing, ready, reviewing
    }

    getDataTypes() {
        return ['control_status', 'workspace_info'];
    }

    render() {
        const controlStatus = this.getData('control_status') || { start: false, pause: true, stop: true, scan: true };

        this.container.innerHTML = `
            <div class="area-content" style="margin-top: 1rem; height: calc(100% - 1rem); display: flex; flex-direction: column;">
                <!-- 项目路径输入区 -->
                <div id="project-path-area" style="margin-bottom: 0.8rem;">
                    <div style="display: flex; gap: 0.3rem; align-items: center;">
                        <input id="project-path-input" type="text" placeholder="输入项目路径..."
                               style="flex: 1; padding: 0.3rem; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; font-size: 0.8rem;" />
                        <button id="init-workspace-btn" style="padding: 0.3rem 0.8rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">初始化</button>
                    </div>
                    <div id="workspace-status" style="margin-top: 0.3rem; font-size: 0.7rem; color: #888;">
                        状态: ${this.workspaceStatus === 'idle' ? '等待输入项目路径' : this.workspaceStatus}
                        ${this.currentTaskId ? ` | 任务ID: ${this.currentTaskId}` : ''}
                    </div>
                </div>

                <!-- 详细区 -->
                <div id="detail-area" class="vscode-scrollbar" style="flex: 1; background: #2A2D30; border: 1px solid #3C3F41; border-radius: 4px; margin-bottom: 0.8rem; position: relative; overflow-y: auto;">
                    <div id="detail-title" style="position: absolute; top: 4px; left: 8px; background: #3C3F41; color: #BBBBBB; padding: 2px 6px; border-radius: 3px; font-size: 0.7rem; z-index: 10;">详细</div>
                    <div id="detail-content" style="padding: 0.5rem; font-family: monospace; font-size: 0.8rem; color: #BBBBBB; line-height: 1.3; height: 100%; display: flex; flex-direction: column;">
                        <div id="detail-placeholder" style="color: #666; text-align: center; flex: 1; display: flex; align-items: center; justify-content: center;">点击左侧算法思维日志查看详细内容</div>
                    </div>
                </div>

                <!-- 输入框 -->
                <textarea id="user-input" placeholder="基于详细区内容提问或自由输入..." style="width: 100%; height: 60px; background: #2A2D30; color: #BBBBBB; border: 1px solid #3C3F41; border-radius: 4px; padding: 0.5rem; resize: vertical; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; font-size: 0.9rem; margin-bottom: 0.8rem;"></textarea>

                <!-- 控制按钮 -->
                <div class="control-buttons" style="display: flex; gap: 0.3rem;">
                    <button id="start-btn" ${controlStatus.start ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #4CAF50; border: 1px solid #4CAF50; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">开始</button>
                    <button id="pause-btn" ${controlStatus.pause ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #FF9800; border: 1px solid #FF9800; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">暂停</button>
                    <button id="stop-btn" ${controlStatus.stop ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #F44336; border: 1px solid #F44336; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">停止</button>
                    <button id="scan-btn" ${this.workspaceStatus === 'ready' ? '' : 'disabled'} style="flex: 1; padding: 0.3rem; background: transparent; color: #2196F3; border: 1px solid #2196F3; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">🔍 审查</button>
                </div>
            </div>
        `;
    }

    bindEvents() {
        this.container.querySelector('#start-btn')?.addEventListener('click', () => this.startMeeting());
        this.container.querySelector('#pause-btn')?.addEventListener('click', () => this.pauseMeeting());
        this.container.querySelector('#stop-btn')?.addEventListener('click', () => this.stopMeeting());
        this.container.querySelector('#scan-btn')?.addEventListener('click', () => this.handleScanningClick());
        this.container.querySelector('#init-workspace-btn')?.addEventListener('click', () => this.initializeWorkspace());

        // 监听工作区相关事件
        document.addEventListener('pm_v2_data_update', (event) => {
            const { eventType, data } = event.detail;
            if (eventType === 'workspace_created') {
                this.handleWorkspaceCreated(data);
            } else if (eventType === 'admission_review_started') {
                this.handleReviewStarted(data);
            }
        });
    }

    startMeeting() {
        console.log("开始会议");
        this.triggerEvent('control-meeting', { action: 'start' });
    }

    pauseMeeting() {
        console.log("暂停会议");
        this.triggerEvent('control-meeting', { action: 'pause' });
    }

    stopMeeting() {
        console.log("停止会议");
        this.triggerEvent('control-meeting', { action: 'stop' });
    }

    handleScanningClick() {
        if (this.workspaceStatus === 'ready' && this.currentTaskId) {
            console.log("启动审查任务:", this.currentTaskId);
            this.workspaceStatus = 'reviewing';
            this.updateWorkspaceStatus();
            this.dataManager.startReviewTask(this.currentTaskId);
        } else {
            console.warn("工作区未就绪或缺少任务ID");
        }
    }

    initializeWorkspace() {
        const pathInput = this.container.querySelector('#project-path-input');
        const projectPath = pathInput.value.trim();

        if (!projectPath) {
            alert('请输入项目路径');
            return;
        }

        console.log("初始化工作区:", projectPath);
        // 不保存projectPath，保持任务驱动架构纯净性
        // 工作区创建后会通过workspace_created事件获取task_id
        this.workspaceStatus = 'initializing';
        this.updateWorkspaceStatus();

        this.dataManager.initializeWorkspace(projectPath);
    }

    handleWorkspaceCreated(data) {
        console.log("工作区创建完成:", data);
        this.currentTaskId = data.task_id;
        this.workspaceStatus = 'ready';
        this.updateWorkspaceStatus();
    }

    handleReviewStarted(data) {
        console.log("审查任务启动:", data);
        this.workspaceStatus = 'reviewing';
        this.updateWorkspaceStatus();
    }

    updateWorkspaceStatus() {
        const statusElement = this.container.querySelector('#workspace-status');
        if (statusElement) {
            let statusText = '';
            switch (this.workspaceStatus) {
                case 'idle':
                    statusText = '等待输入项目路径';
                    break;
                case 'initializing':
                    statusText = '正在初始化工作区...';
                    break;
                case 'ready':
                    statusText = '工作区就绪，可以启动审查';
                    break;
                case 'reviewing':
                    statusText = '审查进行中...';
                    break;
            }

            statusElement.innerHTML = `状态: ${statusText}${this.currentTaskId ? ` | 任务ID: ${this.currentTaskId}` : ''}`;
        }

        // 更新扫描按钮状态
        const scanBtn = this.container.querySelector('#scan-btn');
        if (scanBtn) {
            scanBtn.disabled = this.workspaceStatus !== 'ready';
            scanBtn.textContent = this.workspaceStatus === 'reviewing' ? '🔄 审查中' : '🔍 审查';
        }
    }

    /**
     * 公共方法：更新详细区内容
     * @param {string} content - 要显示的HTML内容
     */
    updateDetail(content) {
        const detailContent = this.container.querySelector('#detail-content');
        const detailPlaceholder = this.container.querySelector('#detail-placeholder');
        const detailTitle = this.container.querySelector('#detail-title');
        
        if (detailContent) {
            // 隐藏"详细"标题
            if (detailTitle) {
                detailTitle.style.display = 'none';
            }
            
            // 更新内容
            detailContent.innerHTML = content;
            if(detailPlaceholder) detailPlaceholder.style.display = 'none';
        }
    }
}